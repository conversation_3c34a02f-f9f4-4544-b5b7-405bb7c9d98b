package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/otiai10/gosseract/v2"
)

// OCR校正器（复用之前的代码）
type OCRCorrector struct {
	charCorrections map[string]string
	organDictionary []string
}

func NewOCRCorrector() *OCRCorrector {
	return &OCRCorrector{
		charCorrections: map[string]string{
			// 基于分析的常见误识别
			"胆囊": "腹部",
			"胆部": "腹部",
			"腹囊": "腹部",
			"服部": "腹部",
			"复部": "腹部",
			"O.000": "0.000",
			"0.00O": "0.000",
			"0.0O0": "0.000",
		},
		organDictionary: []string{
			"腹部第1腰椎水平截面",
			"腹部第2腰椎水平截面",
			"腹部第3腰椎水平截面",
			"腹部第4腰椎水平截面",
			"腹部第5腰椎水平截面",
			"心脏", "肝脏", "肾脏", "脾脏", "肺脏",
			"胃", "胆囊", "胰腺", "十二指肠",
		},
	}
}

func (oc *OCRCorrector) CorrectOCRResult(ocrText string) (string, []string) {
	correctedText := ocrText
	corrections := []string{}
	
	// 字符级别校正
	for wrong, correct := range oc.charCorrections {
		if strings.Contains(correctedText, wrong) {
			oldText := correctedText
			correctedText = strings.ReplaceAll(correctedText, wrong, correct)
			if oldText != correctedText {
				correction := fmt.Sprintf("字符校正: %s → %s", wrong, correct)
				corrections = append(corrections, correction)
			}
		}
	}
	
	return correctedText, corrections
}

// 实际图片OCR测试
func testRealImageOCR() {
	fmt.Println("=== 实际图片OCR识别测试 ===")
	
	imagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`
	fmt.Printf("测试图片: %s\n", imagePath)
	
	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 图片文件不存在: %s\n", imagePath)
		return
	}
	
	fmt.Printf("✅ 图片文件存在\n\n")
	
	// 1. 裁剪左上角1/4区域
	fmt.Println("=== 步骤1: 裁剪左上角1/4区域 ===")
	croppedPath, err := cropTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("❌ 裁剪失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 裁剪完成: %s\n", croppedPath)
	
	// 2. OCR识别
	fmt.Println("\n=== 步骤2: OCR识别 ===")
	rawText, err := performOCR(croppedPath)
	if err != nil {
		fmt.Printf("❌ OCR识别失败: %v\n", err)
		return
	}
	fmt.Printf("原始OCR结果:\n%s\n", rawText)
	
	// 3. OCR结果校正
	fmt.Println("=== 步骤3: OCR结果校正 ===")
	corrector := NewOCRCorrector()
	correctedText, corrections := corrector.CorrectOCRResult(rawText)
	
	if len(corrections) > 0 {
		fmt.Printf("应用的校正:\n")
		for _, correction := range corrections {
			fmt.Printf("  ✓ %s\n", correction)
		}
		fmt.Printf("\n校正后结果:\n%s\n", correctedText)
	} else {
		fmt.Printf("✅ 无需校正，OCR结果准确\n")
	}
	
	// 4. 提取器官名称
	fmt.Println("=== 步骤4: 提取器官名称 ===")
	organName := extractOrganFromText(correctedText)
	fmt.Printf("提取的器官名称: [%s]\n", organName)
	
	// 5. 结果分析
	fmt.Println("\n=== 步骤5: 结果分析 ===")
	analyzeResult(rawText, correctedText, organName, corrections)
}

// 裁剪图片左上角1/4区域
func cropTopLeftQuarter(imagePath string) (string, error) {
	// 打开原图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}
	defer file.Close()
	
	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图片失败: %v", err)
	}
	
	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	
	fmt.Printf("原图尺寸: %dx%d\n", width, height)
	
	// 计算左上角1/4区域
	quarterWidth := width / 4
	quarterHeight := height / 4
	
	fmt.Printf("裁剪区域: %dx%d (左上角1/4)\n", quarterWidth, quarterHeight)
	
	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))
	
	// 复制像素
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}
	
	// 保存裁剪后的图片
	outputPath := "pic/test_cropped_quarter.png"
	os.MkdirAll("pic", 0755)
	
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()
	
	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", fmt.Errorf("保存裁剪图片失败: %v", err)
	}
	
	// 转换为绝对路径
	absPath, _ := filepath.Abs(outputPath)
	return absPath, nil
}

// 执行OCR识别
func performOCR(imagePath string) (string, error) {
	// 创建OCR客户端
	client := gosseract.NewClient()
	defer client.Close()
	
	// 设置语言和参数
	client.SetLanguage("chi_sim+eng")
	client.SetPageSegMode(gosseract.PSM_AUTO)
	
	// 设置图片
	err := client.SetImage(imagePath)
	if err != nil {
		return "", fmt.Errorf("设置图片失败: %v", err)
	}
	
	// 执行OCR
	text, err := client.Text()
	if err != nil {
		return "", fmt.Errorf("OCR识别失败: %v", err)
	}
	
	return text, nil
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	text = strings.TrimSpace(text)
	
	// 查找包含 "0.000" 的部分
	if strings.Contains(text, "0.000") {
		return extractOrganFromLine(text)
	}
	
	// 按行处理
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		// 查找包含 "0.000" 的行
		if strings.Contains(line, "0.000") {
			return extractOrganFromLine(line)
		}
		
		// 也检查包含类似数字的行
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, line); matched {
			result := extractOrganFromLine(line)
			if result != "" {
				return result
			}
		}
	}
	
	return "未知器官"
}

// 从单行中提取器官名称
func extractOrganFromLine(line string) string {
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)
	
	// 模式1: 数字后直接跟器官名称（有空格）
	pattern1 := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		if organName != "" {
			return organName
		}
	}
	
	// 模式1B: 数字后直接跟器官名称（无空格）
	pattern1B := regexp.MustCompile(`[0-9]\.[0-9]{3}([^\s\d].*)`)
	if matches := pattern1B.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		if organName != "" {
			return organName
		}
	}
	
	return ""
}

// 清理器官名称
func cleanOrganName(organName string) string {
	// 移除常见的英文描述
	organName = regexp.MustCompile(`\s*[A-Z][A-Z\s\-]+$`).ReplaceAllString(organName, "")
	organName = strings.Trim(organName, "|")
	organName = strings.TrimSpace(organName)
	
	if matched, _ := regexp.MatchString(`^[A-Za-z0-9\s\-]+$`, organName); matched {
		return ""
	}
	
	return organName
}

// 分析结果
func analyzeResult(rawText, correctedText, organName string, corrections []string) {
	fmt.Println("📊 识别结果分析:")
	
	// 检查是否找到了0.000行
	hasZeroLine := strings.Contains(correctedText, "0.000")
	fmt.Printf("  ✓ 是否找到0.000行: %t\n", hasZeroLine)
	
	// 检查是否应用了校正
	hasCorrections := len(corrections) > 0
	fmt.Printf("  ✓ 是否应用了校正: %t\n", hasCorrections)
	
	// 检查器官名称提取结果
	organExtracted := organName != "未知器官" && organName != ""
	fmt.Printf("  ✓ 是否成功提取器官: %t\n", organExtracted)
	
	// 总体评估
	fmt.Println("\n🎯 总体评估:")
	if hasZeroLine && organExtracted {
		fmt.Println("  ✅ 识别成功！")
		if hasCorrections {
			fmt.Println("  ✅ 校正器发挥了作用")
		} else {
			fmt.Println("  ✅ 原始OCR结果就很准确")
		}
	} else {
		fmt.Println("  ❌ 识别失败")
		if !hasZeroLine {
			fmt.Println("  ❌ 未找到0.000标志行")
		}
		if !organExtracted {
			fmt.Println("  ❌ 未能提取器官名称")
		}
	}
	
	// 建议
	fmt.Println("\n💡 建议:")
	if !hasZeroLine {
		fmt.Println("  - 可能需要调整裁剪区域")
		fmt.Println("  - 可能需要图像预处理增强")
	}
	if hasZeroLine && !organExtracted {
		fmt.Println("  - 可能需要优化提取算法")
		fmt.Println("  - 可能需要添加更多校正规则")
	}
}

func main() {
	testRealImageOCR()
}
