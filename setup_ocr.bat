@echo off
echo ========================================
echo OCR功能设置和测试脚本
echo ========================================
echo.

echo 1. 检查Tesseract-OCR安装状态...
tesseract --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [错误] Tesseract-OCR未安装或未添加到PATH环境变量
    echo.
    echo 请按照以下步骤安装Tesseract-OCR：
    echo 1. 访问: https://github.com/UB-Mannheim/tesseract/wiki
    echo 2. 下载最新版本的Windows安装包
    echo 3. 安装时确保选择中文语言包 ^(chi_sim^)
    echo 4. 将安装目录添加到系统PATH环境变量
    echo    默认安装路径: C:\Program Files\Tesseract-OCR
    echo 5. 重启命令提示符或重启电脑
    echo.
    echo 安装完成后，请重新运行此脚本进行测试
    pause
    exit /b 1
) else (
    echo [成功] Tesseract-OCR已安装
    tesseract --version
    echo.
)

echo 2. 检查中文语言包...
tesseract --list-langs | findstr chi_sim >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [警告] 中文简体语言包 ^(chi_sim^) 未安装
    echo 请重新安装Tesseract-OCR并确保选择中文语言包
    echo.
) else (
    echo [成功] 中文简体语言包已安装
)

echo 3. 测试OCR功能...
if exist "ocr_tests\test_ocr.go" (
    echo 运行OCR测试程序...
    cd ocr_tests
    go run test_ocr.go
    cd ..
) else (
    echo [警告] OCR测试程序不存在
)

echo.
echo 4. 构建包含OCR功能的应用...
echo 是否要重新构建应用以启用OCR功能？ (Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    echo 开始构建...
    call build.bat
) else (
    echo 跳过构建
)

echo.
echo ========================================
echo OCR设置完成
echo ========================================
pause
