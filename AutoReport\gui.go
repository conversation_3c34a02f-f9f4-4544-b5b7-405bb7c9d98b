package main

import (
	"bytes"
	"log"
	"net/url"

	"encoding/json"
	"fmt"
	"image/color"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	// "github.com/boombuler/barcode"
	// "github.com/boombuler/barcode/qr"
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
	"github.com/kbinani/screenshot"
	"github.com/skip2/go-qrcode"
)

// 全局配置变量
var Config = appConfig{}

// GUI应用程序结构

// GUI 独立配置读取函数
func loadConfigForGUI() (appConfig, error) {
	configPath := filepath.Join("config", "app_config.json")
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		return appConfig{}, fmt.Errorf("读取配置文件失败: %v", err)
	}
	var cfg appConfig
	if err := json.Unmarshal(configFile, &cfg); err != nil {
		return appConfig{}, fmt.Errorf("解析配置文件失败: %v", err)
	}
	return cfg, nil
}

type HealthAppGUI struct {
	app       fyne.App
	window    fyne.Window
	appConfig appConfig

	// 主界面容器
	mainContainer *fyne.Container
	tabs          *container.AppTabs

	// 网点信息
	siteIDEntry    *widget.Entry
	siteNameEntry  *widget.Entry
	siteTypeEntry  *widget.Entry
	parentOrgEntry *widget.Entry
	provinceEntry  *widget.Entry
	cityEntry      *widget.Entry
	districtEntry  *widget.Entry
	addressEntry   *widget.Entry
	managerEntry   *widget.Entry
	phoneEntry     *widget.Entry

	// 截图控制
	screenshotModeGroup *widget.RadioGroup
	screenshotButton    *widget.Button
	uploadButton        *widget.Button
	statusLabel         *widget.Label
	previewImage        *widget.Label // 将来替换为图像组件

	// 磁感设备信息
	macAddressLabel *widget.Label

	// 检测挂号信息
	registrationNumberDisplay *fyne.Container // 修改：使用容器来动态更新挂号码的显示
	registrationHintLabel     *widget.Label
	currentRegistrationNumber int // 当天的检测挂号序号

	// 当日受检者名单
	patientList []struct {
		Name             string // 姓名
		RegistrationCode string // 挂号码
		FullCode         string // 完整挂号码
		RegisterTime     string // 挂号时间
	}

	// 当日受检者列表控件
	patientListWidget *widget.List

	// 调用扣子API推送挂号信息
	callCozeRegistrationWorkflow func(fullNumber, shortNumber string)
}

// GenerateCustomAppQRCode 生成微信小程序的二维码。
// 二维码内容包含网点ID和MAC地址作为小程序路径的查询参数，
// 并从配置中读取小程序的AppID和目标页面。
func GenerateCustomAppQRCode(cfg appConfig) ([]byte, error) {
	log.Printf("GenerateCustomAppQRCode received AppID: %s, TargetPage: %s", cfg.MpAppInfo.AppID, cfg.MpAppInfo.TargetPage) // 打印传入的 AppID 和 TargetPage
	miniAppID := cfg.MpAppInfo.AppID
	miniAppPagePath := cfg.MpAppInfo.TargetPage

	if miniAppID == "" {
		return nil, fmt.Errorf("Mini Program AppID is not configured")
	}
	if miniAppPagePath == "" {
		return nil, fmt.Errorf("Mini Program target page is not configured")
	}
	siteID := cfg.SiteInfo.SiteID
	if siteID == "" {
		siteID = "UNKNOWN_SITE_ID" // 如果未设置，则使用回退值
	}

	macAddress := cfg.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN_MAC_ADDRESS" // 如果未设置，则使用回退值
	}

	// 为小程序路径构建查询参数
	queryParams := url.Values{}
	queryParams.Add("siteID", siteID)
	queryParams.Add("macAddress", macAddress)

	// 构建二维码的完整数据字符串（微信小程序URL Scheme）
	// 格式: weixin://dl/business/?appid=APPID&path=PAGEMPATH&query=QUERYSTRING
	// 注意: miniAppPagePath 应该是没有前导斜杠的页面路径，例如 "pages/p_scan/p_scan"
	// scheme中的query部分应包含小程序页面本身的URL编码查询参数。

	// 正确的 weixin://dl/business/ scheme 构建方式:
	// path 参数应该是小程序的页面路径，例如 "pages/index/index"
	// query 参数应该是该页面的查询字符串，例如 "foo=bar&baz=qux"
	// scheme 本身的 path 和 query 值如果包含特殊字符可能需要编码，
	// 但通常 appid、path 和 query 是 scheme URL 的独立组成部分。

	// path 参数应该是实际的小程序页面路径，不需要URL编码。
	// query 参数应该是页面的URL编码查询字符串。
	// 示例: pages/p_scan/p_scan?siteID=SITE1&macAddress=MAC1
	// 因此，path=pages/p_scan/p_scan, query=siteID%3DSITE1%26macAddress%3DMAC1

	dataString := fmt.Sprintf("weixin://dl/business/?appid=%s&path=%s&query=%s",
		miniAppID,            // 从配置中读取
		miniAppPagePath,      // 从配置中读取
		queryParams.Encode(), // 页面的查询字符串，由 Encode() 方法进行URL编码
	)

	// 使用go-qrcode生成二维码图片
	qrCode, err := qrcode.Encode(dataString, qrcode.Medium, 256)
	if err != nil {
		return nil, fmt.Errorf("生成二维码失败: %w", err)
	}

	// 生成文件名：siteID+macAddress(去掉冒号)
	fileName := fmt.Sprintf("%s_%s.png", siteID, strings.ReplaceAll(macAddress, ":", ""))

	// 保存二维码图片到本地
	err = os.WriteFile(filepath.Join("pic", fileName), qrCode, 0644)
	if err != nil {
		return nil, fmt.Errorf("保存二维码文件失败: %w", err)
	}

	return qrCode, nil

}

// 创建新的GUI应用程序
func NewHealthAppGUI() *HealthAppGUI {
	gui := &HealthAppGUI{}
	gui.app = app.New()

	// 应用自定义主题
	gui.app.Settings().SetTheme(newCustomTheme()) // 定义在custom_theme.go中

	gui.window = gui.app.NewWindow("健康检测系统")

	// 获取屏幕尺寸（使用系统API获取真实屏幕分辨率）
	var screenW, screenH int
	user32 := syscall.NewLazyDLL("user32.dll")
	getSystemMetrics := user32.NewProc("GetSystemMetrics")

	// SM_CXSCREEN = 0, SM_CYSCREEN = 1
	screenW_ret, _, _ := getSystemMetrics.Call(0)
	screenH_ret, _, _ := getSystemMetrics.Call(1)
	screenW, screenH = int(screenW_ret), int(screenH_ret)

	// 计算窗口尺寸（固定为屏幕宽度的1/6的60%，高度的1/3）
	originalWidth := int(float32(screenW) / 6)
	width := int(float32(originalWidth) * 0.6) // 减少到原来的60%
	height := int(float32(screenH) / 3)

	// 设置固定窗口大小
	gui.window.Resize(fyne.NewSize(float32(width), float32(height)))
	gui.window.SetFixedSize(true)

	// 创建主界面（不再需要展开/收缩功能）
	gui.createUI(width, width)

	// 设置窗口位置和大小
	// 注意：在Fyne中，UI操作应该在主线程中进行
	gui.window.Resize(fyne.NewSize(float32(width), float32(height)))
	gui.window.SetFixedSize(true)

	// 设置窗口位置到屏幕左上角(0,0)
	// 这需要在窗口显示后通过其他方式实现，因为Fyne不直接支持设置窗口位置

	// 注意：不要在此处初始化 gui.appConfig，需在 main.go 中赋值，否则会导致配置丢失
	return gui
}

// 创建用户界面
func (gui *HealthAppGUI) createUI(narrowWidth, expandedWidth int) {

	// 创建选项卡容器（纵向排列）
	gui.tabs = container.NewAppTabs(
		container.NewTabItemWithIcon("受检人", theme.AccountIcon(), gui.createUserInfoTab()),
		container.NewTabItemWithIcon("网点", theme.HomeIcon(), gui.createSiteInfoTab()),
		container.NewTabItemWithIcon("辅助分析", theme.MediaPhotoIcon(), gui.createScreenshotTab()),
		container.NewTabItemWithIcon("设置", theme.SettingsIcon(), gui.createSettingsTab()),
	)

	// 设置选项卡位置为顶部（横向排列）
	gui.tabs.SetTabLocation(container.TabLocationTop)

	// 创建主容器
	gui.mainContainer = container.NewStack(gui.tabs)

	// 设置主窗口内容
	gui.window.SetContent(gui.mainContainer)
}

// 创建磁感设备选项卡
func (gui *HealthAppGUI) createMagneticDeviceTab() fyne.CanvasObject {
	// 创建标题
	title := widget.NewLabelWithStyle("人体磁感分析仪", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 获取MAC地址
	macAddress := Config.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "未检测到MAC地址"
	}

	// 创建MAC信息标签
	gui.macAddressLabel = widget.NewLabel("MAC信息：" + macAddress)
	gui.macAddressLabel.Alignment = fyne.TextAlignLeading

	// 创建复制按钮
	copyButton := widget.NewButton("复制", func() {
		// 复制MAC地址到剪贴板
		gui.copyToClipboard(macAddress)

		// 显示提示，使用自定义对话框
		gui.showCustomInformationDialog("成功", "MAC信息已复制")
	})

	// 创建MAC信息和复制按钮的水平容器
	macContainer := container.NewHBox(
		gui.macAddressLabel,
		layout.NewSpacer(), // 添加空白区域，使复制按钮靠右对齐
		copyButton,
	)

	// 创建容器
	contentContainer := container.NewStack()

	// 创建布局
	formLayout := container.NewVBox(
		title,
		container.NewPadded(macContainer),
	)

	// 添加滚动容器
	scrollContainer := container.NewScroll(formLayout)

	contentContainer.Objects = []fyne.CanvasObject{scrollContainer}

	return contentContainer
}

// 创建用户信息选项卡
func (gui *HealthAppGUI) createUserInfoTab() fyne.CanvasObject {

	// 创建一个容器来显示表单内容
	contentContainer := container.NewStack()

	// 创建表单布局 - 所有标签宽度统一为四个汉字宽度
	patientListLabel := widget.NewLabel("当日受检者列表") // 正好六个汉字
	patientListLabel.Alignment = fyne.TextAlignCenter
	patientListLabel.TextStyle = fyne.TextStyle{Bold: true}

	// 创建固定宽度的空白标签，用于控制文本框宽度 - 所有文本框宽度统一
	textFieldSpacer := canvas.NewRectangle(color.Transparent)
	textFieldSpacer.SetMinSize(fyne.NewSize(400, 1)) // 统一设置所有文本框宽度

	// 创建当日受检者列表
	// 初始化列表数据
	gui.patientList = []struct {
		Name             string
		RegistrationCode string
		FullCode         string
		RegisterTime     string
	}{}

	// 创建列表控件
	gui.patientListWidget = widget.NewList(
		// 列表长度函数
		func() int {
			return len(gui.patientList)
		},
		// 创建列表项模板
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("姓名："),
				widget.NewLabel(""),
				widget.NewLabel("挂号码："),
				widget.NewLabel(""),
				widget.NewLabel("时间："),
				widget.NewLabel(""),
			)
		},
		// 更新列表项
		func(id widget.ListItemID, item fyne.CanvasObject) {
			if id < len(gui.patientList) {
				patient := gui.patientList[id]
				hbox := item.(*fyne.Container)

				// 更新姓名
				nameLabel := hbox.Objects[1].(*widget.Label)
				nameLabel.SetText(patient.Name)

				// 更新挂号码
				codeLabel := hbox.Objects[3].(*widget.Label)
				codeLabel.SetText(patient.RegistrationCode)

				// 更新时间
				timeLabel := hbox.Objects[5].(*widget.Label)
				timeLabel.SetText(patient.RegisterTime)
			}
		},
	)

	// 创建一个固定高度的容器来包含列表
	listContainer := container.NewVBox(gui.patientListWidget)
	// 使用一个空白矩形来设置最小高度
	spacer := canvas.NewRectangle(color.Transparent)
	spacer.SetMinSize(fyne.NewSize(400, 200))
	listContainer.Add(spacer)

	// 创建检测挂号按钮 - 蓝色背景，白色文字，限制宽度
	registerButton := widget.NewButtonWithIcon("生成挂号码", theme.ContentAddIcon(), func() {
		// 生成检测挂号码
		shortNumber, fullNumber := gui.generateRegistrationNumber()

		// 更新挂号码显示 - 使用 canvas.Text 实现自定义样式
		if gui.registrationNumberDisplay == nil {
			gui.registrationNumberDisplay = container.NewStack()
			gui.registrationNumberDisplay.Objects = []fyne.CanvasObject{canvas.NewText("", color.NRGBA{R: 255, G: 0, B: 0, A: 255})}
		}
		newText := "当前挂号码: " + shortNumber
		styledText := canvas.NewText(newText, color.NRGBA{R: 255, G: 0, B: 0, A: 255}) // 红色
		styledText.TextSize = theme.TextSize() * 1.5                                   // 放大
		styledText.TextStyle = fyne.TextStyle{Bold: true}                              // 加粗
		styledText.Alignment = fyne.TextAlignCenter

		gui.registrationNumberDisplay.Objects = []fyne.CanvasObject{styledText}
		gui.registrationNumberDisplay.Refresh()

		// 调用扣子API推送挂号信息
		if gui.callCozeRegistrationWorkflow != nil {
			gui.callCozeRegistrationWorkflow(fullNumber, shortNumber)
		} else {
			fmt.Println("警告: 挂号推送功能 (callCozeRegistrationWorkflow) 未配置或未初始化。")
			// 如果需要用户提示，可以取消下面这行的注释
			// gui.showCustomInformationDialog("提醒", "挂号推送功能当前不可用")
		}
	})

	// 设置按钮颜色 - 蓝色背景
	registerButton.Importance = widget.HighImportance // 高重要性按钮使用主题的主色（通常是蓝色）

	// 按钮将在后面的按钮容器中使用

	// 创建获取受检者信息按钮 - 蓝色背景，白色文字，减半长度
	startButton := widget.NewButtonWithIcon("获取受检人信息", theme.MediaPlayIcon(), func() {
		// 模拟调用扣子API获取用户信息
		gui.fetchUserInfoFromCoze()
	})

	// 设置按钮颜色 - 蓝色背景
	startButton.Importance = widget.HighImportance // 高重要性按钮使用主题的主色（通常是蓝色）

	// 按钮将在后面的按钮容器中使用

	// 创建提示信息标签 - 分成两行显示，更加简洁
	apiHintLabel1 := widget.NewLabel("受检者完成小程序登记后，")
	apiHintLabel1.Alignment = fyne.TextAlignCenter

	apiHintLabel2 := widget.NewLabel("请点击获取信息按钮")
	apiHintLabel2.Alignment = fyne.TextAlignCenter

	// 创建一个自定义容器，用于限制按钮宽度为窗体宽度的一半
	// 创建一个固定宽度的矩形，用于控制按钮宽度
	registerButtonRect := canvas.NewRectangle(color.RGBA{0, 0, 255, 255}) // 蓝色背景
	registerButtonRect.SetMinSize(fyne.NewSize(200, 40))                  // 设置固定宽度和高度

	startButtonRect := canvas.NewRectangle(color.RGBA{0, 0, 255, 255}) // 蓝色背景
	startButtonRect.SetMinSize(fyne.NewSize(200, 40))                  // 设置固定宽度和高度

	// 创建自定义容器，将按钮放在矩形上方
	registerButtonOverlay := container.NewStack(registerButtonRect, registerButton)
	startButtonOverlay := container.NewStack(startButtonRect, startButton)

	// 创建按钮容器 - 垂直排列
	buttonContainer := container.NewVBox(
		container.NewCenter(registerButtonOverlay), // 居中显示固定宽度的按钮
		apiHintLabel1,
		apiHintLabel2,
		container.NewCenter(startButtonOverlay), // 居中显示固定宽度的按钮
	)

	// 初始化二维码显示区域
	cfg, err := loadConfigForGUI()
	if err != nil {
		log.Printf("读取配置失败: %v", err)
	}
	qrBytes, err := GenerateCustomAppQRCode(cfg)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		qrBytes = []byte{} // 使用空字节作为回退
	}
	qrImg := canvas.NewImageFromReader(bytes.NewReader(qrBytes), "qrcode.png")
	qrImg.FillMode = canvas.ImageFillOriginal
	gui.registrationNumberDisplay = container.NewStack(qrImg)

	// 初始化提示信息标签
	gui.registrationHintLabel = widget.NewLabel("请使用微信扫健康检测码")
	gui.registrationHintLabel.Alignment = fyne.TextAlignCenter

	// 创建挂号区域容器
	registrationContainer := container.NewVBox(
		gui.registrationNumberDisplay, // 修改：使用新的Display容器
		gui.registrationHintLabel,
		container.NewPadded(buttonContainer), // 添加垂直排列的按钮容器
	)

	// 创建用户信息区域容器 - 使用列表控件显示当日受检者列表
	userInfoContainer := container.NewVBox(
		patientListLabel,
		container.NewPadded(listContainer),
	)

	// 创建表单布局 - 使用VBox垂直排列各个水平容器
	formLayout := container.NewVBox(
		widget.NewLabelWithStyle("受检者信息管理", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		container.NewPadded(registrationContainer), // 添加挂号区域容器
		widget.NewSeparator(),                  // 添加分隔线
		container.NewPadded(userInfoContainer), // 添加用户信息区域容器
	)

	// 直接使用表单布局，不使用滚动容器
	contentContainer.Objects = []fyne.CanvasObject{formLayout}

	// 不再需要监听展开按钮点击事件
	// 因为我们已经不使用展开/收缩功能了

	// 不加载上一个受检人信息

	return contentContainer
}

// 创建网点信息选项卡
func (gui *HealthAppGUI) createSiteInfoTab() fyne.CanvasObject {
	// 初始化组件
	gui.siteIDEntry = widget.NewEntry()
	gui.siteNameEntry = widget.NewEntry()
	gui.siteTypeEntry = widget.NewEntry()
	gui.parentOrgEntry = widget.NewEntry()
	gui.provinceEntry = widget.NewEntry()
	gui.cityEntry = widget.NewEntry()
	gui.districtEntry = widget.NewEntry()
	gui.addressEntry = widget.NewEntry()
	gui.managerEntry = widget.NewEntry()
	gui.phoneEntry = widget.NewEntry()

	// 保存按钮
	saveButton := widget.NewButtonWithIcon("保存", theme.DocumentSaveIcon(), func() {
		gui.saveSiteInfo()
	})

	// 创建一个容器来显示表单内容
	contentContainer := container.NewStack()

	// 设置简单的占位符文本
	gui.siteIDEntry.SetPlaceHolder("请输入网点ID")
	gui.siteNameEntry.SetPlaceHolder("请输入网点名称")
	gui.siteTypeEntry.SetPlaceHolder("请输入网点类型")
	gui.parentOrgEntry.SetPlaceHolder("请输入所属机构")
	gui.provinceEntry.SetPlaceHolder("请输入省份")
	gui.cityEntry.SetPlaceHolder("请输入城市")
	gui.districtEntry.SetPlaceHolder("请输入区县")
	gui.addressEntry.SetPlaceHolder("请输入详细地址")
	gui.managerEntry.SetPlaceHolder("请输入负责人")
	gui.phoneEntry.SetPlaceHolder("请输入联系电话")

	// 使用长占位符文本来增加文本框宽度
	longPlaceholder := strings.Repeat("　", 50) // 使用50个全角空格

	// 临时设置长占位符
	gui.siteIDEntry.SetText(longPlaceholder)
	gui.siteNameEntry.SetText(longPlaceholder)
	gui.siteTypeEntry.SetText(longPlaceholder)
	gui.parentOrgEntry.SetText(longPlaceholder)
	gui.provinceEntry.SetText(longPlaceholder)
	gui.cityEntry.SetText(longPlaceholder)
	gui.districtEntry.SetText(longPlaceholder)
	gui.addressEntry.SetText(longPlaceholder)
	gui.managerEntry.SetText(longPlaceholder)
	gui.phoneEntry.SetText(longPlaceholder)

	// 在UI渲染后清除占位符并加载网点信息
	defer func() {
		// 先清除占位符
		gui.siteIDEntry.SetText("")
		gui.siteNameEntry.SetText("")
		gui.siteTypeEntry.SetText("")
		gui.parentOrgEntry.SetText("")
		gui.provinceEntry.SetText("")
		gui.cityEntry.SetText("")
		gui.districtEntry.SetText("")
		gui.addressEntry.SetText("")
		gui.managerEntry.SetText("")
		gui.phoneEntry.SetText("")

		// 然后加载网点信息
		gui.loadSiteInfo()
	}()

	// 创建标题
	title := widget.NewLabelWithStyle("网点信息管理", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 创建每个字段的标签（左对齐）- 所有标签宽度统一为四个汉字宽度
	idLabel := widget.NewLabel("网点ID　") // 使用全角空格补齐到四个汉字宽度
	idLabel.Alignment = fyne.TextAlignLeading

	nameLabel := widget.NewLabel("网点名称") // 正好四个汉字
	nameLabel.Alignment = fyne.TextAlignLeading

	typeLabel := widget.NewLabel("网点类型") // 正好四个汉字
	typeLabel.Alignment = fyne.TextAlignLeading

	orgLabel := widget.NewLabel("所属机构") // 正好四个汉字
	orgLabel.Alignment = fyne.TextAlignLeading

	provinceLabel := widget.NewLabel("省份　　") // 使用全角空格补齐到四个汉字宽度
	provinceLabel.Alignment = fyne.TextAlignLeading

	cityLabel := widget.NewLabel("城市　　") // 使用全角空格补齐到四个汉字宽度
	cityLabel.Alignment = fyne.TextAlignLeading

	districtLabel := widget.NewLabel("区县　　") // 使用全角空格补齐到四个汉字宽度
	districtLabel.Alignment = fyne.TextAlignLeading

	addressLabel := widget.NewLabel("详细地址") // 正好四个汉字
	addressLabel.Alignment = fyne.TextAlignLeading

	managerLabel := widget.NewLabel("负责人　") // 使用全角空格补齐到四个汉字宽度
	managerLabel.Alignment = fyne.TextAlignLeading

	phoneLabel := widget.NewLabel("联系电话") // 正好四个汉字
	phoneLabel.Alignment = fyne.TextAlignLeading

	// 创建固定宽度的空白标签，用于控制文本框宽度 - 所有文本框宽度统一
	textFieldSpacer := canvas.NewRectangle(color.Transparent)
	textFieldSpacer.SetMinSize(fyne.NewSize(400, 1)) // 统一设置所有文本框宽度

	// 创建每个字段的水平容器（标签和输入框横向排列，输入框填满剩余空间）
	// 使用Form布局，让文本框更靠近标签
	idContainer := container.NewHBox(
		idLabel,
		gui.siteIDEntry,
	)
	idContainer.Layout = layout.NewFormLayout() // 使用FormLayout让文本框靠近标签

	nameContainer := container.NewHBox(
		nameLabel,
		gui.siteNameEntry,
	)
	nameContainer.Layout = layout.NewFormLayout()

	typeContainer := container.NewHBox(
		typeLabel,
		gui.siteTypeEntry,
	)
	typeContainer.Layout = layout.NewFormLayout()

	orgContainer := container.NewHBox(
		orgLabel,
		gui.parentOrgEntry,
	)
	orgContainer.Layout = layout.NewFormLayout()

	provinceContainer := container.NewHBox(
		provinceLabel,
		gui.provinceEntry,
	)
	provinceContainer.Layout = layout.NewFormLayout()

	cityContainer := container.NewHBox(
		cityLabel,
		gui.cityEntry,
	)
	cityContainer.Layout = layout.NewFormLayout()

	districtContainer := container.NewHBox(
		districtLabel,
		gui.districtEntry,
	)
	districtContainer.Layout = layout.NewFormLayout()

	addressContainer := container.NewHBox(
		addressLabel,
		gui.addressEntry,
	)
	addressContainer.Layout = layout.NewFormLayout()

	managerContainer := container.NewHBox(
		managerLabel,
		gui.managerEntry,
	)
	managerContainer.Layout = layout.NewFormLayout()

	phoneContainer := container.NewHBox(
		phoneLabel,
		gui.phoneEntry,
	)
	phoneContainer.Layout = layout.NewFormLayout()

	// 创建表单布局 - 使用VBox垂直排列各个水平容器
	formLayout := container.NewVBox(
		title,
		idContainer,
		nameContainer,
		typeContainer,
		orgContainer,
		provinceContainer,
		cityContainer,
		districtContainer,
		addressContainer,
		managerContainer,
		phoneContainer,
		layout.NewSpacer(), // 添加空白区域，增加与上面控件的距离
		layout.NewSpacer(), // 再添加一个空白区域，进一步增加距离
		container.NewPadded(saveButton),
	)

	// 直接使用表单布局，不使用滚动容器
	contentContainer.Objects = []fyne.CanvasObject{formLayout}

	// 不再需要监听展开按钮点击事件
	// 因为我们已经不使用展开/收缩功能了

	// 网点信息将在defer函数中加载

	return contentContainer
}

// 创建截图控制选项卡
func (gui *HealthAppGUI) createScreenshotTab() fyne.CanvasObject {
	// 初始化组件
	options := []string{
		"A - 器官问题来源分析",
		"B - 生化平衡分析",
		"C - 病理形态学分析",
	}
	gui.screenshotModeGroup = widget.NewRadioGroup(options, func(selected string) {})
	gui.screenshotModeGroup.SetSelected(options[0])

	gui.screenshotButton = widget.NewButtonWithIcon("截图", theme.MediaPhotoIcon(), func() {
		gui.takeScreenshot()
	})

	gui.uploadButton = widget.NewButtonWithIcon("上传", theme.UploadIcon(), func() {
		gui.uploadLatestScreenshot()
	})

	gui.statusLabel = widget.NewLabel("就绪")

	// 预览区域（将来替换为实际图像）
	gui.previewImage = widget.NewLabel("截图预览区域")
	gui.previewImage.Alignment = fyne.TextAlignCenter

	// 创建一个容器来显示控制面板
	contentContainer := container.NewStack()

	// 创建新的标题和说明标签
	aiAnalysisTitle := widget.NewLabelWithStyle("检测结果AI辅助分析", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	descriptionText := "说明：\n本辅助分析内容包括：A0 器官问题来源分析；B0 生化平衡分析；C0 病理形态学分析"
	descriptionLabel := widget.NewLabel(descriptionText)
	descriptionLabel.Wrapping = fyne.TextWrapWord // 自动换行

	// 创建控制面板布局
	title := widget.NewLabelWithStyle("截图控制", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	modeLabel := widget.NewLabel("选择截图模式:")
	modeLabel.Alignment = fyne.TextAlignLeading

	// 创建按钮容器
	controls := container.NewHBox(
		gui.screenshotButton,
		gui.uploadButton,
	)

	// 创建完整布局
	formLayout := container.NewVBox(
		aiAnalysisTitle,                       // AI分析标题放在最前
		container.NewPadded(descriptionLabel), // AI分析描述
		widget.NewSeparator(),                 // 分隔线
		title,                                 // "截图控制" 标题
		container.NewPadded(modeLabel),
		container.NewPadded(gui.screenshotModeGroup),
		layout.NewSpacer(),
		container.NewPadded(controls),
		container.NewPadded(gui.statusLabel),
		container.NewPadded(container.NewCenter(gui.previewImage)),
	)

	// 直接使用表单布局，不使用滚动容器
	contentContainer.Objects = []fyne.CanvasObject{formLayout}

	// 不再需要监听展开按钮点击事件
	// 因为我们已经不使用展开/收缩功能了

	return contentContainer
}

// 创建设置选项卡
func (gui *HealthAppGUI) createSettingsTab() fyne.CanvasObject {
	// 裁剪设置
	topPercentEntry := widget.NewEntry()
	topPercentEntry.SetText(fmt.Sprintf("%.3f", Config.CropSettings.TopPercent))

	bottomPercentEntry := widget.NewEntry()
	bottomPercentEntry.SetText(fmt.Sprintf("%.3f", Config.CropSettings.BottomPercent))

	leftPercentEntry := widget.NewEntry()
	leftPercentEntry.SetText(fmt.Sprintf("%.3f", Config.CropSettings.LeftPercent))

	rightPercentEntry := widget.NewEntry()
	rightPercentEntry.SetText(fmt.Sprintf("%.3f", Config.CropSettings.RightPercent))

	// 使用长占位符文本来增加文本框宽度
	longPlaceholder := strings.Repeat("　", 50) // 使用50个全角空格

	// 临时保存当前值
	topValue := topPercentEntry.Text
	bottomValue := bottomPercentEntry.Text
	leftValue := leftPercentEntry.Text
	rightValue := rightPercentEntry.Text

	// 临时设置长占位符
	topPercentEntry.SetText(longPlaceholder)
	bottomPercentEntry.SetText(longPlaceholder)
	leftPercentEntry.SetText(longPlaceholder)
	rightPercentEntry.SetText(longPlaceholder)

	// 在UI渲染后恢复原始值
	defer func() {
		topPercentEntry.SetText(topValue)
		bottomPercentEntry.SetText(bottomValue)
		leftPercentEntry.SetText(leftValue)
		rightPercentEntry.SetText(rightValue)
	}()

	// 保存设置按钮
	saveSettingsButton := widget.NewButtonWithIcon("保存", theme.DocumentSaveIcon(), func() {
		// 解析输入值
		top, _ := strconv.ParseFloat(topPercentEntry.Text, 64)
		bottom, _ := strconv.ParseFloat(bottomPercentEntry.Text, 64)
		left, _ := strconv.ParseFloat(leftPercentEntry.Text, 64)
		right, _ := strconv.ParseFloat(rightPercentEntry.Text, 64)

		// 更新配置
		Config.CropSettings.TopPercent = top
		Config.CropSettings.BottomPercent = bottom
		Config.CropSettings.LeftPercent = left
		Config.CropSettings.RightPercent = right

		// 保存配置
		if err := saveConfig(&Config); err != nil {
			dialog.ShowError(err, gui.window)
			return
		}

		gui.showCustomInformationDialog("成功", "设置已保存")
	})

	// 设备信息显示
	// 获取MAC地址
	macAddress := Config.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "未检测到MAC地址"
	}

	// 创建MAC信息标签
	gui.macAddressLabel = widget.NewLabel("MAC地址: " + macAddress)
	gui.macAddressLabel.Alignment = fyne.TextAlignLeading

	// 创建复制按钮
	copyButton := widget.NewButton("复制", func() {
		// 复制MAC地址到剪贴板
		gui.copyToClipboard(macAddress)

		// 显示提示，使用自定义对话框
		gui.showCustomInformationDialog("成功", "MAC信息已复制")
	})

	// 创建MAC信息和复制按钮的水平容器
	macContainer := container.NewHBox(
		gui.macAddressLabel,
		layout.NewSpacer(), // 添加空白区域，使复制按钮靠右对齐
		copyButton,
	)

	deviceNameLabel := widget.NewLabel("设备名称: " + Config.DeviceInfo.DeviceName)

	// 创建一个容器来显示设置
	contentContainer := container.NewStack()

	// 创建设置布局
	title := widget.NewLabelWithStyle("设置", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 创建AI分析标题和描述标签（在createScreenshotTab中也有类似定义）
	aiAnalysisTitle := widget.NewLabelWithStyle("检测结果AI辅助分析", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	descriptionText := "说明：\n本辅助分析内容包括：A0 器官问题来源分析；B0 生化平衡分析；C0 病理形态学分析"
	descriptionLabel := widget.NewLabel(descriptionText)
	descriptionLabel.Wrapping = fyne.TextWrapWord // 自动换行

	// 创建裁剪设置标签 - 所有标签宽度统一为四个汉字宽度
	topLabel := widget.NewLabel("顶部裁剪") // 正好四个汉字
	topLabel.Alignment = fyne.TextAlignLeading

	bottomLabel := widget.NewLabel("底部裁剪") // 正好四个汉字
	bottomLabel.Alignment = fyne.TextAlignLeading

	leftLabel := widget.NewLabel("左侧裁剪") // 正好四个汉字
	leftLabel.Alignment = fyne.TextAlignLeading

	rightLabel := widget.NewLabel("右侧裁剪") // 正好四个汉字
	rightLabel.Alignment = fyne.TextAlignLeading

	// 创建设备信息标题
	deviceInfoTitle := widget.NewLabelWithStyle("设备信息", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 创建固定宽度的空白标签，用于控制文本框宽度 - 所有文本框宽度统一
	textFieldSpacer := canvas.NewRectangle(color.Transparent)
	textFieldSpacer.SetMinSize(fyne.NewSize(400, 1)) // 统一设置所有文本框宽度

	// 创建每个字段的水平容器（标签和输入框横向排列，输入框填满剩余空间）
	// 使用Form布局，让文本框更靠近标签
	topContainer := container.NewHBox(
		topLabel,
		topPercentEntry,
	)
	topContainer.Layout = layout.NewFormLayout() // 使用FormLayout让文本框靠近标签

	bottomContainer := container.NewHBox(
		bottomLabel,
		bottomPercentEntry,
	)
	bottomContainer.Layout = layout.NewFormLayout()

	leftContainer := container.NewHBox(
		leftLabel,
		leftPercentEntry,
	)
	leftContainer.Layout = layout.NewFormLayout()

	rightContainer := container.NewHBox(
		rightLabel,
		rightPercentEntry,
	)
	rightContainer.Layout = layout.NewFormLayout()

	// 创建完整布局
	formLayout := container.NewVBox(
		aiAnalysisTitle,
		container.NewPadded(descriptionLabel),
		widget.NewSeparator(), // 添加分隔线
		title,
		topContainer,
		bottomContainer,
		leftContainer,
		rightContainer,
		container.NewPadded(saveSettingsButton),
		container.NewPadded(deviceInfoTitle),
		container.NewPadded(macContainer),
		container.NewPadded(deviceNameLabel),
	)

	// 直接使用表单布局，不使用滚动容器
	contentContainer.Objects = []fyne.CanvasObject{formLayout}

	// 不再需要监听展开按钮点击事件
	// 因为我们已经不使用展开/收缩功能了

	return contentContainer
}

// 计算年龄

// 加载用户信息

// 保存网点信息
func (gui *HealthAppGUI) saveSiteInfo() {
	// 更新配置
	Config.SiteInfo.SiteID = gui.siteIDEntry.Text
	Config.SiteInfo.SiteName = gui.siteNameEntry.Text
	Config.SiteInfo.SiteType = gui.siteTypeEntry.Text
	Config.SiteInfo.ParentOrg = gui.parentOrgEntry.Text
	Config.SiteInfo.Location.Province = gui.provinceEntry.Text
	Config.SiteInfo.Location.City = gui.cityEntry.Text
	Config.SiteInfo.Location.District = gui.districtEntry.Text
	Config.SiteInfo.Location.Address = gui.addressEntry.Text
	Config.SiteInfo.Contact.Manager = gui.managerEntry.Text
	Config.SiteInfo.Contact.Phone = gui.phoneEntry.Text

	// 保存配置
	if err := saveConfig(&Config); err != nil {
		dialog.ShowError(err, gui.window)
		return
	}

	gui.showCustomInformationDialog("成功", "网点信息已保存")
}

// 加载网点信息
func (gui *HealthAppGUI) loadSiteInfo() {
	gui.siteIDEntry.SetText(Config.SiteInfo.SiteID)
	gui.siteNameEntry.SetText(Config.SiteInfo.SiteName)
	gui.siteTypeEntry.SetText(Config.SiteInfo.SiteType)
	gui.parentOrgEntry.SetText(Config.SiteInfo.ParentOrg)
	gui.provinceEntry.SetText(Config.SiteInfo.Location.Province)
	gui.cityEntry.SetText(Config.SiteInfo.Location.City)
	gui.districtEntry.SetText(Config.SiteInfo.Location.District)
	gui.addressEntry.SetText(Config.SiteInfo.Location.Address)
	gui.managerEntry.SetText(Config.SiteInfo.Contact.Manager)
	gui.phoneEntry.SetText(Config.SiteInfo.Contact.Phone)
}

// 截图功能
func (gui *HealthAppGUI) takeScreenshot() {
	// 获取选择的模式
	selected := gui.screenshotModeGroup.Selected
	var mode string
	switch {
	case selected == "A - 器官问题来源分析":
		mode = "A"
	case selected == "B - 生化平衡分析":
		mode = "B"
	case selected == "C - 病理形态学分析":
		mode = "C"
	default:
		mode = "A"
	}

	gui.statusLabel.SetText("正在截图...")

	// 获取屏幕数量
	n := screenshot.NumActiveDisplays()
	if n <= 0 {
		gui.statusLabel.SetText("未检测到显示器")
		dialog.ShowError(fmt.Errorf("未检测到显示器"), gui.window)
		return
	}

	// 捕获主屏幕
	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		errMsg := fmt.Sprintf("截图失败: %v", err)
		gui.statusLabel.SetText(errMsg)
		dialog.ShowError(fmt.Errorf(errMsg), gui.window)
		return
	}

	// 应用裁剪设置
	cropBounds := calculateCropBounds(img.Bounds(),
		Config.CropSettings.TopPercent,
		Config.CropSettings.BottomPercent,
		Config.CropSettings.LeftPercent,
		Config.CropSettings.RightPercent)
	croppedImg := performCrop(img, cropBounds)

	// 生成文件名并保存图片
	filename := generateFilename(mode)
	filepath := filepath.Join("pic", filename+".png")

	// 保存裁剪后的图片
	if err := saveImage(croppedImg, filepath); err != nil {
		errMsg := fmt.Sprintf("保存图片失败: %v", err)
		gui.statusLabel.SetText(errMsg)
		dialog.ShowError(fmt.Errorf(errMsg), gui.window)
		return
	}

	// 获取文件大小
	fileInfo, err := os.Stat(filepath)
	if err != nil {
		gui.statusLabel.SetText("获取文件信息失败")
	} else {
		gui.statusLabel.SetText(fmt.Sprintf("截图已保存: %s (大小: %d 字节)", filepath, fileInfo.Size()))
	}

	// 记录操作日志
	LogOperation("GUI截图", currentUser.Name, Config.SiteInfo.SiteID)

	// TODO: 在将来的版本中，添加图像预览功能
}

// 上传最新截图
func (gui *HealthAppGUI) uploadLatestScreenshot() {
	gui.statusLabel.SetText("正在上传最新截图...")

	// 获取pic目录下最新的PNG文件
	files, err := filepath.Glob("pic/*.png")
	if err != nil {
		errMsg := fmt.Sprintf("获取文件列表失败: %v", err)
		gui.statusLabel.SetText(errMsg)
		dialog.ShowError(fmt.Errorf(errMsg), gui.window)
		return
	}

	if len(files) == 0 {
		gui.statusLabel.SetText("没有找到可上传的图片文件")
		gui.showCustomInformationDialog("提示", "没有找到可上传的图片文件")
		return
	}

	// 按修改时间排序，获取最新的文件
	latestFile := files[0]
	latestTime, err := os.Stat(latestFile)
	if err != nil {
		errMsg := fmt.Sprintf("获取文件信息失败: %v", err)
		gui.statusLabel.SetText(errMsg)
		dialog.ShowError(fmt.Errorf(errMsg), gui.window)
		return
	}

	for _, file := range files[1:] {
		fileInfo, err := os.Stat(file)
		if err != nil {
			continue
		}
		if fileInfo.ModTime().After(latestTime.ModTime()) {
			latestFile = file
			latestTime = fileInfo
		}
	}

	gui.statusLabel.SetText(fmt.Sprintf("正在上传: %s", latestFile))

	// 调用上传函数
	fileURL, err := uploadFile(latestFile)
	if err != nil {
		errMsg := fmt.Sprintf("上传失败: %v", err)
		gui.statusLabel.SetText(errMsg)
		dialog.ShowError(fmt.Errorf(errMsg), gui.window)
		return
	}

	successMsg := fmt.Sprintf("上传成功! 文件URL: %s", fileURL)
	gui.statusLabel.SetText(successMsg)

	// 显示成功对话框
	gui.showCustomInformationDialog("上传成功", "文件已成功上传并发送到分析服务")

	// 记录操作日志
	LogOperation("GUI上传", currentUser.Name, Config.SiteInfo.SiteID)
}

// 运行GUI应用程序
func (gui *HealthAppGUI) Run() {
	// 启动快捷键监听
	go gui.listenForHotkeys()

	// 显示窗口并运行应用
	gui.window.ShowAndRun()
}

// 显示自定义信息对话框（带有更宽的按钮）
func (gui *HealthAppGUI) showCustomInformationDialog(title, message string) {
	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel(message),
	)

	// 创建一个更宽的"好"按钮
	okButton := widget.NewButton("　　好　　", nil) // 使用全角空格使按钮更宽

	// 创建自定义对话框
	dlg := dialog.NewCustom(title, "", content, gui.window)

	// 设置按钮点击事件（关闭对话框）
	okButton.OnTapped = func() {
		dlg.Hide()
	}

	// 替换对话框的默认按钮
	dlg.SetButtons([]fyne.CanvasObject{okButton})

	// 显示对话框
	dlg.Show()
}

// 生成检测挂号码
func (gui *HealthAppGUI) generateRegistrationNumber() (string, string) {
	// 检查是否是新的一天，如果是则重置计数器
	now := time.Now()
	today := now.Format("20060102")

	// 从配置文件或其他持久化存储中读取上次的日期和计数
	// 这里简化处理，只在内存中维护
	if gui.currentRegistrationNumber == 0 || today != time.Now().Format("20060102") {
		gui.currentRegistrationNumber = 1
	} else {
		gui.currentRegistrationNumber++
	}

	// 生成3位序号，从001开始
	sequenceNumber := fmt.Sprintf("%03d", gui.currentRegistrationNumber)

	// 获取网点ID
	siteID := Config.SiteInfo.SiteID
	if siteID == "" {
		siteID = "UNKNOWN"
	}

	// 获取MAC地址
	macAddress := Config.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN-MAC"
	}

	// 获取当前时间戳
	timestamp := now.Format("20060102150405") // YYYYMMDDHHmmss

	// 组合完整挂号码（用于API调用）
	fullRegistrationNumber := fmt.Sprintf("%s-%s-%s-%s", siteID, macAddress, timestamp, sequenceNumber)

	// 返回显示用的简短挂号码和完整挂号码
	return sequenceNumber, fullRegistrationNumber
}

// 调用扣子API获取受检人用户信息
func (gui *HealthAppGUI) fetchUserInfoFromCoze() {
	// 获取当前显示的挂号码，而不是生成新的
	shortNumber := ""
	// 从当前显示的标签中提取挂号码
	if gui.registrationNumberDisplay != nil && len(gui.registrationNumberDisplay.Objects) > 0 {
		// 从canvas.Text对象中提取文本内容
		if textObj, ok := gui.registrationNumberDisplay.Objects[0].(*canvas.Text); ok {
			parts := strings.Split(textObj.Text, ": ")
			if len(parts) == 2 {
				shortNumber = parts[1]
			}
		}
	}

	// 如果没有从标签中获取到挂号码，则使用当前计数器值
	if shortNumber == "" {
		shortNumber = fmt.Sprintf("%03d", gui.currentRegistrationNumber)
	}

	// 重新构建完整挂号码
	siteID := Config.SiteInfo.SiteID
	if siteID == "" {
		siteID = "UNKNOWN"
	}

	macAddress := Config.DeviceInfo.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN-MAC"
	}

	timestamp := time.Now().Format("20060102150405")
	fullNumber := fmt.Sprintf("%s-%s-%s-%s", siteID, macAddress, timestamp, shortNumber)

	// 检查是否有挂号码
	if shortNumber == "" {
		gui.showCustomInformationDialog("提醒", "请先点击挂号按钮生成挂号码")
		return
	}

	// 检查API配置
	token := Config.APIKeys.Coze.Token
	workflowID := Config.APIKeys.Coze.WorkflowIDCheckUserInfo // 更正为正确的配置字段名 WorkflowIDUserInfo

	if token == "" || workflowID == "" {
		gui.showCustomInformationDialog("提醒", "未配置扣子API信息，请在设置中配置")
		return
	}

	// 获取网点ID
	// siteID = Config.SiteInfo.SiteID // siteID 已经在此函数前面部分获取和检查过了，这里无需重复获取
	// if siteID == "" {
	// 	siteID = "UNKNOWN"
	// }

	// 准备API请求参数
	requestBody := map[string]interface{}{
		"space_id":          Config.APIKeys.Coze.SpaceID,
		"workflow_id":       workflowID,
		"app_id":            Config.APIKeys.Coze.AppID,
		"is_async":          false,
		"stream":            false,
		"auto_save_history": true,
		"parameters": map[string]interface{}{
			"input_site_id":       siteID,
			"input_device_mac":    macAddress,
			"short_number":        shortNumber, // 短挂号码
			"registration_number": fullNumber,  // 长挂号码
		},
	}

	fmt.Printf("分割线: %s\n", "---------------------")
	fmt.Printf("获取受检人信息的requestBody为: %s\n", requestBody)

	// 调用扣子API
	respData, err := callCozeAPIWithResponse(requestBody)
	if err != nil {
		gui.showCustomInformationDialog("提醒", "获取受检人信息失败: "+err.Error())
		return
	}

	fmt.Printf("调用扣子API后的返回值为: %s\n", respData)

	// 解析响应数据获取用户姓名
	if dataStr, ok := respData["data"].(string); ok {
		var cozeResponseOutput struct { // Define a struct for the expected JSON structure within "data"
			Output string `json:"output"`
		}
		if err := json.Unmarshal([]byte(dataStr), &cozeResponseOutput); err == nil {
			name := cozeResponseOutput.Output
			if name != "" {
				// 将用户姓名添加到受检者列表
				gui.patientList = append(gui.patientList, struct {
					Name             string
					RegistrationCode string // 短挂号码
					FullCode         string // 完整挂号码
					RegisterTime     string
				}{
					Name:             name,
					RegistrationCode: shortNumber, // 使用 shortNumber
					FullCode:         fullNumber,  // 使用 fullNumber
					RegisterTime:     time.Now().Format("2006-01-02 15:04:05"),
				})
				// 刷新受检者列表显示
				if gui.patientListWidget != nil {
					gui.patientListWidget.Refresh()
				}
				gui.showCustomInformationDialog("成功", "成功获取受检人信息: "+name)
				return // 成功获取并处理后返回
			}
		} else {
			// 记录JSON解析提醒
			fmt.Printf("Error unmarshalling Coze API data payload: %v. Data string: %s\n", err, dataStr)
			gui.showCustomInformationDialog("提醒", "解析受检人信息失败 (JSON unmarshal error)")
			return
		}
	} else {
		// 如果 "data" 字段不存在或不是字符串
		fmt.Printf("Coze API response 'data' field is not a string or is missing. Response: %v\n", respData)
	}

	// 如果未能从API响应中提取姓名
	gui.showCustomInformationDialog("提醒", "目前没有受检人输入和确认当前挂号码")
}

// 复制文本到剪贴板
func (gui *HealthAppGUI) copyToClipboard(text string) {
	// 创建一个临时文件来存储MAC地址
	tempFile, err := os.CreateTemp("", "mac-*.txt")
	if err != nil {
		dialog.ShowError(fmt.Errorf("无法创建临时文件: %v", err), gui.window)
		return
	}
	defer os.Remove(tempFile.Name())

	// 写入MAC地址到临时文件
	if _, err := tempFile.WriteString(text); err != nil {
		dialog.ShowError(fmt.Errorf("无法写入临时文件: %v", err), gui.window)
		return
	}
	tempFile.Close()

	// 使用PowerShell命令将文件内容复制到剪贴板
	cmd := exec.Command("powershell", "-command", fmt.Sprintf("Get-Content '%s' | Set-Clipboard", tempFile.Name()))
	if err := cmd.Run(); err != nil {
		dialog.ShowError(fmt.Errorf("无法复制到剪贴板: %v", err), gui.window)
		return
	}
}

// 监听全局快捷键
func (gui *HealthAppGUI) listenForHotkeys() {
	// 添加冷却时间，防止重复触发
	lastAction := time.Now().Add(-1 * time.Second)
	cooldown := 500 * time.Millisecond

	for {
		// 检查组合键是否按下
		ctrlPressed := getAsyncKeyState(VK_CONTROL)
		shiftPressed := getAsyncKeyState(VK_SHIFT)
		aPressed := getAsyncKeyState(0x41) // A键
		bPressed := getAsyncKeyState(0x42) // B键
		cPressed := getAsyncKeyState(0x43) // C键
		uPressed := getAsyncKeyState(VK_U) // U键

		// 检查截图快捷键和模式选择
		if ctrlPressed && shiftPressed {
			// 检查是否在冷却时间内
			if time.Since(lastAction) < cooldown {
				time.Sleep(50 * time.Millisecond)
				continue
			}

			if aPressed || bPressed || cPressed {
				// 记录当前选择的模式
				var mode string
				if aPressed {
					mode = "A - 器官问题来源分析"
				} else if bPressed {
					mode = "B - 生化平衡分析"
				} else if cPressed {
					mode = "C - 病理形态学分析"
				}

				// 直接执行截图操作，不通过UI
				// 获取屏幕数量
				n := screenshot.NumActiveDisplays()
				if n <= 0 {
					continue
				}

				// 捕获主屏幕
				bounds := screenshot.GetDisplayBounds(0)
				img, err := screenshot.CaptureRect(bounds)
				if err != nil {
					continue
				}

				// 应用裁剪设置
				cropBounds := calculateCropBounds(img.Bounds(),
					Config.CropSettings.TopPercent,
					Config.CropSettings.BottomPercent,
					Config.CropSettings.LeftPercent,
					Config.CropSettings.RightPercent)
				croppedImg := performCrop(img, cropBounds)

				// 生成文件名并保存图片
				var modeChar string
				switch mode {
				case "A - 器官问题来源分析":
					modeChar = "A"
				case "B - 生化平衡分析":
					modeChar = "B"
				case "C - 病理形态学分析":
					modeChar = "C"
				default:
					modeChar = "A"
				}

				filename := generateFilename(modeChar)
				filepath := filepath.Join("pic", filename+".png")

				// 保存裁剪后的图片
				if err := saveImage(croppedImg, filepath); err != nil {
					continue
				}

				// 记录操作日志
				LogOperation("快捷键截图", currentUser.Name, Config.SiteInfo.SiteID)

				lastAction = time.Now()
			} else if uPressed {
				// 获取pic目录下最新的PNG文件
				files, err := filepath.Glob("pic/*.png")
				if err != nil || len(files) == 0 {
					continue
				}

				// 按修改时间排序，获取最新的文件
				latestFile := files[0]
				latestTime, err := os.Stat(latestFile)
				if err != nil {
					continue
				}

				for _, file := range files[1:] {
					fileInfo, err := os.Stat(file)
					if err != nil {
						continue
					}
					if fileInfo.ModTime().After(latestTime.ModTime()) {
						latestFile = file
						latestTime = fileInfo
					}
				}

				// 调用上传函数
				_, err = uploadFile(latestFile)
				if err != nil {
					continue
				}

				// 记录操作日志
				LogOperation("快捷键上传", currentUser.Name, Config.SiteInfo.SiteID)

				lastAction = time.Now()
			}
		}

		// 短暂延迟，避免CPU占用过高
		time.Sleep(50 * time.Millisecond)
	}
}
