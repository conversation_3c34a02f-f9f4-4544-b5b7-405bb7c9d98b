# 截图记录功能测试说明

## 🎯 功能概述

已实现的截图记录功能包括：

### 1. **数据库表结构**
- 创建了 `hc-detection-screenshot-records.schema.json`
- 支持10轮检测的完整记录
- 包含B02/C03两种分析类型的截图记录

### 2. **核心方法实现**

#### `CreateOrUpdateScreenshotRecord` (API服务)
```go
// 在 app/services/api_service.go 中
func (as *APIService) CreateOrUpdateScreenshotRecord(params models.ScreenshotRecordParams) error
```

#### `updateScreenshotRecord` (应用层)
```go
// 在 app.go 中
func (a *App) updateScreenshotRecord(mode, userName, organName, filename, cloudURL string, currentUser *models.Registration) error
```

### 3. **集成到主流程**
在 `ProcessScreenshotAndUpload` 方法中，截图上传后自动调用数据库更新：

```go
// 2.5. 更新截图记录到云数据库
err = a.updateScreenshotRecord(mode, userName, organName, filepath.Base(filePath), picURL, currentUser)
```

## 🔧 工作流程

### 当前实现的流程：
```
用户按快捷键 (Ctrl+Shift+B 或 Ctrl+Shift+C)
    ↓
1. OCR识别器官名称
    ↓
2. 生成优化文件名 (用户名_器官部位_检测报告号_轮次_列表类型.png)
    ↓
3. 截图并保存
    ↓
4. 上传到DCloud云存储
    ↓
5. 更新截图记录到云数据库 ⭐ (新增功能)
    ↓
6. 调用扣子工作流
```

### 数据库记录内容：
```json
{
  "session_id": "SES_张三_20240616",
  "user_name": "张三",
  "user_id": "实际用户ID",
  "site_id": "网点ID",
  "registration_id": "挂号记录ID",
  "current_round": 1,
  "total_rounds": 10,
  "screenshots": [
    {
      "round": 1,
      "analysis_type1": "B02",
      "analysis_type2": "C03",
      "detected_organ": "心脏",
      "B02_filename": "张三_心脏_RPT20240616001_R01_B02.png",
      "C03_filename": "张三_心脏_RPT20240616001_R01_C03.png",
      "B02_screenshot_cloud_url": "https://云存储URL",
      "C03_screenshot_cloud_url": "https://云存储URL",
      "screenshot_time": "2024-06-16 10:30:00",
      "ocr_success": true,
      "coze_workflow_called": true
    }
  ],
  "session_status": "IN_PROGRESS"
}
```

## 📊 状态管理逻辑

### 会话状态 (session_status)：
- **CREATED**: 第一轮第一次B02截图时创建
- **IN_PROGRESS**: 第1-9轮截图过程中
- **COMPLETED**: 第10轮C03截图完成时
- **CANCELLED**: 用户取消检测时

### 轮次管理：
- **current_round**: 当前轮次 (1-10)
- **total_rounds**: 总轮次 (固定10)
- 每轮包含B02和C03两次截图

## 🚀 测试步骤

### 1. **准备云函数**
需要在DCloud中创建云函数：
```
https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-screenshot-records/createOrUpdate
```

### 2. **测试B02截图**
1. 按 `Ctrl+Shift+B` (生化平衡分析)
2. 观察控制台输出：
   ```
   [DEBUG] 步骤2.5: 更新截图记录到云数据库...
   [DEBUG] 步骤2.5成功: 截图记录已更新到云数据库
   ```

### 3. **测试C03截图**
1. 按 `Ctrl+Shift+C` (病理形态学分析)
2. 观察控制台输出和数据库记录更新

### 4. **验证数据库记录**
检查云数据库中的 `hc-detection-screenshot-records` 表：
- 是否创建了新记录
- session_id 格式是否正确
- screenshots 数组是否包含截图信息
- 文件名和URL是否正确

## 🔍 调试信息

### 控制台输出示例：
```
[DEBUG] 步骤1.1成功: 识别到器官名称 - 心脏
[DEBUG] 步骤1.2成功: 截图已保存到 pic/张三_心脏_RPT20240616001_R01_B02.png
[DEBUG] 步骤2成功: 图片已上传，URL: https://...
[DEBUG] 步骤2.5: 更新截图记录到云数据库...
Screenshot Record Request JSON: {"user_name":"张三","analysis_type":"B02",...}
[DEBUG] 步骤2.5成功: 截图记录已更新到云数据库
```

### 错误处理：
如果数据库更新失败，会显示警告但不影响主流程：
```
[WARNING] 步骤2.5: 更新截图记录失败 - 云函数返回错误: xxx
```

## 📝 下一步优化

### 1. **轮次管理增强**
- 实现真实的轮次计数逻辑
- 根据数据库记录自动递增轮次

### 2. **状态管理完善**
- 实现会话状态的自动更新
- 第10轮完成时自动标记为COMPLETED

### 3. **错误恢复**
- 网络失败时的重试机制
- 离线模式下的本地缓存

### 4. **数据同步**
- 定期同步本地记录到云数据库
- 冲突解决机制

## ✅ 当前状态

- ✅ 数据库表结构设计完成
- ✅ API服务方法实现完成
- ✅ 主流程集成完成
- ✅ 错误处理机制完成
- ⏳ 需要创建对应的云函数
- ⏳ 需要实际测试验证

现在可以开始测试整个流程，验证截图记录功能是否正常工作！
