package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"os/exec"
	"regexp"
	"strings"
)

// 真实OCR识别
func performRealOCR(imagePath string) {
	fmt.Printf("=== 真实OCR识别 ===\n")
	fmt.Printf("目标图片: %s\n", imagePath)
	
	// 1. 先裁剪左上角1/4区域
	croppedPath, err := cropTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("❌ 裁剪失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 裁剪完成: %s\n", croppedPath)
	
	// 2. 尝试Tesseract OCR
	fmt.Printf("\n=== 尝试Tesseract OCR ===\n")
	tesseractResult := tryTesseractOCR(croppedPath)
	
	if tesseractResult != "" {
		fmt.Printf("✅ Tesseract识别成功!\n")
		fmt.Printf("原始OCR结果: [%s]\n", tesseractResult)
		
		// 分析识别结果
		analyzeOCRResult(tesseractResult)
	} else {
		fmt.Printf("❌ Tesseract识别失败，使用模拟结果进行对比\n")
		showExpectedResults()
	}
}

// 尝试Tesseract OCR
func tryTesseractOCR(imagePath string) string {
	// 检查tesseract是否可用
	if _, err := exec.LookPath("tesseract"); err != nil {
		fmt.Printf("❌ Tesseract未安装或不在PATH中\n")
		fmt.Printf("提示: 请安装Tesseract OCR引擎\n")
		fmt.Printf("下载地址: https://github.com/tesseract-ocr/tesseract\n")
		return ""
	}
	
	fmt.Printf("✅ 找到Tesseract引擎\n")
	
	// 执行tesseract命令，使用中文+英文语言包
	fmt.Printf("正在执行OCR识别...\n")
	cmd := exec.Command("tesseract", imagePath, "stdout", "-l", "chi_sim+eng", "--psm", "6")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("❌ Tesseract执行失败: %v\n", err)
		
		// 尝试只用英文
		fmt.Printf("尝试只使用英文语言包...\n")
		cmd = exec.Command("tesseract", imagePath, "stdout", "-l", "eng", "--psm", "6")
		output, err = cmd.Output()
		if err != nil {
			fmt.Printf("❌ 英文OCR也失败: %v\n", err)
			return ""
		}
	}
	
	text := strings.TrimSpace(string(output))
	return text
}

// 分析OCR识别结果
func analyzeOCRResult(ocrText string) {
	fmt.Printf("\n=== OCR结果分析 ===\n")
	fmt.Printf("识别的完整文本:\n")
	fmt.Printf("「%s」\n", ocrText)
	fmt.Printf("文本长度: %d 字符\n", len(ocrText))
	
	// 按行分析
	lines := strings.Split(ocrText, "\n")
	fmt.Printf("识别行数: %d\n", len(lines))
	
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		fmt.Printf("第%d行: [%s]\n", i+1, line)
	}
	
	// 查找目标模式
	fmt.Printf("\n=== 目标模式匹配 ===\n")
	
	// 查找包含数字的行
	foundTarget := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		// 检查是否包含0.000模式
		if strings.Contains(line, "0.000") || strings.Contains(line, "O.000") || strings.Contains(line, "0.00O") {
			fmt.Printf("✅ 找到目标行: [%s]\n", line)
			foundTarget = true
			
			// 提取器官名称
			organName := extractOrganFromText(line)
			fmt.Printf("提取的器官名称: [%s]\n", organName)
			
			// 应用校正
			correctedOrgan := applyOCRCorrection(organName)
			if correctedOrgan != organName {
				fmt.Printf("校正后的器官名称: [%s]\n", correctedOrgan)
			}
			
			// 与期望结果对比
			if correctedOrgan == "腹部第1腰椎水平截面" {
				fmt.Printf("🎯 识别结果正确!\n")
			} else {
				fmt.Printf("⚠️  识别结果与期望不符，期望: 腹部第1腰椎水平截面\n")
			}
		}
	}
	
	if !foundTarget {
		fmt.Printf("❌ 未找到包含 '0.000' 的目标行\n")
		fmt.Printf("可能的原因:\n")
		fmt.Printf("1. OCR未能识别数字部分\n")
		fmt.Printf("2. 图片质量不足\n")
		fmt.Printf("3. 文字区域不在左上角1/4范围内\n")
	}
	
	// 显示期望结果用于对比
	fmt.Printf("\n=== 期望结果对比 ===\n")
	fmt.Printf("期望识别内容: 0.000 腹部第1腰椎水平截面\n")
	fmt.Printf("实际识别内容: %s\n", ocrText)
	
	// 计算相似度
	similarity := calculateSimilarity(ocrText, "0.000 腹部第1腰椎水平截面")
	fmt.Printf("相似度: %.1f%%\n", similarity)
}

// 显示期望结果
func showExpectedResults() {
	fmt.Printf("\n=== 期望识别结果 ===\n")
	fmt.Printf("基于图片内容，期望的OCR识别结果应该是:\n")
	fmt.Printf("「0.000 腹部第1腰椎水平截面」\n")
	fmt.Printf("\n可能的误识别变体:\n")
	
	variants := []string{
		"0.000 胆囊第1腰椎水平截面",  // 腹→胆 误识别
		"0.000 胆部第1腰椎水平截面",  // 囊→部 误识别
		"O.000 腹部第1腰椎水平截面",  // 0→O 误识别
		"0.00O 腹部第1腰椎水平截面",  // 0→O 误识别
	}
	
	for i, variant := range variants {
		fmt.Printf("%d. %s\n", i+1, variant)
		organName := extractOrganFromText(variant)
		corrected := applyOCRCorrection(organName)
		fmt.Printf("   提取: [%s] → 校正: [%s]\n", organName, corrected)
	}
}

// 裁剪左上角1/4区域
func cropTopLeftQuarter(imagePath string) (string, error) {
	file, err := os.Open(imagePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	
	img, _, err := image.Decode(file)
	if err != nil {
		return "", err
	}
	
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	
	quarterWidth := width / 4
	quarterHeight := height / 4
	
	fmt.Printf("原图尺寸: %dx%d\n", width, height)
	fmt.Printf("裁剪区域: %dx%d (左上角1/4)\n", quarterWidth, quarterHeight)
	
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))
	
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}
	
	outputPath := "pic/real_ocr_cropped.png"
	os.MkdirAll("pic", 0755)
	
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outputFile.Close()
	
	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", err
	}
	
	return outputPath, nil
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	if !strings.Contains(text, "0.000") && !strings.Contains(text, "O.000") && !strings.Contains(text, "0.00O") {
		return "未知器官"
	}
	
	text = strings.TrimSpace(text)
	text = strings.ReplaceAll(text, "O.000", "0.000")
	text = strings.ReplaceAll(text, "0.00O", "0.000")
	text = strings.ReplaceAll(text, "0.0O0", "0.000")
	
	pattern := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	matches := pattern.FindStringSubmatch(text)
	
	if len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		return organName
	}
	
	return "未知器官"
}

// 应用OCR校正规则
func applyOCRCorrection(organName string) string {
	corrections := map[string]string{
		"胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"服部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"复部第1腰椎水平截面": "腹部第1腰椎水平截面",
	}
	
	if corrected, exists := corrections[organName]; exists {
		fmt.Printf("   🔧 校正应用: %s → %s\n", organName, corrected)
		return corrected
	}
	
	return organName
}

// 计算字符串相似度
func calculateSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 100.0
	}
	
	// 简单的字符匹配相似度
	s1Runes := []rune(s1)
	s2Runes := []rune(s2)
	
	matches := 0
	maxLen := len(s1Runes)
	if len(s2Runes) > maxLen {
		maxLen = len(s2Runes)
	}
	
	minLen := len(s1Runes)
	if len(s2Runes) < minLen {
		minLen = len(s2Runes)
	}
	
	for i := 0; i < minLen; i++ {
		if s1Runes[i] == s2Runes[i] {
			matches++
		}
	}
	
	if maxLen == 0 {
		return 0.0
	}
	
	return float64(matches) / float64(maxLen) * 100.0
}

func main() {
	fmt.Println("=== 真实OCR识别程序 ===")
	
	imagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`
	
	// 检查文件
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 图片文件不存在\n")
			return
		}
		imagePath = relativePath
	}
	
	fmt.Printf("✅ 找到图片: %s\n", imagePath)
	
	// 显示文件信息
	if info, err := os.Stat(imagePath); err == nil {
		fmt.Printf("文件大小: %.2f KB\n", float64(info.Size())/1024)
		fmt.Printf("修改时间: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
	}
	
	// 开始OCR识别
	performRealOCR(imagePath)
}
