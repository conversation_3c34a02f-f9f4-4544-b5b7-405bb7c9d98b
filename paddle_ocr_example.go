package main

import (
	"fmt"
	"log"
	"os"
)

// PaddleOCR示例 - 需要安装PaddleOCR-Go
// go get github.com/LKKlein/gocv-paddleocr

/*
使用PaddleOCR的优势：
1. 对中文识别效果极佳
2. 专门针对中文优化
3. 支持文本检测+识别
4. 模型较小，速度快
5. 百度维护，更新频繁

安装步骤：
1. 安装PaddleOCR Python版本
   pip install paddlepaddle paddleocr

2. 安装Go绑定
   go get github.com/LKKlein/gocv-paddleocr

3. 或者使用HTTP API方式调用
*/

// 使用PaddleOCR识别图片
func usePaddleOCR(imagePath string) {
	fmt.Println("=== PaddleOCR识别示例 ===")
	
	// 方法1: 直接调用PaddleOCR Python API
	result := callPaddleOCRAPI(imagePath)
	fmt.Printf("PaddleOCR结果: %s\n", result)
	
	// 方法2: 使用HTTP服务方式
	// 可以启动PaddleOCR服务，然后通过HTTP调用
	fmt.Println("建议使用PaddleOCR HTTP服务方式")
}

// 调用PaddleOCR API
func callPaddleOCRAPI(imagePath string) string {
	// 这里需要实际的PaddleOCR绑定
	// 示例代码，需要根据实际库进行调整
	return "PaddleOCR识别结果示例"
}

func main() {
	imagePath := "pic/医生或健康专家-B02-OCR-20250616_221124.png"
	
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		log.Printf("图片文件不存在: %s", imagePath)
		return
	}
	
	usePaddleOCR(imagePath)
}
