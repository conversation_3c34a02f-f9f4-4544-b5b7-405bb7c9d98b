#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

def test_easyocr():
    print("=== EasyOCR 简单测试 ===")
    
    try:
        print("1. 导入EasyOCR...")
        import easyocr
        print("✅ EasyOCR导入成功")
        
        print("2. 初始化EasyOCR Reader...")
        # 使用CPU模式，避免GPU相关问题
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("✅ EasyOCR Reader初始化成功")
        
        # 检查图片文件
        image_path = sys.argv[1] if len(sys.argv) > 1 else "pic/医生或健康专家-B02-OCR-20250616_221124.png"
        print(f"3. 检查图片文件: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return
        
        print("✅ 图片文件存在")
        
        print("4. 开始OCR识别...")
        result = reader.readtext(image_path)
        print("✅ OCR识别完成")
        
        print(f"5. 识别结果 (共{len(result)}个文本块):")
        for i, (bbox, text, confidence) in enumerate(result):
            print(f"  文本块 {i+1}:")
            print(f"    内容: [{text}]")
            print(f"    置信度: {confidence:.2f}")
            print(f"    位置: {bbox}")
            print()
        
        # 查找目标内容
        print("6. 查找目标内容 (包含0.000的文本):")
        found_target = False
        for bbox, text, confidence in result:
            if "0.000" in text or "O.000" in text or "0.00O" in text:
                print(f"  🎯 找到目标: [{text}] (置信度: {confidence:.2f})")
                found_target = True
        
        if not found_target:
            print("  ❌ 未找到包含0.000的目标文本")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_easyocr()
