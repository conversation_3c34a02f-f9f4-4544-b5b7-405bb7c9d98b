// 检测结果截图记录表 - MVP版本
{
	"bsonType": "object",
	"required": ["user_id", "user_name", "site_id"],
	"permission": {
		"read": true,
		"create": true,
		"update": true,
		"delete": true
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"session_id": {
			"bsonType": "string",
			"description": "检测会话ID，格式：SES_用户名_日期，如：SES_张三_20240616"
		},
		"user_id": {
			"bsonType": "string",
			"description": "受检人userID，参考`uni-id-users`",
			"foreignKey": "uni-id-users._id",
			"defaultValue": {
				"$env": "uid"
			}
		},
		"user_name": {
			"bsonType": "string",
			"description": "受检人姓名"
		},
		"site_id": {
			"bsonType": "string",
			"description": "网点ID"
		},
		"device_no": {
			"bsonType": "string",
			"description": "设备MAC地址"
		},
		"registration_id": {
			"bsonType": "string",
			"description": "关联的挂号记录ID，参考hc-onsite-registration表",
			"foreignKey": "hc-onsite-registration._id"
		},
		"report_number": {
			"bsonType": "string",
			"description": "检测报告号，格式：RPT20240616001"
		},
		"current_round": {
			"bsonType": "int",
			"description": "当前轮次，1-10",
			"defaultValue": 1
		},
		"total_rounds": {
			"bsonType": "int",
			"description": "计划检测轮次总数",
			"defaultValue": 10
		},
		"screenshots": {
			"bsonType": "array",
			"description": "截图记录数组",
			"items": {
				"bsonType": "object",
				"properties": {
					"round": {
						"bsonType": "int",
						"description": "轮次编号，1-10"
					},
					"detected_organ": {
						"bsonType": "string",
						"description": "OCR识别的器官名称"
					},
					"analysis_type": {
						"bsonType": "array",
						"description": "分析类型：A01/B02/C03",
						"items": {
							"bsonType": "string"
						}
					},
					"A01_filename": {
						"bsonType": "string",
						"description": "A01截图文件名"
					},
					"B02_filename": {
						"bsonType": "string",
						"description": "B02截图文件名"
					},
					"C03_filename": {
						"bsonType": "string",
						"description": "C03截图文件名"
					},
					"A01_screenshot_cloud_url": {
						"bsonType": "string",
						"description": "A01截图云存储URL"
					},
					"B02_screenshot_cloud_url": {
						"bsonType": "string",
						"description": "B02截图云存储URL"
					},
					"C03_screenshot_cloud_url": {
						"bsonType": "string",
						"description": "C03截图云存储URL"
					},
					"screenshot_time": {
						"bsonType": "timestamp",
						"description": "截图时间"
					},
					"notes": {
						"bsonType": "string",
						"description": "备注信息"
					}
				}
			}
		},
		"session_status": {
			"bsonType": "string",
			"description": "会话状态：CREATED(已创建)、IN_PROGRESS(进行中)、COMPLETED(已完成)、CANCELLED(已取消)",
			"enum": ["CREATED", "IN_PROGRESS", "COMPLETED", "CANCELLED"],
			"defaultValue": "CREATED"
		},
		"operator_name": {
			"bsonType": "string",
			"description": "操作员姓名"
		},
		"session_start_time": {
			"bsonType": "timestamp",
			"description": "会话开始时间"
		},
		"session_end_time": {
			"bsonType": "timestamp",
			"description": "会话结束时间"
		},
		"created_time": {
			"bsonType": "timestamp",
			"description": "创建时间",
			"defaultValue": {
				"$env": "now"
			}
		},
		"updated_time": {
			"bsonType": "timestamp",
			"description": "更新时间",
			"defaultValue": {
				"$env": "now"
			}
		}
	},
	"version": "0.0.1"
}
