package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// OpenVINO优化OCR结果
type OpenVINOOCRResult struct {
	FinalText           string            `json:"final_text"`
	Confidence          float64           `json:"confidence"`
	ProcessingTime      float64           `json:"processing_time"`
	Engine              string            `json:"engine"`
	Success             bool              `json:"success"`
	CorrectionsApplied  []string          `json:"corrections_applied"`
	OptimizationDetails map[string]string `json:"optimization_details"`
}

// OpenVINO优化OCR服务
type OpenVINOOCRService struct {
	pythonPath   string
	scriptPath   string
	tempDir      string
	initialized  bool
	mu           sync.Mutex
}

// 创建OpenVINO优化OCR服务
func NewOpenVINOOCRService() *OpenVINOOCRService {
	return &OpenVINOOCRService{
		pythonPath:  "python",
		scriptPath:  "openvino_optimized_ocr.py",
		tempDir:     "temp",
		initialized: false,
	}
}

// 检查环境
func (o *OpenVINOOCRService) CheckEnvironment() error {
	fmt.Println("=== 检查OpenVINO优化OCR环境 ===")
	
	// 检查Python
	cmd := exec.Command(o.pythonPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("Python未安装: %v", err)
	}
	fmt.Printf("✓ Python: %s", string(output))
	
	// 检查OpenVINO
	cmd = exec.Command(o.pythonPath, "-c", "import openvino as ov; print('OpenVINO版本:', ov.__version__)")
	output, err = cmd.Output()
	if err != nil {
		fmt.Printf("⚠ OpenVINO检查失败: %v\n", err)
	} else {
		fmt.Printf("✓ %s", string(output))
	}
	
	// 检查EasyOCR
	cmd = exec.Command(o.pythonPath, "-c", "import easyocr; print('EasyOCR可用')")
	output, err = cmd.Output()
	if err != nil {
		return fmt.Errorf("EasyOCR不可用: %v", err)
	}
	fmt.Printf("✓ %s", string(output))
	
	// 检查脚本
	if _, err := os.Stat(o.scriptPath); os.IsNotExist(err) {
		return fmt.Errorf("OpenVINO OCR脚本不存在: %s", o.scriptPath)
	}
	fmt.Printf("✓ OpenVINO OCR脚本: %s\n", o.scriptPath)
	
	o.initialized = true
	return nil
}

// 识别单张图片
func (o *OpenVINOOCRService) RecognizeImage(imagePath string) (*OpenVINOOCRResult, error) {
	o.mu.Lock()
	defer o.mu.Unlock()
	
	if !o.initialized {
		return nil, fmt.Errorf("服务未初始化")
	}
	
	fmt.Printf("OpenVINO优化识别: %s\n", imagePath)
	startTime := time.Now()
	
	// 创建临时脚本处理特定图片
	tempScript := o.createTempScript(imagePath)
	defer os.Remove(tempScript)
	
	// 调用OpenVINO优化OCR脚本
	cmd := exec.Command(o.pythonPath, tempScript)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("OpenVINO OCR脚本执行失败: %v", err)
	}
	
	// 解析JSON输出
	result, err := o.parseOCROutput(string(output))
	if err != nil {
		return nil, fmt.Errorf("解析OpenVINO OCR结果失败: %v", err)
	}
	
	duration := time.Since(startTime)
	fmt.Printf("✓ OpenVINO优化识别完成，耗时: %v\n", duration)
	
	return result, nil
}

// 创建临时脚本处理特定图片
func (o *OpenVINOOCRService) createTempScript(imagePath string) string {
	tempScript := filepath.Join(o.tempDir, "temp_openvino_ocr_script.py")
	os.MkdirAll(o.tempDir, 0755)
	
	scriptContent := fmt.Sprintf(`
import sys
sys.path.append('.')
from openvino_optimized_ocr import OpenVINOOptimizedOCR
import json

# 创建OpenVINO优化OCR引擎并处理指定图片
ocr_engine = OpenVINOOptimizedOCR()
result = ocr_engine.process_image_with_full_optimization('%s')

# 输出JSON结果
output = {
    "final_text": result.final_text,
    "confidence": result.confidence,
    "processing_time": result.processing_time,
    "engine": result.engine,
    "success": result.success,
    "corrections_applied": result.corrections_applied,
    "optimization_details": result.optimization_details
}

print("OPENVINO_JSON_START")
print(json.dumps(output, ensure_ascii=False))
print("OPENVINO_JSON_END")
`, strings.ReplaceAll(imagePath, "\\", "/"))
	
	os.WriteFile(tempScript, []byte(scriptContent), 0644)
	return tempScript
}

// 解析OCR输出
func (o *OpenVINOOCRService) parseOCROutput(output string) (*OpenVINOOCRResult, error) {
	lines := strings.Split(output, "\n")
	
	var jsonContent string
	inJSON := false
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "OPENVINO_JSON_START" {
			inJSON = true
			continue
		}
		if line == "OPENVINO_JSON_END" {
			break
		}
		if inJSON {
			jsonContent += line
		}
	}
	
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到OpenVINO JSON输出")
	}
	
	var result OpenVINOOCRResult
	err := json.Unmarshal([]byte(jsonContent), &result)
	if err != nil {
		return nil, fmt.Errorf("OpenVINO JSON解析失败: %v\n原始输出: %s", err, output)
	}
	
	return &result, nil
}

// 批量处理图片（10轮次优化）
func (o *OpenVINOOCRService) ProcessBatchOptimized(imagePaths []string) ([]*OpenVINOOCRResult, error) {
	fmt.Printf("=== OpenVINO优化批量处理 %d 张图片 ===\n", len(imagePaths))
	
	results := make([]*OpenVINOOCRResult, len(imagePaths))
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	// OpenVINO优化：使用更少的并发数，充分利用CPU优化
	semaphore := make(chan struct{}, 2) // 最多2个并发，让OpenVINO充分优化
	
	startTime := time.Now()
	
	for i, imagePath := range imagePaths {
		wg.Add(1)
		go func(index int, path string) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			result, err := o.RecognizeImage(path)
			
			mu.Lock()
			if err != nil {
				fmt.Printf("图片 %d OpenVINO识别失败: %v\n", index+1, err)
				results[index] = &OpenVINOOCRResult{
					Success: false,
					Engine:  "OpenVINO_Error",
				}
			} else {
				results[index] = result
				fmt.Printf("图片 %d OpenVINO识别成功: [%s] (%.3fs)\n", 
					index+1, result.FinalText, result.ProcessingTime)
			}
			mu.Unlock()
		}(i, imagePath)
	}
	
	wg.Wait()
	
	totalTime := time.Since(startTime)
	successCount := 0
	totalProcessingTime := 0.0
	
	for _, result := range results {
		if result.Success {
			successCount++
			totalProcessingTime += result.ProcessingTime
		}
	}
	
	avgProcessingTime := totalProcessingTime / float64(len(imagePaths))
	
	fmt.Printf("✓ OpenVINO优化批量处理完成: %d/%d 成功，总耗时: %v\n", 
		successCount, len(imagePaths), totalTime)
	fmt.Printf("✓ 平均单张处理时间: %.3f秒\n", avgProcessingTime)
	
	return results, nil
}

// 分析OpenVINO优化结果
func (o *OpenVINOOCRService) AnalyzeOptimizedResult(result *OpenVINOOCRResult) {
	fmt.Printf("\n=== OpenVINO优化结果分析 ===\n")
	fmt.Printf("识别文本: [%s]\n", result.FinalText)
	fmt.Printf("置信度: %.2f%%\n", result.Confidence*100)
	fmt.Printf("处理时间: %.3f秒\n", result.ProcessingTime)
	fmt.Printf("使用引擎: %s\n", result.Engine)
	fmt.Printf("识别成功: %t\n", result.Success)
	
	if len(result.CorrectionsApplied) > 0 {
		fmt.Printf("应用校正: %v\n", result.CorrectionsApplied)
	}
	
	fmt.Printf("\n=== OpenVINO优化详情 ===\n")
	for key, value := range result.OptimizationDetails {
		fmt.Printf("%s: %s\n", key, value)
	}
	
	// 性能评估
	if result.ProcessingTime < 1.0 {
		fmt.Printf("✓ OpenVINO优化效果优秀 (<1秒)\n")
	} else if result.ProcessingTime < 2.0 {
		fmt.Printf("⚠ OpenVINO优化效果良好 (<2秒)\n")
	} else {
		fmt.Printf("❌ OpenVINO优化效果需要进一步调整 (>2秒)\n")
	}
}

// 模拟10轮次OpenVINO优化流程
func simulateOpenVINO10RoundProcess() {
	fmt.Println("=== 模拟10轮次OpenVINO优化流程 ===")
	
	// 创建OpenVINO优化服务
	service := NewOpenVINOOCRService()
	
	// 检查环境
	if err := service.CheckEnvironment(); err != nil {
		fmt.Printf("OpenVINO环境检查失败: %v\n", err)
		return
	}
	
	// 模拟10轮次，每轮2张图片
	imagePaths := []string{}
	testImage := "pic/test_image.png"
	
	// 检查测试图片是否存在
	if _, err := os.Stat(testImage); os.IsNotExist(err) {
		fmt.Printf("❌ 测试图片不存在: %s\n", testImage)
		return
	}
	
	// 生成10轮次的图片路径
	for round := 1; round <= 10; round++ {
		imagePaths = append(imagePaths, testImage) // B02模式
		imagePaths = append(imagePaths, testImage) // C03模式
	}
	
	fmt.Printf("开始OpenVINO优化处理10轮次（共%d张图片）...\n", len(imagePaths))
	
	// OpenVINO优化批量处理
	results, err := service.ProcessBatchOptimized(imagePaths)
	if err != nil {
		fmt.Printf("OpenVINO优化批量处理失败: %v\n", err)
		return
	}
	
	// 分析结果
	fmt.Printf("\n=== OpenVINO优化10轮次处理结果汇总 ===\n")
	successCount := 0
	totalTime := 0.0
	
	for i, result := range results {
		round := (i / 2) + 1
		mode := "B02"
		if i%2 == 1 {
			mode = "C03"
		}
		
		if result.Success {
			successCount++
			totalTime += result.ProcessingTime
			fmt.Printf("轮次%d-%s: ✓ [%s] (%.3fs)\n", 
				round, mode, result.FinalText, result.ProcessingTime)
		} else {
			fmt.Printf("轮次%d-%s: ❌ OpenVINO识别失败\n", round, mode)
		}
	}
	
	avgTime := totalTime / float64(len(results))
	fmt.Printf("\n=== OpenVINO优化性能统计 ===\n")
	fmt.Printf("成功率: %d/%d (%.1f%%)\n", successCount, len(results), 
		float64(successCount)/float64(len(results))*100)
	fmt.Printf("平均处理时间: %.3f秒\n", avgTime)
	fmt.Printf("总处理时间: %.3f秒\n", totalTime)
	
	// 与原方案对比
	fmt.Printf("\n=== OpenVINO优化性能对比 ===\n")
	fmt.Printf("原扣子API方案: 10-30秒/张 × 20张 = 200-600秒\n")
	fmt.Printf("轻量化OCR方案: 1.7秒/张 × 20张 = 34秒\n")
	fmt.Printf("OpenVINO优化方案: %.3f秒/张 × 20张 = %.3f秒\n", avgTime, totalTime)
	
	if totalTime > 0 {
		improvement1 := 400.0 / totalTime  // 相比扣子API
		improvement2 := 34.0 / totalTime   // 相比轻量化OCR
		fmt.Printf("相比扣子API提升: %.1f倍\n", improvement1)
		fmt.Printf("相比轻量化OCR提升: %.1f倍\n", improvement2)
	}
}

func main() {
	fmt.Println("=== OpenVINO优化OCR Go集成测试 ===")
	
	// 运行OpenVINO优化10轮次模拟
	simulateOpenVINO10RoundProcess()
}
