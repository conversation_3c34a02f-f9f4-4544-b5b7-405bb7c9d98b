@echo off
echo ===== PaddleOCR Tiny + OpenVINO 安装脚本 =====
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或不在PATH中
    echo 请先安装Python 3.8-3.11: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装基础依赖...
pip install numpy opencv-python

echo.
echo 安装OpenVINO...
pip install openvino

echo.
echo 安装PaddleOCR...
pip install paddleocr

echo.
echo 验证安装...
python -c "import openvino as ov; print('OpenVINO版本:', ov.__version__)"
python -c "import paddleocr; print('PaddleOCR安装成功')"
python -c "import cv2; print('OpenCV版本:', cv2.__version__)"

if %errorlevel% equ 0 (
    echo.
    echo ===== 安装完成 =====
    echo 现在可以运行: python paddleocr_openvino_solution.py
    echo.
    echo 注意: OpenVINO模型转换需要额外步骤
    echo 请参考 paddleocr_openvino_setup.md 进行模型转换
) else (
    echo.
    echo ===== 安装失败 =====
    echo 请检查错误信息并重试
)

echo.
pause
