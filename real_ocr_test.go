package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"os/exec"
	"regexp"
	"strings"
)

// 真实OCR识别测试
func performRealOCRTest(imagePath string) {
	fmt.Printf("=== 真实OCR识别测试 ===\n")
	fmt.Printf("目标图片: %s\n", imagePath)
	
	// 1. 先裁剪左上角1/4区域
	croppedPath, err := cropTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("❌ 裁剪失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 裁剪完成: %s\n", croppedPath)
	
	// 2. 尝试多种OCR方法
	ocrResults := []OCRResult{}
	
	// 方法1: 尝试Tesseract命令行
	if result := tryTesseractCommand(croppedPath); result.Text != "" {
		ocrResults = append(ocrResults, result)
	}
	
	// 方法2: 尝试PowerShell OCR (Windows 10+)
	if result := tryPowerShellOCR(croppedPath); result.Text != "" {
		ocrResults = append(ocrResults, result)
	}
	
	// 3. 分析OCR结果
	if len(ocrResults) == 0 {
		fmt.Printf("❌ 所有OCR方法都失败，使用模拟结果\n")
		simulateOCRForComparison(croppedPath)
		return
	}
	
	fmt.Printf("\n=== OCR识别结果分析 ===\n")
	for i, result := range ocrResults {
		fmt.Printf("OCR引擎 %d (%s):\n", i+1, result.Engine)
		fmt.Printf("  原始文本: [%s]\n", result.Text)
		fmt.Printf("  置信度: %.2f\n", result.Confidence)
		
		// 提取器官名称
		organName := extractOrganFromText(result.Text)
		fmt.Printf("  提取结果: [%s]\n", organName)
		
		// 应用校正
		correctedOrgan := applyOCRCorrection(organName)
		if correctedOrgan != organName {
			fmt.Printf("  校正结果: [%s]\n", correctedOrgan)
		}
		
		// 分析识别质量
		analyzeOCRQuality(result.Text, correctedOrgan)
		fmt.Println()
	}
}

type OCRResult struct {
	Engine     string
	Text       string
	Confidence float64
	Success    bool
}

// 尝试使用Tesseract命令行
func tryTesseractCommand(imagePath string) OCRResult {
	fmt.Printf("尝试Tesseract OCR...\n")
	
	// 检查tesseract是否可用
	if _, err := exec.LookPath("tesseract"); err != nil {
		fmt.Printf("  ❌ Tesseract未安装或不在PATH中\n")
		return OCRResult{Engine: "Tesseract", Success: false}
	}
	
	// 执行tesseract命令
	cmd := exec.Command("tesseract", imagePath, "stdout", "-l", "chi_sim+eng")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("  ❌ Tesseract执行失败: %v\n", err)
		return OCRResult{Engine: "Tesseract", Success: false}
	}
	
	text := strings.TrimSpace(string(output))
	fmt.Printf("  ✅ Tesseract识别成功\n")
	
	return OCRResult{
		Engine:     "Tesseract",
		Text:       text,
		Confidence: 0.8, // 默认置信度
		Success:    true,
	}
}

// 尝试使用PowerShell OCR (Windows 10+)
func tryPowerShellOCR(imagePath string) OCRResult {
	fmt.Printf("尝试Windows PowerShell OCR...\n")
	
	// 检查是否在Windows上
	if os.Getenv("OS") != "Windows_NT" {
		fmt.Printf("  ❌ 非Windows系统，跳过PowerShell OCR\n")
		return OCRResult{Engine: "PowerShell", Success: false}
	}
	
	// PowerShell OCR脚本
	psScript := fmt.Sprintf(`
Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms
[System.Reflection.Assembly]::LoadWithPartialName("System.Drawing")

$bitmap = New-Object System.Drawing.Bitmap "%s"
$stream = New-Object System.IO.MemoryStream
$bitmap.Save($stream, [System.Drawing.Imaging.ImageFormat]::Png)
$bytes = $stream.ToArray()

# 这里需要Windows.Media.Ocr API，但在PowerShell中比较复杂
# 暂时返回空结果
Write-Output ""
`, imagePath)
	
	cmd := exec.Command("powershell", "-Command", psScript)
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("  ❌ PowerShell OCR执行失败: %v\n", err)
		return OCRResult{Engine: "PowerShell", Success: false}
	}
	
	text := strings.TrimSpace(string(output))
	if text == "" {
		fmt.Printf("  ❌ PowerShell OCR未返回结果\n")
		return OCRResult{Engine: "PowerShell", Success: false}
	}
	
	fmt.Printf("  ✅ PowerShell OCR识别成功\n")
	return OCRResult{
		Engine:     "PowerShell",
		Text:       text,
		Confidence: 0.7,
		Success:    true,
	}
}

// 裁剪左上角1/4区域
func cropTopLeftQuarter(imagePath string) (string, error) {
	file, err := os.Open(imagePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	
	img, _, err := image.Decode(file)
	if err != nil {
		return "", err
	}
	
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	
	quarterWidth := width / 4
	quarterHeight := height / 4
	
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))
	
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}
	
	outputPath := "pic/real_ocr_cropped.png"
	os.MkdirAll("pic", 0755)
	
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outputFile.Close()
	
	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", err
	}
	
	return outputPath, nil
}

// 模拟OCR用于对比
func simulateOCRForComparison(imagePath string) {
	fmt.Printf("\n=== 模拟OCR结果（用于对比） ===\n")
	fmt.Printf("由于无法使用真实OCR，以下是基于图片内容的预期结果:\n\n")
	
	expectedResults := []string{
		"0.000 腹部第1腰椎水平截面",  // 期望的正确结果
		"0.000 胆囊第1腰椎水平截面",  // 可能的误识别
	}
	
	for i, result := range expectedResults {
		fmt.Printf("预期结果 %d: [%s]\n", i+1, result)
		organName := extractOrganFromText(result)
		fmt.Printf("  提取器官: [%s]\n", organName)
		
		corrected := applyOCRCorrection(organName)
		if corrected != organName {
			fmt.Printf("  校正后: [%s]\n", corrected)
		}
		
		if i == 0 {
			fmt.Printf("  ✅ 这是期望的正确结果\n")
		} else {
			fmt.Printf("  ⚠️  这是可能的误识别，已校正\n")
		}
		fmt.Println()
	}
}

// 分析OCR识别质量
func analyzeOCRQuality(ocrText, extractedOrgan string) {
	fmt.Printf("  质量分析:\n")
	
	// 检查是否包含期望的模式
	if strings.Contains(ocrText, "0.000") {
		fmt.Printf("    ✅ 包含数字标识符 '0.000'\n")
	} else if strings.Contains(ocrText, "O.000") || strings.Contains(ocrText, "0.00O") {
		fmt.Printf("    ⚠️  数字识别有误，但可校正\n")
	} else {
		fmt.Printf("    ❌ 未找到数字标识符\n")
	}
	
	// 检查器官名称
	if extractedOrgan == "腹部第1腰椎水平截面" {
		fmt.Printf("    ✅ 器官名称识别正确\n")
	} else if extractedOrgan != "未知器官" {
		fmt.Printf("    ⚠️  器官名称需要校正\n")
	} else {
		fmt.Printf("    ❌ 未能识别器官名称\n")
	}
	
	// 检查文本长度和复杂度
	if len(ocrText) > 10 {
		fmt.Printf("    ✅ 识别文本长度合理\n")
	} else {
		fmt.Printf("    ⚠️  识别文本过短\n")
	}
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	if !strings.Contains(text, "0.000") && !strings.Contains(text, "O.000") && !strings.Contains(text, "0.00O") {
		return "未知器官"
	}
	
	text = strings.TrimSpace(text)
	text = strings.ReplaceAll(text, "O.000", "0.000")
	text = strings.ReplaceAll(text, "0.00O", "0.000")
	text = strings.ReplaceAll(text, "0.0O0", "0.000")
	
	pattern := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	matches := pattern.FindStringSubmatch(text)
	
	if len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		return organName
	}
	
	return "未知器官"
}

// 应用OCR校正规则
func applyOCRCorrection(organName string) string {
	corrections := map[string]string{
		"胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"服部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"复部第1腰椎水平截面": "腹部第1腰椎水平截面",
	}
	
	if corrected, exists := corrections[organName]; exists {
		fmt.Printf("    🔧 校正: %s → %s\n", organName, corrected)
		return corrected
	}
	
	return organName
}

func main() {
	fmt.Println("=== 真实OCR识别测试程序 ===")
	
	imagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`
	
	// 检查文件
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 图片文件不存在\n")
			return
		}
		imagePath = relativePath
	}
	
	fmt.Printf("✅ 找到图片: %s\n", imagePath)
	
	// 显示文件信息
	if info, err := os.Stat(imagePath); err == nil {
		fmt.Printf("文件大小: %.2f KB\n", float64(info.Size())/1024)
	}
	
	// 开始OCR识别
	performRealOCRTest(imagePath)
}
