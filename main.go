package main

import (
	"embed"
	"fmt"
	"log"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

// 全局变量存储锁文件路径
var lockFilePath string
var lockFile *os.File

// checkSingleInstance 检查是否已有实例在运行
func checkSingleInstance() bool {
	log.Println("[单实例检查] 开始检查是否已有实例在运行...")

	// 获取当前执行文件的目录
	exePath, err := os.Executable()
	if err != nil {
		log.Printf("[单实例检查] 错误: 无法获取可执行文件路径: %v", err)
		return false
	}
	exeDir := filepath.Dir(exePath)
	lockFilePath = filepath.Join(exeDir, "MagneticOperator.lock")
	log.Printf("[单实例检查] 锁文件路径: %s", lockFilePath)

	// 检查锁文件是否存在
	if info, err := os.Stat(lockFilePath); err == nil {
		log.Printf("[单实例检查] 发现现有锁文件，大小: %d 字节，修改时间: %v", info.Size(), info.ModTime())

		// 锁文件存在，读取PID并检查进程是否还在运行
		data, err := os.ReadFile(lockFilePath)
		if err != nil {
			log.Printf("[单实例检查] 警告: 无法读取锁文件内容: %v", err)
		} else {
			pidStr := strings.TrimSpace(string(data))
			log.Printf("[单实例检查] 锁文件内容: '%s'", pidStr)

			if pid, err := strconv.Atoi(pidStr); err == nil {
				log.Printf("[单实例检查] 检查进程 PID %d 是否在运行...", pid)
				if isProcessRunning(pid) {
					log.Printf("[单实例检查] 进程 PID %d 仍在运行，拒绝启动新实例", pid)
					return false
				} else {
					log.Printf("[单实例检查] 进程 PID %d 已不存在，清理旧锁文件", pid)
				}
			} else {
				log.Printf("[单实例检查] 警告: 锁文件内容格式无效: %v", err)
			}
		}

		// 如果进程不存在或读取失败，删除旧的锁文件
		if err := os.Remove(lockFilePath); err != nil {
			log.Printf("[单实例检查] 警告: 删除旧锁文件失败: %v", err)
		} else {
			log.Println("[单实例检查] 成功删除旧锁文件")
		}
	} else {
		log.Println("[单实例检查] 未发现现有锁文件")
	}

	// 创建新的锁文件，使用独占模式防止竞态条件
	log.Println("[单实例检查] 尝试创建新锁文件...")
	var err2 error
	lockFile, err2 = os.OpenFile(lockFilePath, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0666)
	if err2 != nil {
		log.Printf("[单实例检查] 错误: 无法创建锁文件: %v", err2)
		// 如果创建失败，可能是另一个实例正在同时启动
		time.Sleep(100 * time.Millisecond) // 短暂等待
		// 再次检查是否有其他实例创建了锁文件
		if data, err := os.ReadFile(lockFilePath); err == nil {
			if pid, err := strconv.Atoi(strings.TrimSpace(string(data))); err == nil {
				if isProcessRunning(pid) {
					log.Printf("[单实例检查] 检测到另一个实例 PID %d 已启动", pid)
					return false
				}
			}
		}
		return false
	}

	// 写入当前进程ID
	pid := os.Getpid()
	log.Printf("[单实例检查] 写入当前进程 PID: %d", pid)
	if _, err := lockFile.WriteString(fmt.Sprintf("%d", pid)); err != nil {
		log.Printf("[单实例检查] 错误: 写入PID失败: %v", err)
		lockFile.Close()
		os.Remove(lockFilePath)
		return false
	}

	// 刷新文件缓冲区
	if err := lockFile.Sync(); err != nil {
		log.Printf("[单实例检查] 警告: 同步文件失败: %v", err)
	}

	log.Println("[单实例检查] 成功创建锁文件，允许启动")
	return true
}

// isProcessRunning 检查指定PID的进程是否在运行
func isProcessRunning(pid int) bool {
	log.Printf("[进程检查] 检查进程 PID %d 是否在运行...", pid)

	// 方法1: 使用os.FindProcess和Signal(0)检查（推荐方法）
	process, err := os.FindProcess(pid)
	if err != nil {
		log.Printf("[进程检查] FindProcess失败: %v", err)
		return false
	}

	// 在Windows上，Signal(0)不可用，所以使用tasklist作为备用方法
	// 但首先尝试发送一个无害的信号来检查进程是否存在
	err = process.Signal(syscall.Signal(0))
	if err == nil {
		log.Printf("[进程检查] 进程 PID %d 存在（通过Signal检查）", pid)
		return true
	}
	log.Printf("[进程检查] Signal检查失败: %v，尝试tasklist方法", err)

	// 方法2: 使用tasklist命令检查进程是否存在（Windows备用方法）
	cmdStr := fmt.Sprintf("tasklist /FI \"PID eq %d\" /NH", pid)
	log.Printf("[进程检查] 执行命令: %s", cmdStr)

	cmd := exec.Command("cmd", "/c", cmdStr)
	out, err := cmd.Output()
	if err != nil {
		log.Printf("[进程检查] tasklist命令执行失败: %v", err)
		return false
	}

	// 如果输出为空或只包含空白字符，说明进程不存在
	output := strings.TrimSpace(string(out))
	log.Printf("[进程检查] tasklist输出: '%s'", output)

	if len(output) == 0 {
		log.Printf("[进程检查] 进程 PID %d 不存在（输出为空）", pid)
		return false
	}

	// 检查输出是否包含"INFO: No tasks are running"等错误信息
	if strings.Contains(output, "INFO: No tasks are running") ||
		strings.Contains(output, "信息: 没有运行的任务匹配指定标准") {
		log.Printf("[进程检查] 进程 PID %d 不存在（tasklist返回无匹配）", pid)
		return false
	}

	log.Printf("[进程检查] 进程 PID %d 存在（通过tasklist确认）", pid)
	return true
}

// cleanupLockFile 清理锁文件
func cleanupLockFile() {
	log.Println("[清理] 开始清理锁文件...")

	// 先关闭文件句柄
	if lockFile != nil {
		log.Println("[清理] 关闭锁文件句柄")
		lockFile.Close()
		lockFile = nil
	}

	// 删除锁文件
	if lockFilePath != "" {
		if err := os.Remove(lockFilePath); err != nil {
			log.Printf("[清理] 警告: 删除锁文件失败: %v", err)
		} else {
			log.Printf("[清理] 成功删除锁文件: %s", lockFilePath)
		}
		lockFilePath = ""
	} else {
		log.Println("[清理] 无锁文件需要清理")
	}
}

func main() {
	// 设置日志输出格式
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("[启动] 磁感分析操作台启动中...")

	// 检查单实例
	log.Println("[启动] 开始单实例检查")
	if !checkSingleInstance() {
		log.Println("[启动] 单实例检查失败，程序退出")
		fmt.Println("磁感分析操作台已在运行中，请勿重复启动！")
		os.Exit(1)
	}
	log.Println("[启动] 单实例检查通过")

	// 设置程序退出时清理锁文件
	defer cleanupLockFile()

	// 设置信号处理，确保程序被强制终止时也能清理锁文件
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		log.Println("[信号] 收到退出信号，开始清理...")
		cleanupLockFile()
		log.Println("[信号] 清理完成，程序退出")
		os.Exit(0)
	}()

	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err := wails.Run(&options.App{
		Title:             "磁感分析操作台",
		Width:             320, // 默认小窗体宽度
		Height:            800, // 默认小窗体高度
		MinWidth:          300,
		MinHeight:         600,
		MaxWidth:          1400,
		MaxHeight:         1000,
		StartHidden:       false,
		HideWindowOnClose: false,
		AlwaysOnTop:       true, // 始终置顶
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 240, G: 242, B: 245, A: 1},
		OnStartup:        app.startup,
		OnBeforeClose:    app.shutdown,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}
