package main

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// 应用程序配置结构体
type appConfig struct {
	MpAppInfo struct {
		AppID      string `json:"appid"`
		TargetPage string `json:"target_page"`
	} `json:"mp_app_info"`

	SiteInfo struct {
		SiteID    string `json:"site_id"`
		SiteName  string `json:"site_name"`
		SiteType  string `json:"site_type"`
		ParentOrg string `json:"parent_org"`
		Location  struct {
			Province string `json:"province"`
			City     string `json:"city"`
			District string `json:"district"`
			Address  string `json:"address"`
		} `json:"location"`
		Contact struct {
			Manager string `json:"manager"`
			Phone   string `json:"phone"`
		} `json:"contact"`
	} `json:"site_info"`

	CropSettings struct {
		TopPercent    float64 `json:"top_percent"`
		BottomPercent float64 `json:"bottom_percent"`
		LeftPercent   float64 `json:"left_percent"`
		RightPercent  float64 `json:"right_percent"`
	} `json:"crop_settings"`

	APIKeys struct {
		Dcloud struct {
			AccessKey string `json:"access_key"`
			SecretKey string `json:"secret_key"`
			UploadURL string `json:"upload_url"`
			SpaceID   string `json:"space_id"`
		} `json:"dcloud"`
		Coze struct {
			Token                      string `json:"token"`
			WorkflowIDPostPic          string `json:"workflow_id_post_pic"`          // 调用扣子load_screenshot工作流，推送截图信息
			WorkflowIDPostRegistration string `json:"workflow_id_post_registration"` // 调用扣子get_user_registration工作流，推送受检人挂号信息
			WorkflowIDCheckUserInfo    string `json:"workflow_id_user_info"`         // 调用扣子confirm_registration_to_check工作流，推送受检人挂号信息
			SpaceID                    string `json:"space_id"`
			AppID                      string `json:"app_id"`
		} `json:"coze"`
	} `json:"api_keys"`

	DeviceInfo struct {
		MACAddress string `json:"mac_address"`
		DeviceName string `json:"device_name"`
	} `json:"device_info"`

	UserInfo struct {
		Name     string `json:"name"`
		Gender   string `json:"gender"`
		Birth    string `json:"birth"`
		IDNumber string `json:"id_number"`
	} `json:"user_info"`
}

// 保存配置到文件
func saveConfig(config *appConfig) error {
	configPath := filepath.Join("config", "app_config.json")
	os.MkdirAll(filepath.Dir(configPath), 0755)

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}
