#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轻量化OCR解决方案
基于现有可用的OCR引擎，实现高性能医疗图像识别
"""

import os
import sys
import time
import json
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass

@dataclass
class LightweightOCRResult:
    final_text: str
    confidence: float
    processing_time: float
    engine: str
    corrections_applied: List[str]
    success: bool

class LightweightOCRService:
    """轻量化OCR服务"""
    
    def __init__(self):
        self.available_engines = self._check_available_engines()
        self.medical_terms = self._load_medical_terms()
        self.ocr_engines = {}
        self._initialize_engines()
        
    def _check_available_engines(self) -> Dict[str, bool]:
        """检查可用的OCR引擎"""
        engines = {
            "easyocr": False,
            "paddleocr": False,
            "tesseract": False
        }
        
        try:
            import easyocr
            engines["easyocr"] = True
            print("OK: EasyOCR available")
        except ImportError:
            print("NO: EasyOCR not available")

        try:
            import paddleocr
            engines["paddleocr"] = True
            print("OK: PaddleOCR available")
        except ImportError:
            print("NO: PaddleOCR not available")

        try:
            import subprocess
            subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
            engines["tesseract"] = True
            print("OK: Tesseract available")
        except:
            print("NO: Tesseract not available")
        
        return engines
    
    def _load_medical_terms(self) -> Dict[str, str]:
        """加载医疗术语校正库"""
        return {
            # 器官名称校正
            "胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
            "胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
            "腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
            "服部第1腰椎水平截面": "腹部第1腰椎水平截面",
            "复部第1腰椎水平截面": "腹部第1腰椎水平截面",
            
            # 数字校正
            "0,000": "0.000",
            "O.000": "0.000",
            "0.00O": "0.000",
            "0.0O0": "0.000",
            "o.000": "0.000",
            "0.ooo": "0.000",
        }
    
    def _initialize_engines(self):
        """初始化可用的OCR引擎"""
        # 初始化EasyOCR
        if self.available_engines["easyocr"]:
            try:
                import easyocr
                self.ocr_engines["easyocr"] = easyocr.Reader(['ch_sim', 'en'], gpu=False)
                print("OK: EasyOCR initialized")
            except Exception as e:
                print(f"NO: EasyOCR initialization failed: {e}")
        
        # 初始化PaddleOCR
        if self.available_engines["paddleocr"]:
            try:
                from paddleocr import PaddleOCR
                # 使用轻量化配置
                self.ocr_engines["paddleocr"] = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    show_log=False,
                    use_gpu=False,
                    det_model_dir=None,  # 使用默认轻量模型
                    rec_model_dir=None,
                    cls_model_dir=None,
                    det_limit_side_len=960,  # 限制检测边长，提升速度
                    det_limit_type='min',
                )
                print("OK: PaddleOCR initialized with lightweight config")
            except Exception as e:
                print(f"NO: PaddleOCR initialization failed: {e}")
    
    def preprocess_image(self, image_path: str, target_size: Tuple[int, int] = None) -> str:
        """图像预处理优化"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Cannot read image: {image_path}")
            
            # 图像优化处理
            # 1. 如果指定了目标尺寸，进行缩放
            if target_size:
                image = cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
            
            # 2. 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 3. 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # 4. 高斯滤波去噪
            denoised = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            # 5. 锐化处理
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(denoised, -1, kernel)
            
            # 6. 自适应二值化
            binary = cv2.adaptiveThreshold(
                sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # 保存预处理后的图像
            processed_path = image_path.replace('.png', '_processed.png')
            cv2.imwrite(processed_path, binary)
            
            return processed_path
            
        except Exception as e:
            print(f"Image preprocessing failed: {e}")
            return image_path  # 返回原图路径
    
    def run_easyocr(self, image_path: str) -> List[Dict]:
        """运行EasyOCR"""
        if "easyocr" not in self.ocr_engines:
            return []
        
        try:
            results = self.ocr_engines["easyocr"].readtext(image_path)
            ocr_results = []
            
            for bbox, text, confidence in results:
                if confidence > 0.3:  # 置信度阈值
                    ocr_results.append({
                        "text": text,
                        "confidence": confidence,
                        "engine": "EasyOCR",
                        "bbox": bbox
                    })
            
            return ocr_results
        except Exception as e:
            print(f"EasyOCR error: {e}")
            return []
    
    def run_paddleocr(self, image_path: str) -> List[Dict]:
        """运行PaddleOCR"""
        if "paddleocr" not in self.ocr_engines:
            return []
        
        try:
            result = self.ocr_engines["paddleocr"].ocr(image_path, cls=True)
            ocr_results = []
            
            for idx in range(len(result)):
                res = result[idx]
                if res:
                    for line in res:
                        bbox = line[0]
                        text_info = line[1]
                        text = text_info[0]
                        confidence = text_info[1]
                        
                        if confidence > 0.3:
                            ocr_results.append({
                                "text": text,
                                "confidence": confidence,
                                "engine": "PaddleOCR",
                                "bbox": bbox
                            })
            
            return ocr_results
        except Exception as e:
            print(f"PaddleOCR error: {e}")
            return []
    
    def apply_medical_corrections(self, text: str) -> Tuple[str, List[str]]:
        """应用医疗术语校正"""
        corrections_applied = []
        corrected_text = text
        
        for wrong, correct in self.medical_terms.items():
            if wrong in corrected_text:
                corrected_text = corrected_text.replace(wrong, correct)
                corrections_applied.append(f"{wrong} → {correct}")
        
        return corrected_text, corrections_applied
    
    def extract_target_pattern(self, all_results: List[Dict]) -> Tuple[str, float, List[str]]:
        """提取目标模式并应用校正"""
        target_candidates = []
        
        for result in all_results:
            text = result["text"]
            confidence = result["confidence"]
            
            # 查找包含数字模式的文本
            if any(pattern in text for pattern in ["0.000", "0,000", "O.000", "0.00O", "o.000", "0.ooo"]):
                # 应用校正
                corrected_text, corrections = self.apply_medical_corrections(text)
                
                # 标准化数字格式
                for wrong_num in ["0,000", "O.000", "0.00O", "o.000", "0.ooo"]:
                    corrected_text = corrected_text.replace(wrong_num, "0.000")
                
                target_candidates.append((corrected_text, confidence, corrections))
        
        if target_candidates:
            # 选择置信度最高的结果
            best_candidate = max(target_candidates, key=lambda x: x[1])
            return best_candidate
        
        return "", 0.0, []
    
    def process_image_optimized(self, image_path: str) -> LightweightOCRResult:
        """优化的图像处理流程"""
        start_time = time.time()
        
        print(f"Processing image: {image_path}")
        
        try:
            # 1. 图像预处理
            processed_image_path = self.preprocess_image(image_path)
            
            # 2. 运行所有可用的OCR引擎
            all_results = []
            engines_used = []
            
            if "paddleocr" in self.ocr_engines:
                paddle_results = self.run_paddleocr(processed_image_path)
                all_results.extend(paddle_results)
                if paddle_results:
                    engines_used.append("PaddleOCR")
            
            if "easyocr" in self.ocr_engines:
                easy_results = self.run_easyocr(processed_image_path)
                all_results.extend(easy_results)
                if easy_results:
                    engines_used.append("EasyOCR")
            
            # 3. 提取目标模式并应用校正
            final_text, confidence, corrections = self.extract_target_pattern(all_results)
            
            processing_time = time.time() - start_time
            
            # 清理临时文件
            if processed_image_path != image_path and os.path.exists(processed_image_path):
                os.remove(processed_image_path)
            
            return LightweightOCRResult(
                final_text=final_text,
                confidence=confidence,
                processing_time=processing_time,
                engine="+".join(engines_used) if engines_used else "None",
                corrections_applied=corrections,
                success=bool(final_text)
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"Error processing image: {e}")
            
            return LightweightOCRResult(
                final_text="",
                confidence=0.0,
                processing_time=processing_time,
                engine="Error",
                corrections_applied=[],
                success=False
            )

def main():
    """主函数 - 演示使用"""
    print("=== Lightweight OCR Solution ===")
    
    # 创建服务实例
    service = LightweightOCRService()
    
    # 检查可用引擎
    available_count = sum(service.available_engines.values())
    if available_count == 0:
        print("No OCR engines available!")
        return
    
    print(f"Available engines: {available_count}")
    
    # 测试图像
    test_image = "pic/test_image.png"
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        return
    
    # 处理图像
    result = service.process_image_optimized(test_image)
    
    # 显示结果
    print(f"\n=== Results ===")
    print(f"Final text: [{result.final_text}]")
    print(f"Confidence: {result.confidence:.2f}")
    print(f"Processing time: {result.processing_time:.3f}s")
    print(f"Engine: {result.engine}")
    print(f"Success: {result.success}")
    if result.corrections_applied:
        print(f"Corrections: {result.corrections_applied}")
    
    # 输出JSON格式结果
    output = {
        "final_text": result.final_text,
        "confidence": result.confidence,
        "processing_time": result.processing_time,
        "engine": result.engine,
        "success": result.success,
        "corrections_applied": result.corrections_applied
    }
    
    print(f"\n=== JSON Output ===")
    print(json.dumps(output, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
