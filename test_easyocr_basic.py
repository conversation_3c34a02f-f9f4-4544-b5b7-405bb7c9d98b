#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

def test_easyocr():
    print("=== EasyOCR Basic Test ===")
    
    try:
        print("1. Importing EasyOCR...")
        import easyocr
        print("OK: EasyOCR imported successfully")
        
        print("2. Initializing EasyOCR Reader...")
        # Use CPU mode to avoid GPU issues
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        print("OK: EasyOCR Reader initialized")
        
        # Check image file
        image_path = sys.argv[1] if len(sys.argv) > 1 else "pic/医生或健康专家-B02-OCR-20250616_221124.png"
        print(f"3. Checking image file: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"ERROR: Image file not found: {image_path}")
            return
        
        print("OK: Image file exists")
        
        print("4. Starting OCR recognition...")
        result = reader.readtext(image_path)
        print("OK: OCR recognition completed")
        
        print(f"5. Recognition results ({len(result)} text blocks):")
        for i, (bbox, text, confidence) in enumerate(result):
            print(f"  Block {i+1}:")
            print(f"    Text: [{text}]")
            print(f"    Confidence: {confidence:.2f}")
            print()
        
        # Look for target content
        print("6. Looking for target content (containing 0.000):")
        found_target = False
        for bbox, text, confidence in result:
            if "0.000" in text or "O.000" in text or "0.00O" in text:
                print(f"  TARGET FOUND: [{text}] (confidence: {confidence:.2f})")
                found_target = True
        
        if not found_target:
            print("  No target text containing 0.000 found")
        
        return result
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_easyocr()
