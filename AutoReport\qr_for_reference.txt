// 1. 桌面 GUI 端：生成健康检测码
// 1.1 生成健康检测码
// 在桌面 GUI 端（Go 语言），生成健康检测码的逻辑如下：
package main

import (
	"fmt"
	"time"
	"encoding/base64"
	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
)

// 配置信息
type Config struct {
	SiteID    string
	MACAddress string
}

// 生成挂号码
func GenerateFullNumber(config Config) string {
	siteID := config.SiteID
	if siteID == "" {
		siteID = "UNKNOWN"
	}

	macAddress := config.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN-MAC"
	}

	timestamp := time.Now().Format("20060102150405")
	fullNumber := fmt.Sprintf("%s-%s-%s", siteID, macAddress, timestamp)
	return fullNumber
}

// 生成健康检测码（二维码）
func GenerateHealthQRCode(fullNumber string) ([]byte, error) {
	// 将挂号码编码为二维码
	qrCode, err := qr.Encode(fullNumber, qr.EcLevelL)
	if err != nil {
		return nil, err
	}

	// 将二维码转换为图片
	img, err := barcode.Image(qrCode, barcode.FgBlack, barcode.BgWhite, 20)
	if err != nil {
		return nil, err
	}

	// 将图片转换为 Base64 编码
	buf := new(bytes.Buffer)
	if err := png.Encode(buf, img); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func main() {
	config := Config{
		SiteID: "SITE123",
		MACAddress: "00:1A:2B:3C:4D:5E",
	}

	fullNumber := GenerateFullNumber(config)
	qrBytes, err := GenerateHealthQRCode(fullNumber)
	if err != nil {
		fmt.Println("生成二维码失败:", err)
		return
	}

	// 将二维码图片保存到文件或显示在 GUI 界面上
	err = ioutil.WriteFile("health_qr.png", qrBytes, 0644)
	if err != nil {
		fmt.Println("保存二维码失败:", err)
		return
	}

	fmt.Println("健康检测码生成成功，已保存为 health_qr.png")
}