package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"os/exec"
	"regexp"
	"strings"
)

// 测试不同区域的OCR识别
func testDifferentRegions(imagePath string) {
	fmt.Printf("=== 测试不同区域的OCR识别 ===\n")

	// 测试多个区域
	regions := []Region{
		{Name: "左上角1/4", X: 0, Y: 0, WidthRatio: 0.25, HeightRatio: 0.25},
		{Name: "左上角1/2", X: 0, Y: 0, WidthRatio: 0.5, HeightRatio: 0.5},
		{Name: "上半部分", X: 0, Y: 0, WidthRatio: 1.0, HeightRatio: 0.5},
		{Name: "左半部分", X: 0, Y: 0, WidthRatio: 0.5, HeightRatio: 1.0},
		{Name: "中心区域", X: 0.25, Y: 0.25, WidthRatio: 0.5, HeightRatio: 0.5},
		{Name: "下半部分", X: 0, Y: 0.5, WidthRatio: 1.0, HeightRatio: 0.5},
	}

	for i, region := range regions {
		fmt.Printf("\n=== 测试区域 %d: %s ===\n", i+1, region.Name)

		croppedPath, err := cropRegion(imagePath, region, fmt.Sprintf("region_%d", i+1))
		if err != nil {
			fmt.Printf("❌ 裁剪失败: %v\n", err)
			continue
		}

		fmt.Printf("✅ 裁剪完成: %s\n", croppedPath)

		// OCR识别
		ocrResult := performOCR(croppedPath)
		fmt.Printf("OCR结果: [%s]\n", ocrResult)

		// 检查是否包含目标内容
		if containsTarget(ocrResult) {
			fmt.Printf("🎯 找到目标内容!\n")
			analyzeTargetContent(ocrResult)
		} else {
			fmt.Printf("❌ 未找到目标内容\n")
		}
	}
}

type Region struct {
	Name        string
	X           float64 // X起始位置比例 (0-1)
	Y           float64 // Y起始位置比例 (0-1)
	WidthRatio  float64 // 宽度比例 (0-1)
	HeightRatio float64 // 高度比例 (0-1)
}

// 裁剪指定区域
func cropRegion(imagePath string, region Region, suffix string) (string, error) {
	file, err := os.Open(imagePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return "", err
	}

	bounds := img.Bounds()
	fullWidth := bounds.Dx()
	fullHeight := bounds.Dy()

	// 计算裁剪区域
	startX := int(float64(fullWidth) * region.X)
	startY := int(float64(fullHeight) * region.Y)
	cropWidth := int(float64(fullWidth) * region.WidthRatio)
	cropHeight := int(float64(fullHeight) * region.HeightRatio)

	fmt.Printf("原图: %dx%d, 裁剪: %dx%d (从 %d,%d 开始)\n",
		fullWidth, fullHeight, cropWidth, cropHeight, startX, startY)

	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, cropWidth, cropHeight))

	for y := 0; y < cropHeight; y++ {
		for x := 0; x < cropWidth; x++ {
			srcX := startX + x
			srcY := startY + y
			if srcX < fullWidth && srcY < fullHeight {
				croppedImg.Set(x, y, img.At(srcX, srcY))
			}
		}
	}

	// 保存裁剪后的图片
	outputPath := fmt.Sprintf("pic/ocr_%s.png", suffix)
	os.MkdirAll("pic", 0755)

	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outputFile.Close()

	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", err
	}

	return outputPath, nil
}

// 执行OCR识别
func performOCR(imagePath string) string {
	// 检查tesseract是否可用
	if _, err := exec.LookPath("tesseract"); err != nil {
		return "Tesseract不可用"
	}

	// 执行tesseract命令
	cmd := exec.Command("tesseract", imagePath, "stdout", "-l", "chi_sim+eng", "--psm", "6")
	output, err := cmd.Output()
	if err != nil {
		// 尝试只用英文
		cmd = exec.Command("tesseract", imagePath, "stdout", "-l", "eng", "--psm", "6")
		output, err = cmd.Output()
		if err != nil {
			return "OCR识别失败"
		}
	}

	return strings.TrimSpace(string(output))
}

// 检查是否包含目标内容
func containsTarget(text string) bool {
	// 检查是否包含数字模式
	patterns := []string{
		"0.000",
		"O.000",
		"0.00O",
		"0.0O0",
	}

	for _, pattern := range patterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}

	// 检查是否包含相关关键词
	keywords := []string{
		"腹部",
		"胆囊",
		"腰椎",
		"水平",
		"截面",
	}

	for _, keyword := range keywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}

	return false
}

// 分析目标内容
func analyzeTargetContent(text string) {
	fmt.Printf("=== 目标内容分析 ===\n")
	fmt.Printf("识别文本: [%s]\n", text)

	// 按行分析
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fmt.Printf("第%d行: [%s]\n", i+1, line)

		// 检查是否是目标行
		if strings.Contains(line, "0.000") || strings.Contains(line, "O.000") || strings.Contains(line, "0.00O") {
			fmt.Printf("  🎯 这是目标行!\n")

			// 提取器官名称
			organName := extractOrganFromText(line)
			fmt.Printf("  提取器官: [%s]\n", organName)

			// 应用校正
			corrected := applyOCRCorrection(organName)
			if corrected != organName {
				fmt.Printf("  校正结果: [%s]\n", corrected)
			}
		}
	}
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	if !strings.Contains(text, "0.000") && !strings.Contains(text, "O.000") && !strings.Contains(text, "0.00O") {
		return "未知器官"
	}

	text = strings.TrimSpace(text)
	text = strings.ReplaceAll(text, "O.000", "0.000")
	text = strings.ReplaceAll(text, "0.00O", "0.000")
	text = strings.ReplaceAll(text, "0.0O0", "0.000")

	pattern := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	matches := pattern.FindStringSubmatch(text)

	if len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		return organName
	}

	return "未知器官"
}

// 应用OCR校正规则
func applyOCRCorrection(organName string) string {
	corrections := map[string]string{
		"胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"服部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"复部第1腰椎水平截面": "腹部第1腰椎水平截面",
	}

	if corrected, exists := corrections[organName]; exists {
		fmt.Printf("    🔧 校正: %s → %s\n", organName, corrected)
		return corrected
	}

	return organName
}

func main() {
	fmt.Println("=== OCR区域识别程序 ===")

	imagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`

	// 检查文件
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 图片文件不存在\n")
			return
		}
		imagePath = relativePath
	}

	fmt.Printf("✅ 找到图片: %s\n", imagePath)

	// 显示文件信息
	if info, err := os.Stat(imagePath); err == nil {
		fmt.Printf("文件大小: %.2f KB\n", float64(info.Size())/1024)
	}

	// 测试不同区域
	testDifferentRegions(imagePath)
}
