package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"regexp"
	"strings"

	"github.com/otiai10/gosseract/v2"
)

// OCR器官名称提取测试（模拟版本）
func testOCRWithSampleImage() {
	fmt.Println("=== OCR器官名称提取测试（模拟版本） ===")
	fmt.Println("基于您提供的图片内容进行测试")
	fmt.Println("图片描述：标准图谱分析窗口，包含相似度递减列表")

	// 模拟您提供的图片中左上角1/4区域可能包含的文本内容
	fmt.Println("\n=== 模拟OCR识别的文本内容 ===")

	// 根据您的图片，左上角1/4区域主要包含：
	// 1. 标题部分："标准图谱分析窗口"
	// 2. 表格开始部分，包含"0.000"行
	sampleTexts := []string{
		// 完整的表格行（最重要的测试用例）
		"0.000 胆囊第1腰椎水平截面",

		// 可能的OCR识别变体
		"0.000胆囊第1腰椎水平截面",
		"0.000  胆囊第1腰椎水平截面",
		"| 0.000 | 胆囊第1腰椎水平截面 |",

		// 其他可能的行
		"4.479 优化配置",
		"0.054 C反应蛋白C-REACTIVE PROTEIN",
		"0.072 血尿酸SERUM URIC ACID",

		// 多行文本测试
		`按照标准图谱相似度递减列表:
0.000 胆囊第1腰椎水平截面
4.479 优化配置`,

		// 表格格式测试
		`| 数值 | 器官名称 |
| 0.000 | 胆囊第1腰椎水平截面 |
| 4.479 | 优化配置 |`,
	}

	fmt.Printf("共 %d 个测试用例\n", len(sampleTexts))

	for i, text := range sampleTexts {
		fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
		fmt.Printf("测试用例 %d:\n", i+1)
		fmt.Printf("原文本: [%s]\n", text)

		organName := extractOrganFromText(text)
		fmt.Printf("提取结果: [%s]\n", organName)

		if organName != "未知器官" && organName != "" {
			fmt.Printf("✅ 成功提取器官名称\n")
		} else {
			fmt.Printf("❌ 未能提取器官名称\n")
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 50) + "\n")
	fmt.Println("测试完成！")
}

// 测试实际图片文件
func testRealImage(client *gosseract.Client, imagePath string) {
	// 1. 裁剪左上角1/4区域
	croppedPath, err := cropTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("裁剪图片失败: %v\n", err)
		return
	}
	fmt.Printf("裁剪完成: %s\n", croppedPath)

	// 2. OCR识别
	err = client.SetImage(croppedPath)
	if err != nil {
		fmt.Printf("设置图片失败: %v\n", err)
		return
	}

	text, err := client.Text()
	if err != nil {
		fmt.Printf("OCR识别失败: %v\n", err)
		return
	}

	fmt.Printf("OCR识别文本:\n%s\n", text)

	// 3. 提取器官名称
	organName := extractOrganFromText(text)
	fmt.Printf("最终提取的器官名称: [%s]\n", organName)
}

// 裁剪图片左上角1/4区域
func cropTopLeftQuarter(imagePath string) (string, error) {
	// 打开原图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return "", err
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算左上角1/4区域
	quarterWidth := width / 4
	quarterHeight := height / 4

	fmt.Printf("原图尺寸: %dx%d\n", width, height)
	fmt.Printf("裁剪区域: %dx%d (左上角1/4)\n", quarterWidth, quarterHeight)

	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))

	// 复制像素
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}

	// 保存裁剪后的图片
	outputPath := "pic/cropped_quarter.png"
	os.MkdirAll("pic", 0755)

	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outputFile.Close()

	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", err
	}

	return outputPath, nil
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	// 清理文本
	text = strings.TrimSpace(text)

	// 查找包含 "0.000" 的部分
	if strings.Contains(text, "0.000") {
		return extractOrganFromLine(text)
	}

	// 按行处理
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找包含 "0.000" 的行
		if strings.Contains(line, "0.000") {
			return extractOrganFromLine(line)
		}

		// 也检查包含类似数字的行
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, line); matched {
			result := extractOrganFromLine(line)
			if result != "" {
				return result
			}
		}
	}

	return "未知器官"
}

// 从单行中提取器官名称
func extractOrganFromLine(line string) string {
	// 移除多余空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	fmt.Printf("分析行: [%s]\n", line)

	// 模式1: 数字后直接跟器官名称
	pattern1 := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		// 清理可能的英文部分
		organName = cleanOrganName(organName)
		fmt.Printf("模式1提取: [%s]\n", organName)
		return organName
	}

	// 模式2: 表格格式
	pattern2 := regexp.MustCompile(`\|\s*[0-9]\.[0-9]{3}\s*\|\s*(.+?)\s*\|`)
	if matches := pattern2.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		fmt.Printf("模式2提取: [%s]\n", organName)
		return organName
	}

	// 模式3: 简单分割
	parts := strings.Fields(line)
	for i, part := range parts {
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, part); matched {
			if i+1 < len(parts) {
				organParts := parts[i+1:]
				organName := strings.Join(organParts, " ")
				organName = cleanOrganName(organName)
				fmt.Printf("模式3提取: [%s]\n", organName)
				return organName
			}
		}
	}

	return ""
}

// 清理器官名称
func cleanOrganName(organName string) string {
	// 移除常见的英文描述
	organName = regexp.MustCompile(`\s*[A-Z\s-]+$`).ReplaceAllString(organName, "")

	// 移除多余的空格
	organName = strings.TrimSpace(organName)

	return organName
}

func main() {
	testOCRWithSampleImage()
}
