package main

import (
	"context"
	"fmt"
	"log"

	"MagneticOperator/app/services"
)

func main() {
	fmt.Println("=== 优化文件命名功能测试 ===")

	// 创建配置服务
	ctx := context.Background()
	configService := services.NewConfigService(ctx)
	
	// 创建截图服务
	screenshotService := services.NewScreenshotService(configService)
	
	fmt.Println("1. 测试OCR识别器官名称...")
	
	// 测试OCR识别器官名称
	_, organName, err := screenshotService.TakeOCRRegionScreenshot("生化平衡分析", "张三")
	if err != nil {
		log.Printf("OCR识别失败: %v", err)
		organName = "心脏" // 使用模拟器官名称进行测试
	}
	fmt.Printf("✓ 识别到器官名称: %s\n", organName)
	
	fmt.Println("\n2. 测试优化文件命名...")
	
	// 测试优化文件命名
	filePath, err := screenshotService.TakeScreenshotWithOptimizedNaming("生化平衡分析", "张三", organName, nil)
	if err != nil {
		log.Printf("优化命名截图失败: %v", err)
	} else {
		fmt.Printf("✓ 优化命名截图成功\n")
		fmt.Printf("  文件路径: %s\n", filePath)
		
		// 从文件路径中提取文件名
		fileName := filePath[4:] // 移除 "pic/" 前缀
		fmt.Printf("  文件名: %s\n", fileName)
		
		// 分析文件名格式
		fmt.Println("\n📋 文件名格式分析:")
		fmt.Println("  格式: 用户名_器官部位_检测报告号_轮次_列表类型.png")
		fmt.Printf("  实际: %s\n", fileName)
		
		// 验证文件名是否符合预期格式
		if len(fileName) > 0 && fileName[len(fileName)-4:] == ".png" {
			fmt.Println("  ✓ 文件扩展名正确")
		}
		
		// 检查是否包含器官名称
		if organName != "未知器官" && len(organName) > 0 {
			fmt.Printf("  ✓ 包含器官名称: %s\n", organName)
		}
	}
	
	fmt.Println("\n3. 测试不同模式的文件命名...")
	
	// 测试不同模式
	modes := []string{"器官问题来源分析", "生化平衡分析", "病理形态学分析"}
	users := []string{"李四", "王五"}
	organs := []string{"肝脏", "肺"}
	
	for i, mode := range modes {
		userName := users[i%len(users)]
		organName := organs[i%len(organs)]
		
		fmt.Printf("\n  测试 %s - 用户: %s, 器官: %s\n", mode, userName, organName)
		
		filePath, err := screenshotService.TakeScreenshotWithOptimizedNaming(mode, userName, organName, nil)
		if err != nil {
			log.Printf("    失败: %v", err)
		} else {
			fileName := filePath[4:] // 移除 "pic/" 前缀
			fmt.Printf("    ✓ 文件名: %s\n", fileName)
		}
	}
	
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n🎯 文件命名规则说明:")
	fmt.Println("格式: 用户名_器官部位_检测报告号_轮次_列表类型.png")
	fmt.Println("示例:")
	fmt.Println("  张三_心脏_RPT20240616001_R01_B02.png")
	fmt.Println("  张三_心脏_RPT20240616001_R01_C03.png")
	fmt.Println("  张三_肝脏_RPT20240616001_R02_B02.png")
	fmt.Println("\n📝 字段说明:")
	fmt.Println("  用户名: 当前操作用户")
	fmt.Println("  器官部位: OCR识别的器官名称")
	fmt.Println("  检测报告号: RPT+日期+序号")
	fmt.Println("  轮次: R01-R10 (当前固定R01)")
	fmt.Println("  列表类型: A01/B02/C03 (对应不同分析模式)")
}
