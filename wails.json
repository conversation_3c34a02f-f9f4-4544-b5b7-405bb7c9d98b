{"$schema": "https://wails.io/schemas/config.v2.json", "name": "MagneticOperator", "outputfilename": "MagneticOperator", "frontend:install": "npm install", "frontend:build": "npm run build", "frontend:dev:watcher": "npm run dev", "frontend:dev:serverUrl": "auto", "build": {"dir": "build", "appName": "MagneticOperator", "assetdir": "./build", "reloaddirs": "frontend/src", "preBuildHooks": {"command": "echo 'Pre-build hook'"}, "postBuildHooks": {"command": "copy config\\app_config.json build\\bin\\config\\app_config.json"}}, "author": {"name": "aylcf", "email": "<EMAIL>"}}