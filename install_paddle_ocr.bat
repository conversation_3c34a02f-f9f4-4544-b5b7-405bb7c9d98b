@echo off
echo ===== PaddleOCR 安装脚本 =====
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或不在PATH中
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装PaddlePaddle...
pip install paddlepaddle

echo.
echo 安装PaddleOCR...
pip install paddleocr

echo.
echo 验证安装...
python -c "import paddleocr; print('PaddleOCR安装成功! 版本:', paddleocr.__version__)"

if %errorlevel% equ 0 (
    echo.
    echo ===== 安装完成 =====
    echo 现在可以运行: go run paddle_ocr_solution.go
) else (
    echo.
    echo ===== 安装失败 =====
    echo 请检查网络连接或手动安装
)

echo.
pause
