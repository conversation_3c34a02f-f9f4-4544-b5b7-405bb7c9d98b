package main

import (
	"fmt"
	"strings"
	"time"
)

// 模拟文件命名逻辑
func generateOptimizedFilename(mode string, userName string, organName string) string {
	// 获取模式代码
	var modeCode string
	switch mode {
	case "器官问题来源分析":
		modeCode = "A01"
	case "生化平衡分析":
		modeCode = "B02"
	case "病理形态学分析":
		modeCode = "C03"
	default:
		modeCode = "A01"
	}

	// 清理用户名（移除特殊字符）
	cleanUserName := cleanFilenameString(userName)
	
	// 清理器官名称
	cleanOrganName := cleanFilenameString(organName)
	
	// 生成检测报告号：RPT + 日期 + 序号
	timestamp := time.Now()
	reportNumber := fmt.Sprintf("RPT%s001", timestamp.Format("20060102"))
	
	// 轮次（目前固定为R01，后续可以根据实际需求动态生成）
	roundNumber := "R01"
	
	// 列表类型（从模式代码中提取）
	listType := modeCode
	
	// 生成最终文件名
	// 格式：用户名_器官部位_检测报告号_轮次_列表类型.png
	filename := fmt.Sprintf("%s_%s_%s_%s_%s.png", 
		cleanUserName, cleanOrganName, reportNumber, roundNumber, listType)
	
	return filename
}

// 清理文件名字符串，移除不适合文件名的字符
func cleanFilenameString(input string) string {
	if input == "" {
		return "未知"
	}
	
	// 移除或替换不适合文件名的字符
	cleaned := strings.ReplaceAll(input, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "/", "_")
	cleaned = strings.ReplaceAll(cleaned, "\\", "_")
	cleaned = strings.ReplaceAll(cleaned, ":", "_")
	cleaned = strings.ReplaceAll(cleaned, "*", "_")
	cleaned = strings.ReplaceAll(cleaned, "?", "_")
	cleaned = strings.ReplaceAll(cleaned, "\"", "_")
	cleaned = strings.ReplaceAll(cleaned, "<", "_")
	cleaned = strings.ReplaceAll(cleaned, ">", "_")
	cleaned = strings.ReplaceAll(cleaned, "|", "_")
	
	// 如果清理后为空，返回默认值
	if cleaned == "" {
		return "未知"
	}
	
	return cleaned
}

func main() {
	fmt.Println("=== 文件命名规则测试 ===")
	fmt.Println("文件命名规则：用户名_器官部位_检测报告号_轮次_列表类型.png")
	fmt.Println()

	// 测试用例
	testCases := []struct {
		mode     string
		userName string
		organName string
		expected string
	}{
		{"生化平衡分析", "张三", "心脏", "张三_心脏_RPT20240616001_R01_B02.png"},
		{"病理形态学分析", "张三", "心脏", "张三_心脏_RPT20240616001_R01_C03.png"},
		{"生化平衡分析", "张三", "肝脏", "张三_肝脏_RPT20240616001_R01_B02.png"},
		{"器官问题来源分析", "李四", "肺", "李四_肺_RPT20240616001_R01_A01.png"},
		{"生化平衡分析", "王五", "肾脏", "王五_肾脏_RPT20240616001_R01_B02.png"},
		{"病理形态学分析", "医生或健康专家", "未知器官", "医生或健康专家_未知器官_RPT20240616001_R01_C03.png"},
	}

	fmt.Println("📋 测试结果:")
	for i, tc := range testCases {
		result := generateOptimizedFilename(tc.mode, tc.userName, tc.organName)
		fmt.Printf("%d. 模式: %s\n", i+1, tc.mode)
		fmt.Printf("   用户: %s, 器官: %s\n", tc.userName, tc.organName)
		fmt.Printf("   生成文件名: %s\n", result)
		
		// 验证文件名格式
		parts := strings.Split(strings.TrimSuffix(result, ".png"), "_")
		if len(parts) == 5 {
			fmt.Printf("   ✓ 格式正确: %s_%s_%s_%s_%s.png\n", parts[0], parts[1], parts[2], parts[3], parts[4])
		} else {
			fmt.Printf("   ❌ 格式错误: 应该有5个部分，实际有%d个\n", len(parts))
		}
		fmt.Println()
	}

	fmt.Println("🎯 文件命名规则说明:")
	fmt.Println("1. 用户名: 当前操作用户名称")
	fmt.Println("2. 器官部位: OCR识别的器官名称")
	fmt.Println("3. 检测报告号: RPT+日期+序号 (如: RPT20240616001)")
	fmt.Println("4. 轮次: R01-R10 (当前固定为R01)")
	fmt.Println("5. 列表类型: A01(器官问题来源分析)/B02(生化平衡分析)/C03(病理形态学分析)")
	
	fmt.Println("\n✨ 优化效果:")
	fmt.Println("- 文件名包含器官信息，便于识别和管理")
	fmt.Println("- 统一的命名格式，便于后续数据库管理")
	fmt.Println("- 支持轮次标识，为10轮检测做准备")
	fmt.Println("- 自动清理特殊字符，确保文件名合法")
}
