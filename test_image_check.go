package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// 检查并分析图片文件
func analyzeImageFile(imagePath string) {
	fmt.Printf("=== 图片文件分析 ===\n")
	fmt.Printf("目标图片: %s\n", imagePath)

	// 检查文件是否存在
	fileInfo, err := os.Stat(imagePath)
	if err != nil {
		fmt.Printf("❌ 文件不存在: %v\n", err)
		return
	}

	fmt.Printf("✅ 文件存在\n")
	fmt.Printf("文件大小: %.2f KB\n", float64(fileInfo.Size())/1024)
	fmt.Printf("修改时间: %s\n", fileInfo.ModTime().Format("2006-01-02 15:04:05"))

	// 尝试打开并分析图片
	file, err := os.Open(imagePath)
	if err != nil {
		fmt.Printf("❌ 无法打开文件: %v\n", err)
		return
	}
	defer file.Close()

	// 解码图片获取基本信息
	img, format, err := image.Decode(file)
	if err != nil {
		fmt.Printf("❌ 无法解码图片: %v\n", err)
		return
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	fmt.Printf("✅ 图片解码成功\n")
	fmt.Printf("图片格式: %s\n", format)
	fmt.Printf("图片尺寸: %d x %d 像素\n", width, height)

	// 计算左上角1/4区域
	quarterWidth := width / 4
	quarterHeight := height / 4
	fmt.Printf("左上角1/4区域: %d x %d 像素\n", quarterWidth, quarterHeight)

	// 裁剪左上角1/4区域
	fmt.Printf("\n=== 裁剪左上角1/4区域 ===\n")
	croppedPath, err := cropTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("❌ 裁剪失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 裁剪成功\n")
	fmt.Printf("裁剪后图片保存到: %s\n", croppedPath)

	// 显示裁剪后的图片信息
	if croppedInfo, err := os.Stat(croppedPath); err == nil {
		fmt.Printf("裁剪后文件大小: %.2f KB\n", float64(croppedInfo.Size())/1024)
	}

	// 模拟OCR识别过程
	fmt.Printf("\n=== 模拟OCR识别 ===\n")
	simulateOCRRecognition(croppedPath)
}

// 裁剪图片左上角1/4区域
func cropTopLeftQuarter(imagePath string) (string, error) {
	// 打开原图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图片失败: %v", err)
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算左上角1/4区域
	quarterWidth := width / 4
	quarterHeight := height / 4

	fmt.Printf("原图尺寸: %dx%d\n", width, height)
	fmt.Printf("裁剪区域: %dx%d (左上角1/4)\n", quarterWidth, quarterHeight)

	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))

	// 复制像素
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}

	// 保存裁剪后的图片
	outputPath := "pic/cropped_quarter_test.png"
	os.MkdirAll("pic", 0755)

	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", fmt.Errorf("保存裁剪图片失败: %v", err)
	}

	return outputPath, nil
}

// 模拟OCR识别过程
func simulateOCRRecognition(imagePath string) {
	fmt.Printf("正在分析裁剪后的图片: %s\n", imagePath)

	// 基于您之前提到的实际内容，模拟可能的OCR识别结果
	possibleResults := []string{
		"0.000 腹部第1腰椎水平截面", // 正确的内容
		"0.000 胆囊第1腰椎水平截面", // 可能的误识别
		"0.000 胆部第1腰椎水平截面", // 部分误识别
		"0.000 腹囊第1腰椎水平截面", // 部分误识别
		"O.000 腹部第1腰椎水平截面", // 数字误识别
		"0.00O 腹部第1腰椎水平截面", // 数字误识别
	}

	fmt.Printf("\n可能的OCR识别结果:\n")
	for i, result := range possibleResults {
		fmt.Printf("%d. %s\n", i+1, result)

		// 测试我们的提取算法
		organName := extractOrganFromText(result)
		fmt.Printf("   → 提取的器官名称: [%s]\n", organName)

		if i == 0 {
			fmt.Printf("   ✅ 这是期望的正确结果\n")
		} else {
			fmt.Printf("   ⚠️  这是可能的误识别结果\n")
		}
		fmt.Println()
	}

	fmt.Printf("=== OCR校正建议 ===\n")
	fmt.Printf("如果OCR识别出现误识别，建议:\n")
	fmt.Printf("1. 使用OCR结果校正器\n")
	fmt.Printf("2. 建立常见误识别字符对照表\n")
	fmt.Printf("3. 使用医疗术语词典进行校正\n")
	fmt.Printf("4. 提高图片质量和分辨率\n")
}

// 从文本中提取器官名称（简化版本）
func extractOrganFromText(text string) string {
	// 查找包含 "0.000" 的模式
	if !strings.Contains(text, "0.000") && !strings.Contains(text, "O.000") && !strings.Contains(text, "0.00O") {
		return "未知器官"
	}

	// 清理文本
	text = strings.TrimSpace(text)

	// 修正常见的数字误识别
	text = strings.ReplaceAll(text, "O.000", "0.000")
	text = strings.ReplaceAll(text, "0.00O", "0.000")
	text = strings.ReplaceAll(text, "0.0O0", "0.000")

	// 使用正则表达式提取器官名称
	pattern := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	matches := pattern.FindStringSubmatch(text)

	if len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])

		// 应用OCR校正规则
		organName = applyOCRCorrection(organName)

		return organName
	}

	return "未知器官"
}

// 应用OCR校正规则
func applyOCRCorrection(organName string) string {
	// 常见误识别校正
	corrections := map[string]string{
		"胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"服部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"复部第1腰椎水平截面": "腹部第1腰椎水平截面",
	}

	if corrected, exists := corrections[organName]; exists {
		fmt.Printf("   🔧 应用校正: %s → %s\n", organName, corrected)
		return corrected
	}

	return organName
}

func main() {
	fmt.Println("=== OCR图片分析测试程序 ===")

	// 测试指定的图片文件
	testImagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`

	// 检查绝对路径
	if _, err := os.Stat(testImagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 绝对路径不存在: %s\n", testImagePath)

		// 尝试相对路径
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 相对路径也不存在: %s\n", relativePath)

			// 列出pic目录下的文件
			fmt.Printf("\n📁 pic目录下的文件:\n")
			if files, err := filepath.Glob("pic/*.png"); err == nil {
				for _, file := range files {
					fmt.Printf("  - %s\n", file)
				}
			}
			return
		} else {
			fmt.Printf("✅ 使用相对路径: %s\n", relativePath)
			testImagePath = relativePath
		}
	} else {
		fmt.Printf("✅ 使用绝对路径: %s\n", testImagePath)
	}

	// 开始分析
	analyzeImageFile(testImagePath)
}
