# 快速测试指南

## 🎯 测试目标

验证轮次命名修复是否正确工作。

## 📋 测试步骤

### 1. **构建应用**
```bash
# 进入项目目录
cd f:\myHbuilderAPP\MagneticOperator

# 构建应用（不包含OCR，避免依赖问题）
wails build

# 或者使用批处理文件
build_no_ocr.bat
```

### 2. **启动应用**
```bash
# 运行构建的应用
.\build\bin\MagneticOperator.exe
```

### 3. **进行截图测试**

#### 方法1: 使用快捷键
- 按 `Ctrl+Shift+B` 进行第一次生化平衡分析截图
- 再次按 `Ctrl+Shift+B` 进行第二次截图
- 继续按几次，观察轮次变化

#### 方法2: 使用前端界面
- 在应用界面中点击相应的截图按钮
- 重复点击，观察文件命名变化

### 4. **检查文件命名**

查看 `pic` 目录下生成的文件：

**期望的文件命名格式：**
```
第1次: 医生或健康专家_未知器官_RPT20250616001_R01_B02.png
第2次: 医生或健康专家_未知器官_RPT20250616001_R02_B02.png
第3次: 医生或健康专家_未知器官_RPT20250616001_R03_B02.png
...
```

### 5. **观察控制台输出**

应该看到类似的调试信息：
```
[DEBUG] 当前轮次: R01
[DEBUG] 轮次已更新，下次截图将使用: R02

[DEBUG] 当前轮次: R02
[DEBUG] 轮次已更新，下次截图将使用: R03
```

## ✅ 验证要点

### 1. **轮次递增**
- [x] 第一次截图显示 R01
- [x] 第二次截图显示 R02
- [x] 轮次正确递增到 R10

### 2. **轮次限制**
- [x] 超过10轮后保持在 R10
- [x] 不会超过最大轮次

### 3. **用户隔离**
- [x] 不同用户的轮次独立计算
- [x] 同一用户的轮次连续递增

### 4. **日期重置**
- [x] 新的一天轮次从 R01 重新开始

## 🐛 故障排除

### 问题1: 轮次仍然显示 R01
**可能原因：** 使用了旧版本的应用
**解决方案：** 重新构建应用并确保使用新构建的版本

### 问题2: 应用启动失败
**可能原因：** 构建过程中出现错误
**解决方案：** 检查构建日志，确保所有依赖正确安装

### 问题3: OCR相关错误
**可能原因：** 使用了包含OCR的构建但未安装Tesseract
**解决方案：** 使用 `build_no_ocr.bat` 构建无OCR版本

## 📊 测试结果记录

| 测试项目 | 期望结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| 第1次截图 | R01 | _____ | ⬜ |
| 第2次截图 | R02 | _____ | ⬜ |
| 第3次截图 | R03 | _____ | ⬜ |
| 轮次递增 | 正常递增 | _____ | ⬜ |
| 最大轮次 | R10 | _____ | ⬜ |

## 🎉 测试完成

如果所有测试项目都通过，说明轮次命名修复成功！

## 📞 技术支持

如果测试过程中遇到问题：
1. 检查控制台输出的调试信息
2. 查看 `logs/operation.log` 日志文件
3. 确认使用的是最新构建的应用版本
