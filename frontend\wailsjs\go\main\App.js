// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddPatient(arg1) {
  return window['go']['main']['App']['AddPatient'](arg1);
}

export function AutoCollapseAfterInactivity() {
  return window['go']['main']['App']['AutoCollapseAfterInactivity']();
}

export function ClearPatientList() {
  return window['go']['main']['App']['ClearPatientList']();
}

export function ExtractOrganFromScreenshot(arg1) {
  return window['go']['main']['App']['ExtractOrganFromScreenshot'](arg1);
}

export function GenerateQRCode() {
  return window['go']['main']['App']['GenerateQRCode']();
}

export function GenerateRegistrationQRCode() {
  return window['go']['main']['App']['GenerateRegistrationQRCode']();
}

export function GetConfig() {
  return window['go']['main']['App']['GetConfig']();
}

export function GetCurrentRegistrationNumber() {
  return window['go']['main']['App']['GetCurrentRegistrationNumber']();
}

export function GetModeConfig() {
  return window['go']['main']['App']['GetModeConfig']();
}

export function GetPatientList() {
  return window['go']['main']['App']['GetPatientList']();
}

export function GetRegistrations() {
  return window['go']['main']['App']['GetRegistrations']();
}

export function GetSiteInfo() {
  return window['go']['main']['App']['GetSiteInfo']();
}

export function GetTodayPatientCount() {
  return window['go']['main']['App']['GetTodayPatientCount']();
}

export function GetWindowState() {
  return window['go']['main']['App']['GetWindowState']();
}

export function Greet(arg1) {
  return window['go']['main']['App']['Greet'](arg1);
}

export function HandleHotkey(arg1) {
  return window['go']['main']['App']['HandleHotkey'](arg1);
}

export function HandleKeyboardShortcut(arg1) {
  return window['go']['main']['App']['HandleKeyboardShortcut'](arg1);
}

export function MinimizeWindow() {
  return window['go']['main']['App']['MinimizeWindow']();
}

export function ProcessScreenshotAndUpload(arg1, arg2) {
  return window['go']['main']['App']['ProcessScreenshotAndUpload'](arg1, arg2);
}

export function ProcessScreenshotWithOCR(arg1, arg2) {
  return window['go']['main']['App']['ProcessScreenshotWithOCR'](arg1, arg2);
}

export function RemovePatient(arg1) {
  return window['go']['main']['App']['RemovePatient'](arg1);
}

export function SetAlwaysOnTop(arg1) {
  return window['go']['main']['App']['SetAlwaysOnTop'](arg1);
}

export function SetWindowPosition(arg1) {
  return window['go']['main']['App']['SetWindowPosition'](arg1);
}

export function TakeOCRScreenshot(arg1, arg2) {
  return window['go']['main']['App']['TakeOCRScreenshot'](arg1, arg2);
}

export function TestOCR(arg1) {
  return window['go']['main']['App']['TestOCR'](arg1);
}

export function ToggleWindowSize() {
  return window['go']['main']['App']['ToggleWindowSize']();
}

export function UpdateCropSettings(arg1) {
  return window['go']['main']['App']['UpdateCropSettings'](arg1);
}

export function UpdateSiteInfo(arg1) {
  return window['go']['main']['App']['UpdateSiteInfo'](arg1);
}

export function ValidateOCRSetup() {
  return window['go']['main']['App']['ValidateOCRSetup']();
}
