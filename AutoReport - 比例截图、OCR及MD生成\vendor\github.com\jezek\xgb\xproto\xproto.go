// Package xproto is the X client API for the  extension.
package xproto

// This file is automatically generated from xproto.xml. Edit at your peril!

import (
	"github.com/jezek/xgb"
)

// Setup parses the setup bytes retrieved when
// connecting into a SetupInfo struct.
func Setup(c *xgb.Conn) *SetupInfo {
	setup := new(SetupInfo)
	SetupInfoRead(c.SetupBytes, setup)
	return setup
}

// DefaultScreen gets the default screen info from SetupInfo.
func (s *SetupInfo) DefaultScreen(c *xgb.Conn) *ScreenInfo {
	return &s.Roots[c.DefaultScreen]
}

// BadAccess is the error number for a BadAccess.
const BadAccess = 10

type AccessError RequestError

// AccessErrorNew constructs a AccessError value that implements xgb.Error from a byte slice.
func AccessErrorNew(buf []byte) xgb.Error {
	v := AccessError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Access"
	return v
}

// SequenceId returns the sequence id attached to the BadAccess error.
// This is mostly used internally.
func (err AccessError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadAccess error. If no bad value exists, 0 is returned.
func (err AccessError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadAccess error.
func (err AccessError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadAccess {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[10] = AccessErrorNew
}

const (
	AccessControlDisable = 0
	AccessControlEnable  = 1
)

// BadAlloc is the error number for a BadAlloc.
const BadAlloc = 11

type AllocError RequestError

// AllocErrorNew constructs a AllocError value that implements xgb.Error from a byte slice.
func AllocErrorNew(buf []byte) xgb.Error {
	v := AllocError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Alloc"
	return v
}

// SequenceId returns the sequence id attached to the BadAlloc error.
// This is mostly used internally.
func (err AllocError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadAlloc error. If no bad value exists, 0 is returned.
func (err AllocError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadAlloc error.
func (err AllocError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadAlloc {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[11] = AllocErrorNew
}

const (
	AllowAsyncPointer   = 0
	AllowSyncPointer    = 1
	AllowReplayPointer  = 2
	AllowAsyncKeyboard  = 3
	AllowSyncKeyboard   = 4
	AllowReplayKeyboard = 5
	AllowAsyncBoth      = 6
	AllowSyncBoth       = 7
)

type Arc struct {
	X      int16
	Y      int16
	Width  uint16
	Height uint16
	Angle1 int16
	Angle2 int16
}

// ArcRead reads a byte slice into a Arc value.
func ArcRead(buf []byte, v *Arc) int {
	b := 0

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.Angle1 = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Angle2 = int16(xgb.Get16(buf[b:]))
	b += 2

	return b
}

// ArcReadList reads a byte slice into a list of Arc values.
func ArcReadList(buf []byte, dest []Arc) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Arc{}
		b += ArcRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Arc value to a byte slice.
func (v Arc) Bytes() []byte {
	buf := make([]byte, 12)
	b := 0

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], uint16(v.Angle1))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Angle2))
	b += 2

	return buf[:b]
}

// ArcListBytes writes a list of Arc values to a byte slice.
func ArcListBytes(buf []byte, list []Arc) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

const (
	ArcModeChord    = 0
	ArcModePieSlice = 1
)

type Atom uint32

func NewAtomId(c *xgb.Conn) (Atom, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Atom(id), nil
}

const (
	AtomNone               = 0
	AtomAny                = 0
	AtomPrimary            = 1
	AtomSecondary          = 2
	AtomArc                = 3
	AtomAtom               = 4
	AtomBitmap             = 5
	AtomCardinal           = 6
	AtomColormap           = 7
	AtomCursor             = 8
	AtomCutBuffer0         = 9
	AtomCutBuffer1         = 10
	AtomCutBuffer2         = 11
	AtomCutBuffer3         = 12
	AtomCutBuffer4         = 13
	AtomCutBuffer5         = 14
	AtomCutBuffer6         = 15
	AtomCutBuffer7         = 16
	AtomDrawable           = 17
	AtomFont               = 18
	AtomInteger            = 19
	AtomPixmap             = 20
	AtomPoint              = 21
	AtomRectangle          = 22
	AtomResourceManager    = 23
	AtomRgbColorMap        = 24
	AtomRgbBestMap         = 25
	AtomRgbBlueMap         = 26
	AtomRgbDefaultMap      = 27
	AtomRgbGrayMap         = 28
	AtomRgbGreenMap        = 29
	AtomRgbRedMap          = 30
	AtomString             = 31
	AtomVisualid           = 32
	AtomWindow             = 33
	AtomWmCommand          = 34
	AtomWmHints            = 35
	AtomWmClientMachine    = 36
	AtomWmIconName         = 37
	AtomWmIconSize         = 38
	AtomWmName             = 39
	AtomWmNormalHints      = 40
	AtomWmSizeHints        = 41
	AtomWmZoomHints        = 42
	AtomMinSpace           = 43
	AtomNormSpace          = 44
	AtomMaxSpace           = 45
	AtomEndSpace           = 46
	AtomSuperscriptX       = 47
	AtomSuperscriptY       = 48
	AtomSubscriptX         = 49
	AtomSubscriptY         = 50
	AtomUnderlinePosition  = 51
	AtomUnderlineThickness = 52
	AtomStrikeoutAscent    = 53
	AtomStrikeoutDescent   = 54
	AtomItalicAngle        = 55
	AtomXHeight            = 56
	AtomQuadWidth          = 57
	AtomWeight             = 58
	AtomPointSize          = 59
	AtomResolution         = 60
	AtomCopyright          = 61
	AtomNotice             = 62
	AtomFontName           = 63
	AtomFamilyName         = 64
	AtomFullName           = 65
	AtomCapHeight          = 66
	AtomWmClass            = 67
	AtomWmTransientFor     = 68
)

// BadAtom is the error number for a BadAtom.
const BadAtom = 5

type AtomError ValueError

// AtomErrorNew constructs a AtomError value that implements xgb.Error from a byte slice.
func AtomErrorNew(buf []byte) xgb.Error {
	v := AtomError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Atom"
	return v
}

// SequenceId returns the sequence id attached to the BadAtom error.
// This is mostly used internally.
func (err AtomError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadAtom error. If no bad value exists, 0 is returned.
func (err AtomError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadAtom error.
func (err AtomError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadAtom {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[5] = AtomErrorNew
}

const (
	AutoRepeatModeOff     = 0
	AutoRepeatModeOn      = 1
	AutoRepeatModeDefault = 2
)

const (
	BackPixmapNone           = 0
	BackPixmapParentRelative = 1
)

const (
	BackingStoreNotUseful  = 0
	BackingStoreWhenMapped = 1
	BackingStoreAlways     = 2
)

const (
	BlankingNotPreferred = 0
	BlankingPreferred    = 1
	BlankingDefault      = 2
)

type Bool32 uint32

type Button byte

const (
	ButtonIndexAny = 0
	ButtonIndex1   = 1
	ButtonIndex2   = 2
	ButtonIndex3   = 3
	ButtonIndex4   = 4
	ButtonIndex5   = 5
)

const (
	ButtonMask1   = 256
	ButtonMask2   = 512
	ButtonMask3   = 1024
	ButtonMask4   = 2048
	ButtonMask5   = 4096
	ButtonMaskAny = 32768
)

// ButtonPress is the event number for a ButtonPressEvent.
const ButtonPress = 4

type ButtonPressEvent struct {
	Sequence   uint16
	Detail     Button
	Time       Timestamp
	Root       Window
	Event      Window
	Child      Window
	RootX      int16
	RootY      int16
	EventX     int16
	EventY     int16
	State      uint16
	SameScreen bool
	// padding: 1 bytes
}

// ButtonPressEventNew constructs a ButtonPressEvent value that implements xgb.Event from a byte slice.
func ButtonPressEventNew(buf []byte) xgb.Event {
	v := ButtonPressEvent{}
	b := 1 // don't read event number

	v.Detail = Button(buf[b])
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.RootX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RootY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.State = xgb.Get16(buf[b:])
	b += 2

	if buf[b] == 1 {
		v.SameScreen = true
	} else {
		v.SameScreen = false
	}
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a ButtonPressEvent value to a byte slice.
func (v ButtonPressEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 4
	b += 1

	buf[b] = byte(v.Detail)
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Root))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Child))
	b += 4

	xgb.Put16(buf[b:], uint16(v.RootX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.RootY))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventY))
	b += 2

	xgb.Put16(buf[b:], v.State)
	b += 2

	if v.SameScreen {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the ButtonPress event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ButtonPressEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ButtonPressEvent.
func (v ButtonPressEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreen: %t", v.SameScreen))
	return "ButtonPress {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[4] = ButtonPressEventNew
}

// ButtonRelease is the event number for a ButtonReleaseEvent.
const ButtonRelease = 5

type ButtonReleaseEvent ButtonPressEvent

// ButtonReleaseEventNew constructs a ButtonReleaseEvent value that implements xgb.Event from a byte slice.
func ButtonReleaseEventNew(buf []byte) xgb.Event {
	return ButtonReleaseEvent(ButtonPressEventNew(buf).(ButtonPressEvent))
}

// Bytes writes a ButtonReleaseEvent value to a byte slice.
func (v ButtonReleaseEvent) Bytes() []byte {
	buf := ButtonPressEvent(v).Bytes()
	buf[0] = 5
	return buf
}

// SequenceId returns the sequence id attached to the ButtonRelease event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ButtonReleaseEvent) SequenceId() uint16 {
	return v.Sequence
}

func (v ButtonReleaseEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreen: %t", v.SameScreen))
	return "ButtonRelease {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[5] = ButtonReleaseEventNew
}

const (
	CapStyleNotLast    = 0
	CapStyleButt       = 1
	CapStyleRound      = 2
	CapStyleProjecting = 3
)

type Char2b struct {
	Byte1 byte
	Byte2 byte
}

// Char2bRead reads a byte slice into a Char2b value.
func Char2bRead(buf []byte, v *Char2b) int {
	b := 0

	v.Byte1 = buf[b]
	b += 1

	v.Byte2 = buf[b]
	b += 1

	return b
}

// Char2bReadList reads a byte slice into a list of Char2b values.
func Char2bReadList(buf []byte, dest []Char2b) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Char2b{}
		b += Char2bRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Char2b value to a byte slice.
func (v Char2b) Bytes() []byte {
	buf := make([]byte, 2)
	b := 0

	buf[b] = v.Byte1
	b += 1

	buf[b] = v.Byte2
	b += 1

	return buf[:b]
}

// Char2bListBytes writes a list of Char2b values to a byte slice.
func Char2bListBytes(buf []byte, list []Char2b) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type Charinfo struct {
	LeftSideBearing  int16
	RightSideBearing int16
	CharacterWidth   int16
	Ascent           int16
	Descent          int16
	Attributes       uint16
}

// CharinfoRead reads a byte slice into a Charinfo value.
func CharinfoRead(buf []byte, v *Charinfo) int {
	b := 0

	v.LeftSideBearing = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RightSideBearing = int16(xgb.Get16(buf[b:]))
	b += 2

	v.CharacterWidth = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Ascent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Descent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Attributes = xgb.Get16(buf[b:])
	b += 2

	return b
}

// CharinfoReadList reads a byte slice into a list of Charinfo values.
func CharinfoReadList(buf []byte, dest []Charinfo) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Charinfo{}
		b += CharinfoRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Charinfo value to a byte slice.
func (v Charinfo) Bytes() []byte {
	buf := make([]byte, 12)
	b := 0

	xgb.Put16(buf[b:], uint16(v.LeftSideBearing))
	b += 2

	xgb.Put16(buf[b:], uint16(v.RightSideBearing))
	b += 2

	xgb.Put16(buf[b:], uint16(v.CharacterWidth))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Ascent))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Descent))
	b += 2

	xgb.Put16(buf[b:], v.Attributes)
	b += 2

	return buf[:b]
}

// CharinfoListBytes writes a list of Charinfo values to a byte slice.
func CharinfoListBytes(buf []byte, list []Charinfo) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

const (
	CirculateRaiseLowest  = 0
	CirculateLowerHighest = 1
)

// CirculateNotify is the event number for a CirculateNotifyEvent.
const CirculateNotify = 26

type CirculateNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event  Window
	Window Window
	// padding: 4 bytes
	Place byte
	// padding: 3 bytes
}

// CirculateNotifyEventNew constructs a CirculateNotifyEvent value that implements xgb.Event from a byte slice.
func CirculateNotifyEventNew(buf []byte) xgb.Event {
	v := CirculateNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	b += 4 // padding

	v.Place = buf[b]
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a CirculateNotifyEvent value to a byte slice.
func (v CirculateNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 26
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	b += 4 // padding

	buf[b] = v.Place
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the CirculateNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v CirculateNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of CirculateNotifyEvent.
func (v CirculateNotifyEvent) String() string {
	fieldVals := make([]string, 0, 6)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Place: %d", v.Place))
	return "CirculateNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[26] = CirculateNotifyEventNew
}

// CirculateRequest is the event number for a CirculateRequestEvent.
const CirculateRequest = 27

type CirculateRequestEvent CirculateNotifyEvent

// CirculateRequestEventNew constructs a CirculateRequestEvent value that implements xgb.Event from a byte slice.
func CirculateRequestEventNew(buf []byte) xgb.Event {
	return CirculateRequestEvent(CirculateNotifyEventNew(buf).(CirculateNotifyEvent))
}

// Bytes writes a CirculateRequestEvent value to a byte slice.
func (v CirculateRequestEvent) Bytes() []byte {
	buf := CirculateNotifyEvent(v).Bytes()
	buf[0] = 27
	return buf
}

// SequenceId returns the sequence id attached to the CirculateRequest event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v CirculateRequestEvent) SequenceId() uint16 {
	return v.Sequence
}

func (v CirculateRequestEvent) String() string {
	fieldVals := make([]string, 0, 6)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Place: %d", v.Place))
	return "CirculateRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[27] = CirculateRequestEventNew
}

// ClientMessage is the event number for a ClientMessageEvent.
const ClientMessage = 33

type ClientMessageEvent struct {
	Sequence uint16
	Format   byte
	Window   Window
	Type     Atom
	Data     ClientMessageDataUnion
}

// ClientMessageEventNew constructs a ClientMessageEvent value that implements xgb.Event from a byte slice.
func ClientMessageEventNew(buf []byte) xgb.Event {
	v := ClientMessageEvent{}
	b := 1 // don't read event number

	v.Format = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Type = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Data = ClientMessageDataUnion{}
	b += ClientMessageDataUnionRead(buf[b:], &v.Data)

	return v
}

// Bytes writes a ClientMessageEvent value to a byte slice.
func (v ClientMessageEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 33
	b += 1

	buf[b] = v.Format
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Type))
	b += 4

	{
		unionBytes := v.Data.Bytes()
		copy(buf[b:], unionBytes)
		b += len(unionBytes)
	}

	return buf
}

// SequenceId returns the sequence id attached to the ClientMessage event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ClientMessageEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ClientMessageEvent.
func (v ClientMessageEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Format: %d", v.Format))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Type: %d", v.Type))
	return "ClientMessage {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[33] = ClientMessageEventNew
}

// ClientMessageDataUnion is a represention of the ClientMessageDataUnion union type.
// Note that to *create* a Union, you should *never* create
// this struct directly (unless you know what you're doing).
// Instead use one of the following constructors for 'ClientMessageDataUnion':
//
//	ClientMessageDataUnionData8New(Data8 []byte) ClientMessageDataUnion
//	ClientMessageDataUnionData16New(Data16 []uint16) ClientMessageDataUnion
//	ClientMessageDataUnionData32New(Data32 []uint32) ClientMessageDataUnion
type ClientMessageDataUnion struct {
	Data8  []byte   // size: 20
	Data16 []uint16 // size: 20
	Data32 []uint32 // size: 20
}

// ClientMessageDataUnionData8New constructs a new ClientMessageDataUnion union type with the Data8 field.
func ClientMessageDataUnionData8New(Data8 []byte) ClientMessageDataUnion {
	var b int
	buf := make([]byte, 20)

	copy(buf[b:], Data8[:20])
	b += int(20)

	// Create the Union type
	v := ClientMessageDataUnion{}

	// Now copy buf into all fields

	b = 0 // always read the same bytes
	v.Data8 = make([]byte, 20)
	copy(v.Data8[:20], buf[b:])
	b += int(20)

	b = 0 // always read the same bytes
	v.Data16 = make([]uint16, 10)
	for i := 0; i < int(10); i++ {
		v.Data16[i] = xgb.Get16(buf[b:])
		b += 2
	}

	b = 0 // always read the same bytes
	v.Data32 = make([]uint32, 5)
	for i := 0; i < int(5); i++ {
		v.Data32[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return v
}

// ClientMessageDataUnionData16New constructs a new ClientMessageDataUnion union type with the Data16 field.
func ClientMessageDataUnionData16New(Data16 []uint16) ClientMessageDataUnion {
	var b int
	buf := make([]byte, 20)

	for i := 0; i < int(10); i++ {
		xgb.Put16(buf[b:], Data16[i])
		b += 2
	}

	// Create the Union type
	v := ClientMessageDataUnion{}

	// Now copy buf into all fields

	b = 0 // always read the same bytes
	v.Data8 = make([]byte, 20)
	copy(v.Data8[:20], buf[b:])
	b += int(20)

	b = 0 // always read the same bytes
	v.Data16 = make([]uint16, 10)
	for i := 0; i < int(10); i++ {
		v.Data16[i] = xgb.Get16(buf[b:])
		b += 2
	}

	b = 0 // always read the same bytes
	v.Data32 = make([]uint32, 5)
	for i := 0; i < int(5); i++ {
		v.Data32[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return v
}

// ClientMessageDataUnionData32New constructs a new ClientMessageDataUnion union type with the Data32 field.
func ClientMessageDataUnionData32New(Data32 []uint32) ClientMessageDataUnion {
	var b int
	buf := make([]byte, 20)

	for i := 0; i < int(5); i++ {
		xgb.Put32(buf[b:], Data32[i])
		b += 4
	}

	// Create the Union type
	v := ClientMessageDataUnion{}

	// Now copy buf into all fields

	b = 0 // always read the same bytes
	v.Data8 = make([]byte, 20)
	copy(v.Data8[:20], buf[b:])
	b += int(20)

	b = 0 // always read the same bytes
	v.Data16 = make([]uint16, 10)
	for i := 0; i < int(10); i++ {
		v.Data16[i] = xgb.Get16(buf[b:])
		b += 2
	}

	b = 0 // always read the same bytes
	v.Data32 = make([]uint32, 5)
	for i := 0; i < int(5); i++ {
		v.Data32[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return v
}

// ClientMessageDataUnionRead reads a byte slice into a ClientMessageDataUnion value.
func ClientMessageDataUnionRead(buf []byte, v *ClientMessageDataUnion) int {
	var b int

	b = 0 // re-read the same bytes
	v.Data8 = make([]byte, 20)
	copy(v.Data8[:20], buf[b:])
	b += int(20)

	b = 0 // re-read the same bytes
	v.Data16 = make([]uint16, 10)
	for i := 0; i < int(10); i++ {
		v.Data16[i] = xgb.Get16(buf[b:])
		b += 2
	}

	b = 0 // re-read the same bytes
	v.Data32 = make([]uint32, 5)
	for i := 0; i < int(5); i++ {
		v.Data32[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return 20
}

// ClientMessageDataUnionReadList reads a byte slice into a list of ClientMessageDataUnion values.
func ClientMessageDataUnionReadList(buf []byte, dest []ClientMessageDataUnion) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = ClientMessageDataUnion{}
		b += ClientMessageDataUnionRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a ClientMessageDataUnion value to a byte slice.
// Each field in a union must contain the same data.
// So simply pick the first field and write that to the wire.
func (v ClientMessageDataUnion) Bytes() []byte {
	buf := make([]byte, 20)
	b := 0

	copy(buf[b:], v.Data8[:20])
	b += int(20)
	return buf
}

// ClientMessageDataUnionListBytes writes a list of ClientMessageDataUnion values to a byte slice.
func ClientMessageDataUnionListBytes(buf []byte, list []ClientMessageDataUnion) int {
	b := 0
	var unionBytes []byte
	for _, item := range list {
		unionBytes = item.Bytes()
		copy(buf[b:], unionBytes)
		b += xgb.Pad(len(unionBytes))
	}
	return b
}

const (
	ClipOrderingUnsorted = 0
	ClipOrderingYSorted  = 1
	ClipOrderingYXSorted = 2
	ClipOrderingYXBanded = 3
)

const (
	CloseDownDestroyAll      = 0
	CloseDownRetainPermanent = 1
	CloseDownRetainTemporary = 2
)

const (
	ColorFlagRed   = 1
	ColorFlagGreen = 2
	ColorFlagBlue  = 4
)

type Coloritem struct {
	Pixel uint32
	Red   uint16
	Green uint16
	Blue  uint16
	Flags byte
	// padding: 1 bytes
}

// ColoritemRead reads a byte slice into a Coloritem value.
func ColoritemRead(buf []byte, v *Coloritem) int {
	b := 0

	v.Pixel = xgb.Get32(buf[b:])
	b += 4

	v.Red = xgb.Get16(buf[b:])
	b += 2

	v.Green = xgb.Get16(buf[b:])
	b += 2

	v.Blue = xgb.Get16(buf[b:])
	b += 2

	v.Flags = buf[b]
	b += 1

	b += 1 // padding

	return b
}

// ColoritemReadList reads a byte slice into a list of Coloritem values.
func ColoritemReadList(buf []byte, dest []Coloritem) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Coloritem{}
		b += ColoritemRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Coloritem value to a byte slice.
func (v Coloritem) Bytes() []byte {
	buf := make([]byte, 12)
	b := 0

	xgb.Put32(buf[b:], v.Pixel)
	b += 4

	xgb.Put16(buf[b:], v.Red)
	b += 2

	xgb.Put16(buf[b:], v.Green)
	b += 2

	xgb.Put16(buf[b:], v.Blue)
	b += 2

	buf[b] = v.Flags
	b += 1

	b += 1 // padding

	return buf[:b]
}

// ColoritemListBytes writes a list of Coloritem values to a byte slice.
func ColoritemListBytes(buf []byte, list []Coloritem) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type Colormap uint32

func NewColormapId(c *xgb.Conn) (Colormap, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Colormap(id), nil
}

const (
	ColormapNone = 0
)

// BadColormap is the error number for a BadColormap.
const BadColormap = 12

type ColormapError ValueError

// ColormapErrorNew constructs a ColormapError value that implements xgb.Error from a byte slice.
func ColormapErrorNew(buf []byte) xgb.Error {
	v := ColormapError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Colormap"
	return v
}

// SequenceId returns the sequence id attached to the BadColormap error.
// This is mostly used internally.
func (err ColormapError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadColormap error. If no bad value exists, 0 is returned.
func (err ColormapError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadColormap error.
func (err ColormapError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadColormap {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[12] = ColormapErrorNew
}

const (
	ColormapAllocNone = 0
	ColormapAllocAll  = 1
)

// ColormapNotify is the event number for a ColormapNotifyEvent.
const ColormapNotify = 32

type ColormapNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Window   Window
	Colormap Colormap
	New      bool
	State    byte
	// padding: 2 bytes
}

// ColormapNotifyEventNew constructs a ColormapNotifyEvent value that implements xgb.Event from a byte slice.
func ColormapNotifyEventNew(buf []byte) xgb.Event {
	v := ColormapNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Colormap = Colormap(xgb.Get32(buf[b:]))
	b += 4

	if buf[b] == 1 {
		v.New = true
	} else {
		v.New = false
	}
	b += 1

	v.State = buf[b]
	b += 1

	b += 2 // padding

	return v
}

// Bytes writes a ColormapNotifyEvent value to a byte slice.
func (v ColormapNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 32
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Colormap))
	b += 4

	if v.New {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	buf[b] = v.State
	b += 1

	b += 2 // padding

	return buf
}

// SequenceId returns the sequence id attached to the ColormapNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ColormapNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ColormapNotifyEvent.
func (v ColormapNotifyEvent) String() string {
	fieldVals := make([]string, 0, 6)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Colormap: %d", v.Colormap))
	fieldVals = append(fieldVals, xgb.Sprintf("New: %t", v.New))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	return "ColormapNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[32] = ColormapNotifyEventNew
}

const (
	ColormapStateUninstalled = 0
	ColormapStateInstalled   = 1
)

const (
	ConfigWindowX           = 1
	ConfigWindowY           = 2
	ConfigWindowWidth       = 4
	ConfigWindowHeight      = 8
	ConfigWindowBorderWidth = 16
	ConfigWindowSibling     = 32
	ConfigWindowStackMode   = 64
)

// ConfigureNotify is the event number for a ConfigureNotifyEvent.
const ConfigureNotify = 22

type ConfigureNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event            Window
	Window           Window
	AboveSibling     Window
	X                int16
	Y                int16
	Width            uint16
	Height           uint16
	BorderWidth      uint16
	OverrideRedirect bool
	// padding: 1 bytes
}

// ConfigureNotifyEventNew constructs a ConfigureNotifyEvent value that implements xgb.Event from a byte slice.
func ConfigureNotifyEventNew(buf []byte) xgb.Event {
	v := ConfigureNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.AboveSibling = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.BorderWidth = xgb.Get16(buf[b:])
	b += 2

	if buf[b] == 1 {
		v.OverrideRedirect = true
	} else {
		v.OverrideRedirect = false
	}
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a ConfigureNotifyEvent value to a byte slice.
func (v ConfigureNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 22
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.AboveSibling))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], v.BorderWidth)
	b += 2

	if v.OverrideRedirect {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the ConfigureNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ConfigureNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ConfigureNotifyEvent.
func (v ConfigureNotifyEvent) String() string {
	fieldVals := make([]string, 0, 11)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("AboveSibling: %d", v.AboveSibling))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	fieldVals = append(fieldVals, xgb.Sprintf("BorderWidth: %d", v.BorderWidth))
	fieldVals = append(fieldVals, xgb.Sprintf("OverrideRedirect: %t", v.OverrideRedirect))
	return "ConfigureNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[22] = ConfigureNotifyEventNew
}

// ConfigureRequest is the event number for a ConfigureRequestEvent.
const ConfigureRequest = 23

type ConfigureRequestEvent struct {
	Sequence    uint16
	StackMode   byte
	Parent      Window
	Window      Window
	Sibling     Window
	X           int16
	Y           int16
	Width       uint16
	Height      uint16
	BorderWidth uint16
	ValueMask   uint16
}

// ConfigureRequestEventNew constructs a ConfigureRequestEvent value that implements xgb.Event from a byte slice.
func ConfigureRequestEventNew(buf []byte) xgb.Event {
	v := ConfigureRequestEvent{}
	b := 1 // don't read event number

	v.StackMode = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Parent = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Sibling = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.BorderWidth = xgb.Get16(buf[b:])
	b += 2

	v.ValueMask = xgb.Get16(buf[b:])
	b += 2

	return v
}

// Bytes writes a ConfigureRequestEvent value to a byte slice.
func (v ConfigureRequestEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 23
	b += 1

	buf[b] = v.StackMode
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Parent))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Sibling))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], v.BorderWidth)
	b += 2

	xgb.Put16(buf[b:], v.ValueMask)
	b += 2

	return buf
}

// SequenceId returns the sequence id attached to the ConfigureRequest event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ConfigureRequestEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ConfigureRequestEvent.
func (v ConfigureRequestEvent) String() string {
	fieldVals := make([]string, 0, 10)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("StackMode: %d", v.StackMode))
	fieldVals = append(fieldVals, xgb.Sprintf("Parent: %d", v.Parent))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Sibling: %d", v.Sibling))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	fieldVals = append(fieldVals, xgb.Sprintf("BorderWidth: %d", v.BorderWidth))
	fieldVals = append(fieldVals, xgb.Sprintf("ValueMask: %d", v.ValueMask))
	return "ConfigureRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[23] = ConfigureRequestEventNew
}

const (
	CoordModeOrigin   = 0
	CoordModePrevious = 1
)

// CreateNotify is the event number for a CreateNotifyEvent.
const CreateNotify = 16

type CreateNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Parent           Window
	Window           Window
	X                int16
	Y                int16
	Width            uint16
	Height           uint16
	BorderWidth      uint16
	OverrideRedirect bool
	// padding: 1 bytes
}

// CreateNotifyEventNew constructs a CreateNotifyEvent value that implements xgb.Event from a byte slice.
func CreateNotifyEventNew(buf []byte) xgb.Event {
	v := CreateNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Parent = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.BorderWidth = xgb.Get16(buf[b:])
	b += 2

	if buf[b] == 1 {
		v.OverrideRedirect = true
	} else {
		v.OverrideRedirect = false
	}
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a CreateNotifyEvent value to a byte slice.
func (v CreateNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 16
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Parent))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], v.BorderWidth)
	b += 2

	if v.OverrideRedirect {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the CreateNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v CreateNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of CreateNotifyEvent.
func (v CreateNotifyEvent) String() string {
	fieldVals := make([]string, 0, 10)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Parent: %d", v.Parent))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	fieldVals = append(fieldVals, xgb.Sprintf("BorderWidth: %d", v.BorderWidth))
	fieldVals = append(fieldVals, xgb.Sprintf("OverrideRedirect: %t", v.OverrideRedirect))
	return "CreateNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[16] = CreateNotifyEventNew
}

type Cursor uint32

func NewCursorId(c *xgb.Conn) (Cursor, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Cursor(id), nil
}

const (
	CursorNone = 0
)

// BadCursor is the error number for a BadCursor.
const BadCursor = 6

type CursorError ValueError

// CursorErrorNew constructs a CursorError value that implements xgb.Error from a byte slice.
func CursorErrorNew(buf []byte) xgb.Error {
	v := CursorError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Cursor"
	return v
}

// SequenceId returns the sequence id attached to the BadCursor error.
// This is mostly used internally.
func (err CursorError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadCursor error. If no bad value exists, 0 is returned.
func (err CursorError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadCursor error.
func (err CursorError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadCursor {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[6] = CursorErrorNew
}

const (
	CwBackPixmap       = 1
	CwBackPixel        = 2
	CwBorderPixmap     = 4
	CwBorderPixel      = 8
	CwBitGravity       = 16
	CwWinGravity       = 32
	CwBackingStore     = 64
	CwBackingPlanes    = 128
	CwBackingPixel     = 256
	CwOverrideRedirect = 512
	CwSaveUnder        = 1024
	CwEventMask        = 2048
	CwDontPropagate    = 4096
	CwColormap         = 8192
	CwCursor           = 16384
)

type DepthInfo struct {
	Depth byte
	// padding: 1 bytes
	VisualsLen uint16
	// padding: 4 bytes
	Visuals []VisualInfo // size: xgb.Pad((int(VisualsLen) * 24))
}

// DepthInfoRead reads a byte slice into a DepthInfo value.
func DepthInfoRead(buf []byte, v *DepthInfo) int {
	b := 0

	v.Depth = buf[b]
	b += 1

	b += 1 // padding

	v.VisualsLen = xgb.Get16(buf[b:])
	b += 2

	b += 4 // padding

	v.Visuals = make([]VisualInfo, v.VisualsLen)
	b += VisualInfoReadList(buf[b:], v.Visuals)

	return b
}

// DepthInfoReadList reads a byte slice into a list of DepthInfo values.
func DepthInfoReadList(buf []byte, dest []DepthInfo) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = DepthInfo{}
		b += DepthInfoRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a DepthInfo value to a byte slice.
func (v DepthInfo) Bytes() []byte {
	buf := make([]byte, (8 + xgb.Pad((int(v.VisualsLen) * 24))))
	b := 0

	buf[b] = v.Depth
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], v.VisualsLen)
	b += 2

	b += 4 // padding

	b += VisualInfoListBytes(buf[b:], v.Visuals)

	return buf[:b]
}

// DepthInfoListBytes writes a list of DepthInfo values to a byte slice.
func DepthInfoListBytes(buf []byte, list []DepthInfo) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// DepthInfoListSize computes the size (bytes) of a list of DepthInfo values.
func DepthInfoListSize(list []DepthInfo) int {
	size := 0
	for _, item := range list {
		size += (8 + xgb.Pad((int(item.VisualsLen) * 24)))
	}
	return size
}

// DestroyNotify is the event number for a DestroyNotifyEvent.
const DestroyNotify = 17

type DestroyNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event  Window
	Window Window
}

// DestroyNotifyEventNew constructs a DestroyNotifyEvent value that implements xgb.Event from a byte slice.
func DestroyNotifyEventNew(buf []byte) xgb.Event {
	v := DestroyNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Bytes writes a DestroyNotifyEvent value to a byte slice.
func (v DestroyNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 17
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	return buf
}

// SequenceId returns the sequence id attached to the DestroyNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v DestroyNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of DestroyNotifyEvent.
func (v DestroyNotifyEvent) String() string {
	fieldVals := make([]string, 0, 3)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	return "DestroyNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[17] = DestroyNotifyEventNew
}

type Drawable uint32

func NewDrawableId(c *xgb.Conn) (Drawable, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Drawable(id), nil
}

// BadDrawable is the error number for a BadDrawable.
const BadDrawable = 9

type DrawableError ValueError

// DrawableErrorNew constructs a DrawableError value that implements xgb.Error from a byte slice.
func DrawableErrorNew(buf []byte) xgb.Error {
	v := DrawableError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Drawable"
	return v
}

// SequenceId returns the sequence id attached to the BadDrawable error.
// This is mostly used internally.
func (err DrawableError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadDrawable error. If no bad value exists, 0 is returned.
func (err DrawableError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadDrawable error.
func (err DrawableError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadDrawable {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[9] = DrawableErrorNew
}

// EnterNotify is the event number for a EnterNotifyEvent.
const EnterNotify = 7

type EnterNotifyEvent struct {
	Sequence        uint16
	Detail          byte
	Time            Timestamp
	Root            Window
	Event           Window
	Child           Window
	RootX           int16
	RootY           int16
	EventX          int16
	EventY          int16
	State           uint16
	Mode            byte
	SameScreenFocus byte
}

// EnterNotifyEventNew constructs a EnterNotifyEvent value that implements xgb.Event from a byte slice.
func EnterNotifyEventNew(buf []byte) xgb.Event {
	v := EnterNotifyEvent{}
	b := 1 // don't read event number

	v.Detail = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.RootX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RootY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.State = xgb.Get16(buf[b:])
	b += 2

	v.Mode = buf[b]
	b += 1

	v.SameScreenFocus = buf[b]
	b += 1

	return v
}

// Bytes writes a EnterNotifyEvent value to a byte slice.
func (v EnterNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 7
	b += 1

	buf[b] = v.Detail
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Root))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Child))
	b += 4

	xgb.Put16(buf[b:], uint16(v.RootX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.RootY))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventY))
	b += 2

	xgb.Put16(buf[b:], v.State)
	b += 2

	buf[b] = v.Mode
	b += 1

	buf[b] = v.SameScreenFocus
	b += 1

	return buf
}

// SequenceId returns the sequence id attached to the EnterNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v EnterNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of EnterNotifyEvent.
func (v EnterNotifyEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("Mode: %d", v.Mode))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreenFocus: %d", v.SameScreenFocus))
	return "EnterNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[7] = EnterNotifyEventNew
}

const (
	EventMaskNoEvent              = 0
	EventMaskKeyPress             = 1
	EventMaskKeyRelease           = 2
	EventMaskButtonPress          = 4
	EventMaskButtonRelease        = 8
	EventMaskEnterWindow          = 16
	EventMaskLeaveWindow          = 32
	EventMaskPointerMotion        = 64
	EventMaskPointerMotionHint    = 128
	EventMaskButton1Motion        = 256
	EventMaskButton2Motion        = 512
	EventMaskButton3Motion        = 1024
	EventMaskButton4Motion        = 2048
	EventMaskButton5Motion        = 4096
	EventMaskButtonMotion         = 8192
	EventMaskKeymapState          = 16384
	EventMaskExposure             = 32768
	EventMaskVisibilityChange     = 65536
	EventMaskStructureNotify      = 131072
	EventMaskResizeRedirect       = 262144
	EventMaskSubstructureNotify   = 524288
	EventMaskSubstructureRedirect = 1048576
	EventMaskFocusChange          = 2097152
	EventMaskPropertyChange       = 4194304
	EventMaskColorMapChange       = 8388608
	EventMaskOwnerGrabButton      = 16777216
)

// Expose is the event number for a ExposeEvent.
const Expose = 12

type ExposeEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Window Window
	X      uint16
	Y      uint16
	Width  uint16
	Height uint16
	Count  uint16
	// padding: 2 bytes
}

// ExposeEventNew constructs a ExposeEvent value that implements xgb.Event from a byte slice.
func ExposeEventNew(buf []byte) xgb.Event {
	v := ExposeEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = xgb.Get16(buf[b:])
	b += 2

	v.Y = xgb.Get16(buf[b:])
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.Count = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	return v
}

// Bytes writes a ExposeEvent value to a byte slice.
func (v ExposeEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 12
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put16(buf[b:], v.X)
	b += 2

	xgb.Put16(buf[b:], v.Y)
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], v.Count)
	b += 2

	b += 2 // padding

	return buf
}

// SequenceId returns the sequence id attached to the Expose event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ExposeEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ExposeEvent.
func (v ExposeEvent) String() string {
	fieldVals := make([]string, 0, 8)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	fieldVals = append(fieldVals, xgb.Sprintf("Count: %d", v.Count))
	return "Expose {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[12] = ExposeEventNew
}

const (
	ExposuresNotAllowed = 0
	ExposuresAllowed    = 1
	ExposuresDefault    = 2
)

const (
	FamilyInternet          = 0
	FamilyDECnet            = 1
	FamilyChaos             = 2
	FamilyServerInterpreted = 5
	FamilyInternet6         = 6
)

const (
	FillRuleEvenOdd = 0
	FillRuleWinding = 1
)

const (
	FillStyleSolid          = 0
	FillStyleTiled          = 1
	FillStyleStippled       = 2
	FillStyleOpaqueStippled = 3
)

// FocusIn is the event number for a FocusInEvent.
const FocusIn = 9

type FocusInEvent struct {
	Sequence uint16
	Detail   byte
	Event    Window
	Mode     byte
	// padding: 3 bytes
}

// FocusInEventNew constructs a FocusInEvent value that implements xgb.Event from a byte slice.
func FocusInEventNew(buf []byte) xgb.Event {
	v := FocusInEvent{}
	b := 1 // don't read event number

	v.Detail = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Mode = buf[b]
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a FocusInEvent value to a byte slice.
func (v FocusInEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 9
	b += 1

	buf[b] = v.Detail
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	buf[b] = v.Mode
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the FocusIn event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v FocusInEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of FocusInEvent.
func (v FocusInEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Mode: %d", v.Mode))
	return "FocusIn {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[9] = FocusInEventNew
}

// FocusOut is the event number for a FocusOutEvent.
const FocusOut = 10

type FocusOutEvent FocusInEvent

// FocusOutEventNew constructs a FocusOutEvent value that implements xgb.Event from a byte slice.
func FocusOutEventNew(buf []byte) xgb.Event {
	return FocusOutEvent(FocusInEventNew(buf).(FocusInEvent))
}

// Bytes writes a FocusOutEvent value to a byte slice.
func (v FocusOutEvent) Bytes() []byte {
	buf := FocusInEvent(v).Bytes()
	buf[0] = 10
	return buf
}

// SequenceId returns the sequence id attached to the FocusOut event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v FocusOutEvent) SequenceId() uint16 {
	return v.Sequence
}

func (v FocusOutEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Mode: %d", v.Mode))
	return "FocusOut {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[10] = FocusOutEventNew
}

type Font uint32

func NewFontId(c *xgb.Conn) (Font, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Font(id), nil
}

// BadFont is the error number for a BadFont.
const BadFont = 7

type FontError ValueError

// FontErrorNew constructs a FontError value that implements xgb.Error from a byte slice.
func FontErrorNew(buf []byte) xgb.Error {
	v := FontError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Font"
	return v
}

// SequenceId returns the sequence id attached to the BadFont error.
// This is mostly used internally.
func (err FontError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadFont error. If no bad value exists, 0 is returned.
func (err FontError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadFont error.
func (err FontError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadFont {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[7] = FontErrorNew
}

const (
	FontNone = 0
)

const (
	FontDrawLeftToRight = 0
	FontDrawRightToLeft = 1
)

type Fontable uint32

func NewFontableId(c *xgb.Conn) (Fontable, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Fontable(id), nil
}

type Fontprop struct {
	Name  Atom
	Value uint32
}

// FontpropRead reads a byte slice into a Fontprop value.
func FontpropRead(buf []byte, v *Fontprop) int {
	b := 0

	v.Name = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Value = xgb.Get32(buf[b:])
	b += 4

	return b
}

// FontpropReadList reads a byte slice into a list of Fontprop values.
func FontpropReadList(buf []byte, dest []Fontprop) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Fontprop{}
		b += FontpropRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Fontprop value to a byte slice.
func (v Fontprop) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	xgb.Put32(buf[b:], uint32(v.Name))
	b += 4

	xgb.Put32(buf[b:], v.Value)
	b += 4

	return buf[:b]
}

// FontpropListBytes writes a list of Fontprop values to a byte slice.
func FontpropListBytes(buf []byte, list []Fontprop) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type Format struct {
	Depth        byte
	BitsPerPixel byte
	ScanlinePad  byte
	// padding: 5 bytes
}

// FormatRead reads a byte slice into a Format value.
func FormatRead(buf []byte, v *Format) int {
	b := 0

	v.Depth = buf[b]
	b += 1

	v.BitsPerPixel = buf[b]
	b += 1

	v.ScanlinePad = buf[b]
	b += 1

	b += 5 // padding

	return b
}

// FormatReadList reads a byte slice into a list of Format values.
func FormatReadList(buf []byte, dest []Format) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Format{}
		b += FormatRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Format value to a byte slice.
func (v Format) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	buf[b] = v.Depth
	b += 1

	buf[b] = v.BitsPerPixel
	b += 1

	buf[b] = v.ScanlinePad
	b += 1

	b += 5 // padding

	return buf[:b]
}

// FormatListBytes writes a list of Format values to a byte slice.
func FormatListBytes(buf []byte, list []Format) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// BadGContext is the error number for a BadGContext.
const BadGContext = 13

type GContextError ValueError

// GContextErrorNew constructs a GContextError value that implements xgb.Error from a byte slice.
func GContextErrorNew(buf []byte) xgb.Error {
	v := GContextError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "GContext"
	return v
}

// SequenceId returns the sequence id attached to the BadGContext error.
// This is mostly used internally.
func (err GContextError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadGContext error. If no bad value exists, 0 is returned.
func (err GContextError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadGContext error.
func (err GContextError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadGContext {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[13] = GContextErrorNew
}

const (
	GcFunction           = 1
	GcPlaneMask          = 2
	GcForeground         = 4
	GcBackground         = 8
	GcLineWidth          = 16
	GcLineStyle          = 32
	GcCapStyle           = 64
	GcJoinStyle          = 128
	GcFillStyle          = 256
	GcFillRule           = 512
	GcTile               = 1024
	GcStipple            = 2048
	GcTileStippleOriginX = 4096
	GcTileStippleOriginY = 8192
	GcFont               = 16384
	GcSubwindowMode      = 32768
	GcGraphicsExposures  = 65536
	GcClipOriginX        = 131072
	GcClipOriginY        = 262144
	GcClipMask           = 524288
	GcDashOffset         = 1048576
	GcDashList           = 2097152
	GcArcMode            = 4194304
)

type Gcontext uint32

func NewGcontextId(c *xgb.Conn) (Gcontext, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Gcontext(id), nil
}

// GeGeneric is the event number for a GeGenericEvent.
const GeGeneric = 35

type GeGenericEvent struct {
	Sequence uint16
	// padding: 22 bytes
}

// GeGenericEventNew constructs a GeGenericEvent value that implements xgb.Event from a byte slice.
func GeGenericEventNew(buf []byte) xgb.Event {
	v := GeGenericEvent{}
	b := 1 // don't read event number

	b += 22 // padding

	return v
}

// Bytes writes a GeGenericEvent value to a byte slice.
func (v GeGenericEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 35
	b += 1

	b += 22 // padding

	return buf
}

// SequenceId returns the sequence id attached to the GeGeneric event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v GeGenericEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of GeGenericEvent.
func (v GeGenericEvent) String() string {
	fieldVals := make([]string, 0, 1)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	return "GeGeneric {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[35] = GeGenericEventNew
}

const (
	GetPropertyTypeAny = 0
)

const (
	GrabAny = 0
)

const (
	GrabModeSync  = 0
	GrabModeAsync = 1
)

const (
	GrabStatusSuccess        = 0
	GrabStatusAlreadyGrabbed = 1
	GrabStatusInvalidTime    = 2
	GrabStatusNotViewable    = 3
	GrabStatusFrozen         = 4
)

// GraphicsExposure is the event number for a GraphicsExposureEvent.
const GraphicsExposure = 13

type GraphicsExposureEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Drawable    Drawable
	X           uint16
	Y           uint16
	Width       uint16
	Height      uint16
	MinorOpcode uint16
	Count       uint16
	MajorOpcode byte
	// padding: 3 bytes
}

// GraphicsExposureEventNew constructs a GraphicsExposureEvent value that implements xgb.Event from a byte slice.
func GraphicsExposureEventNew(buf []byte) xgb.Event {
	v := GraphicsExposureEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Drawable = Drawable(xgb.Get32(buf[b:]))
	b += 4

	v.X = xgb.Get16(buf[b:])
	b += 2

	v.Y = xgb.Get16(buf[b:])
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.MinorOpcode = xgb.Get16(buf[b:])
	b += 2

	v.Count = xgb.Get16(buf[b:])
	b += 2

	v.MajorOpcode = buf[b]
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a GraphicsExposureEvent value to a byte slice.
func (v GraphicsExposureEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 13
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Drawable))
	b += 4

	xgb.Put16(buf[b:], v.X)
	b += 2

	xgb.Put16(buf[b:], v.Y)
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	xgb.Put16(buf[b:], v.MinorOpcode)
	b += 2

	xgb.Put16(buf[b:], v.Count)
	b += 2

	buf[b] = v.MajorOpcode
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the GraphicsExposure event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v GraphicsExposureEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of GraphicsExposureEvent.
func (v GraphicsExposureEvent) String() string {
	fieldVals := make([]string, 0, 10)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Drawable: %d", v.Drawable))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", v.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("Count: %d", v.Count))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", v.MajorOpcode))
	return "GraphicsExposure {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[13] = GraphicsExposureEventNew
}

const (
	GravityBitForget = 0
	GravityWinUnmap  = 0
	GravityNorthWest = 1
	GravityNorth     = 2
	GravityNorthEast = 3
	GravityWest      = 4
	GravityCenter    = 5
	GravityEast      = 6
	GravitySouthWest = 7
	GravitySouth     = 8
	GravitySouthEast = 9
	GravityStatic    = 10
)

// GravityNotify is the event number for a GravityNotifyEvent.
const GravityNotify = 24

type GravityNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event  Window
	Window Window
	X      int16
	Y      int16
}

// GravityNotifyEventNew constructs a GravityNotifyEvent value that implements xgb.Event from a byte slice.
func GravityNotifyEventNew(buf []byte) xgb.Event {
	v := GravityNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	return v
}

// Bytes writes a GravityNotifyEvent value to a byte slice.
func (v GravityNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 24
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	return buf
}

// SequenceId returns the sequence id attached to the GravityNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v GravityNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of GravityNotifyEvent.
func (v GravityNotifyEvent) String() string {
	fieldVals := make([]string, 0, 5)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	return "GravityNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[24] = GravityNotifyEventNew
}

const (
	GxClear        = 0
	GxAnd          = 1
	GxAndReverse   = 2
	GxCopy         = 3
	GxAndInverted  = 4
	GxNoop         = 5
	GxXor          = 6
	GxOr           = 7
	GxNor          = 8
	GxEquiv        = 9
	GxInvert       = 10
	GxOrReverse    = 11
	GxCopyInverted = 12
	GxOrInverted   = 13
	GxNand         = 14
	GxSet          = 15
)

type Host struct {
	Family byte
	// padding: 1 bytes
	AddressLen uint16
	Address    []byte // size: xgb.Pad((int(AddressLen) * 1))
	// alignment gap to multiple of 4
}

// HostRead reads a byte slice into a Host value.
func HostRead(buf []byte, v *Host) int {
	b := 0

	v.Family = buf[b]
	b += 1

	b += 1 // padding

	v.AddressLen = xgb.Get16(buf[b:])
	b += 2

	v.Address = make([]byte, v.AddressLen)
	copy(v.Address[:v.AddressLen], buf[b:])
	b += int(v.AddressLen)

	b = (b + 3) & ^3 // alignment gap

	return b
}

// HostReadList reads a byte slice into a list of Host values.
func HostReadList(buf []byte, dest []Host) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Host{}
		b += HostRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Host value to a byte slice.
func (v Host) Bytes() []byte {
	buf := make([]byte, ((4 + xgb.Pad((int(v.AddressLen) * 1))) + 4))
	b := 0

	buf[b] = v.Family
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], v.AddressLen)
	b += 2

	copy(buf[b:], v.Address[:v.AddressLen])
	b += int(v.AddressLen)

	b = (b + 3) & ^3 // alignment gap

	return buf[:b]
}

// HostListBytes writes a list of Host values to a byte slice.
func HostListBytes(buf []byte, list []Host) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// HostListSize computes the size (bytes) of a list of Host values.
func HostListSize(list []Host) int {
	size := 0
	for _, item := range list {
		size += ((4 + xgb.Pad((int(item.AddressLen) * 1))) + 4)
	}
	return size
}

const (
	HostModeInsert = 0
	HostModeDelete = 1
)

// BadIDChoice is the error number for a BadIDChoice.
const BadIDChoice = 14

type IDChoiceError ValueError

// IDChoiceErrorNew constructs a IDChoiceError value that implements xgb.Error from a byte slice.
func IDChoiceErrorNew(buf []byte) xgb.Error {
	v := IDChoiceError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "IDChoice"
	return v
}

// SequenceId returns the sequence id attached to the BadIDChoice error.
// This is mostly used internally.
func (err IDChoiceError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadIDChoice error. If no bad value exists, 0 is returned.
func (err IDChoiceError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadIDChoice error.
func (err IDChoiceError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadIDChoice {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[14] = IDChoiceErrorNew
}

const (
	ImageFormatXYBitmap = 0
	ImageFormatXYPixmap = 1
	ImageFormatZPixmap  = 2
)

const (
	ImageOrderLSBFirst = 0
	ImageOrderMSBFirst = 1
)

// BadImplementation is the error number for a BadImplementation.
const BadImplementation = 17

type ImplementationError RequestError

// ImplementationErrorNew constructs a ImplementationError value that implements xgb.Error from a byte slice.
func ImplementationErrorNew(buf []byte) xgb.Error {
	v := ImplementationError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Implementation"
	return v
}

// SequenceId returns the sequence id attached to the BadImplementation error.
// This is mostly used internally.
func (err ImplementationError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadImplementation error. If no bad value exists, 0 is returned.
func (err ImplementationError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadImplementation error.
func (err ImplementationError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadImplementation {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[17] = ImplementationErrorNew
}

const (
	InputFocusNone           = 0
	InputFocusPointerRoot    = 1
	InputFocusParent         = 2
	InputFocusFollowKeyboard = 3
)

const (
	JoinStyleMiter = 0
	JoinStyleRound = 1
	JoinStyleBevel = 2
)

const (
	KbKeyClickPercent = 1
	KbBellPercent     = 2
	KbBellPitch       = 4
	KbBellDuration    = 8
	KbLed             = 16
	KbLedMode         = 32
	KbKey             = 64
	KbAutoRepeatMode  = 128
)

const (
	KeyButMaskShift   = 1
	KeyButMaskLock    = 2
	KeyButMaskControl = 4
	KeyButMaskMod1    = 8
	KeyButMaskMod2    = 16
	KeyButMaskMod3    = 32
	KeyButMaskMod4    = 64
	KeyButMaskMod5    = 128
	KeyButMaskButton1 = 256
	KeyButMaskButton2 = 512
	KeyButMaskButton3 = 1024
	KeyButMaskButton4 = 2048
	KeyButMaskButton5 = 4096
)

// KeyPress is the event number for a KeyPressEvent.
const KeyPress = 2

type KeyPressEvent struct {
	Sequence   uint16
	Detail     Keycode
	Time       Timestamp
	Root       Window
	Event      Window
	Child      Window
	RootX      int16
	RootY      int16
	EventX     int16
	EventY     int16
	State      uint16
	SameScreen bool
	// padding: 1 bytes
}

// KeyPressEventNew constructs a KeyPressEvent value that implements xgb.Event from a byte slice.
func KeyPressEventNew(buf []byte) xgb.Event {
	v := KeyPressEvent{}
	b := 1 // don't read event number

	v.Detail = Keycode(buf[b])
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.RootX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RootY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.State = xgb.Get16(buf[b:])
	b += 2

	if buf[b] == 1 {
		v.SameScreen = true
	} else {
		v.SameScreen = false
	}
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a KeyPressEvent value to a byte slice.
func (v KeyPressEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 2
	b += 1

	buf[b] = byte(v.Detail)
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Root))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Child))
	b += 4

	xgb.Put16(buf[b:], uint16(v.RootX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.RootY))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventY))
	b += 2

	xgb.Put16(buf[b:], v.State)
	b += 2

	if v.SameScreen {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the KeyPress event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v KeyPressEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of KeyPressEvent.
func (v KeyPressEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreen: %t", v.SameScreen))
	return "KeyPress {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[2] = KeyPressEventNew
}

// KeyRelease is the event number for a KeyReleaseEvent.
const KeyRelease = 3

type KeyReleaseEvent KeyPressEvent

// KeyReleaseEventNew constructs a KeyReleaseEvent value that implements xgb.Event from a byte slice.
func KeyReleaseEventNew(buf []byte) xgb.Event {
	return KeyReleaseEvent(KeyPressEventNew(buf).(KeyPressEvent))
}

// Bytes writes a KeyReleaseEvent value to a byte slice.
func (v KeyReleaseEvent) Bytes() []byte {
	buf := KeyPressEvent(v).Bytes()
	buf[0] = 3
	return buf
}

// SequenceId returns the sequence id attached to the KeyRelease event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v KeyReleaseEvent) SequenceId() uint16 {
	return v.Sequence
}

func (v KeyReleaseEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreen: %t", v.SameScreen))
	return "KeyRelease {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[3] = KeyReleaseEventNew
}

type Keycode byte

type Keycode32 uint32

// KeymapNotify is the event number for a KeymapNotifyEvent.
const KeymapNotify = 11

type KeymapNotifyEvent struct {
	Keys []byte // size: 32
}

// KeymapNotifyEventNew constructs a KeymapNotifyEvent value that implements xgb.Event from a byte slice.
func KeymapNotifyEventNew(buf []byte) xgb.Event {
	v := KeymapNotifyEvent{}
	b := 1 // don't read event number

	v.Keys = make([]byte, 31)
	copy(v.Keys[:31], buf[b:])
	b += int(31)

	return v
}

// Bytes writes a KeymapNotifyEvent value to a byte slice.
func (v KeymapNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 11
	b += 1

	copy(buf[b:], v.Keys[:31])
	b += int(31)

	return buf
}

// SequenceId returns the sequence id attached to the KeymapNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v KeymapNotifyEvent) SequenceId() uint16 {
	return uint16(0)
}

// String is a rudimentary string representation of KeymapNotifyEvent.
func (v KeymapNotifyEvent) String() string {
	fieldVals := make([]string, 0, 1)
	return "KeymapNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[11] = KeymapNotifyEventNew
}

type Keysym uint32

const (
	KillAllTemporary = 0
)

// LeaveNotify is the event number for a LeaveNotifyEvent.
const LeaveNotify = 8

type LeaveNotifyEvent EnterNotifyEvent

// LeaveNotifyEventNew constructs a LeaveNotifyEvent value that implements xgb.Event from a byte slice.
func LeaveNotifyEventNew(buf []byte) xgb.Event {
	return LeaveNotifyEvent(EnterNotifyEventNew(buf).(EnterNotifyEvent))
}

// Bytes writes a LeaveNotifyEvent value to a byte slice.
func (v LeaveNotifyEvent) Bytes() []byte {
	buf := EnterNotifyEvent(v).Bytes()
	buf[0] = 8
	return buf
}

// SequenceId returns the sequence id attached to the LeaveNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v LeaveNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

func (v LeaveNotifyEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("Mode: %d", v.Mode))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreenFocus: %d", v.SameScreenFocus))
	return "LeaveNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[8] = LeaveNotifyEventNew
}

const (
	LedModeOff = 0
	LedModeOn  = 1
)

// BadLength is the error number for a BadLength.
const BadLength = 16

type LengthError RequestError

// LengthErrorNew constructs a LengthError value that implements xgb.Error from a byte slice.
func LengthErrorNew(buf []byte) xgb.Error {
	v := LengthError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Length"
	return v
}

// SequenceId returns the sequence id attached to the BadLength error.
// This is mostly used internally.
func (err LengthError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadLength error. If no bad value exists, 0 is returned.
func (err LengthError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadLength error.
func (err LengthError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadLength {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[16] = LengthErrorNew
}

const (
	LineStyleSolid      = 0
	LineStyleOnOffDash  = 1
	LineStyleDoubleDash = 2
)

const (
	MapIndexShift   = 0
	MapIndexLock    = 1
	MapIndexControl = 2
	MapIndex1       = 3
	MapIndex2       = 4
	MapIndex3       = 5
	MapIndex4       = 6
	MapIndex5       = 7
)

// MapNotify is the event number for a MapNotifyEvent.
const MapNotify = 19

type MapNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event            Window
	Window           Window
	OverrideRedirect bool
	// padding: 3 bytes
}

// MapNotifyEventNew constructs a MapNotifyEvent value that implements xgb.Event from a byte slice.
func MapNotifyEventNew(buf []byte) xgb.Event {
	v := MapNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	if buf[b] == 1 {
		v.OverrideRedirect = true
	} else {
		v.OverrideRedirect = false
	}
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a MapNotifyEvent value to a byte slice.
func (v MapNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 19
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	if v.OverrideRedirect {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the MapNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v MapNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of MapNotifyEvent.
func (v MapNotifyEvent) String() string {
	fieldVals := make([]string, 0, 5)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("OverrideRedirect: %t", v.OverrideRedirect))
	return "MapNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[19] = MapNotifyEventNew
}

// MapRequest is the event number for a MapRequestEvent.
const MapRequest = 20

type MapRequestEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Parent Window
	Window Window
}

// MapRequestEventNew constructs a MapRequestEvent value that implements xgb.Event from a byte slice.
func MapRequestEventNew(buf []byte) xgb.Event {
	v := MapRequestEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Parent = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Bytes writes a MapRequestEvent value to a byte slice.
func (v MapRequestEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 20
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Parent))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	return buf
}

// SequenceId returns the sequence id attached to the MapRequest event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v MapRequestEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of MapRequestEvent.
func (v MapRequestEvent) String() string {
	fieldVals := make([]string, 0, 3)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Parent: %d", v.Parent))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	return "MapRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[20] = MapRequestEventNew
}

const (
	MapStateUnmapped   = 0
	MapStateUnviewable = 1
	MapStateViewable   = 2
)

const (
	MappingModifier = 0
	MappingKeyboard = 1
	MappingPointer  = 2
)

// MappingNotify is the event number for a MappingNotifyEvent.
const MappingNotify = 34

type MappingNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Request      byte
	FirstKeycode Keycode
	Count        byte
	// padding: 1 bytes
}

// MappingNotifyEventNew constructs a MappingNotifyEvent value that implements xgb.Event from a byte slice.
func MappingNotifyEventNew(buf []byte) xgb.Event {
	v := MappingNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Request = buf[b]
	b += 1

	v.FirstKeycode = Keycode(buf[b])
	b += 1

	v.Count = buf[b]
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a MappingNotifyEvent value to a byte slice.
func (v MappingNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 34
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	buf[b] = v.Request
	b += 1

	buf[b] = byte(v.FirstKeycode)
	b += 1

	buf[b] = v.Count
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the MappingNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v MappingNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of MappingNotifyEvent.
func (v MappingNotifyEvent) String() string {
	fieldVals := make([]string, 0, 5)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Request: %d", v.Request))
	fieldVals = append(fieldVals, xgb.Sprintf("FirstKeycode: %d", v.FirstKeycode))
	fieldVals = append(fieldVals, xgb.Sprintf("Count: %d", v.Count))
	return "MappingNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[34] = MappingNotifyEventNew
}

const (
	MappingStatusSuccess = 0
	MappingStatusBusy    = 1
	MappingStatusFailure = 2
)

// BadMatch is the error number for a BadMatch.
const BadMatch = 8

type MatchError RequestError

// MatchErrorNew constructs a MatchError value that implements xgb.Error from a byte slice.
func MatchErrorNew(buf []byte) xgb.Error {
	v := MatchError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Match"
	return v
}

// SequenceId returns the sequence id attached to the BadMatch error.
// This is mostly used internally.
func (err MatchError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadMatch error. If no bad value exists, 0 is returned.
func (err MatchError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadMatch error.
func (err MatchError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadMatch {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[8] = MatchErrorNew
}

const (
	ModMaskShift   = 1
	ModMaskLock    = 2
	ModMaskControl = 4
	ModMask1       = 8
	ModMask2       = 16
	ModMask3       = 32
	ModMask4       = 64
	ModMask5       = 128
	ModMaskAny     = 32768
)

const (
	MotionNormal = 0
	MotionHint   = 1
)

// MotionNotify is the event number for a MotionNotifyEvent.
const MotionNotify = 6

type MotionNotifyEvent struct {
	Sequence   uint16
	Detail     byte
	Time       Timestamp
	Root       Window
	Event      Window
	Child      Window
	RootX      int16
	RootY      int16
	EventX     int16
	EventY     int16
	State      uint16
	SameScreen bool
	// padding: 1 bytes
}

// MotionNotifyEventNew constructs a MotionNotifyEvent value that implements xgb.Event from a byte slice.
func MotionNotifyEventNew(buf []byte) xgb.Event {
	v := MotionNotifyEvent{}
	b := 1 // don't read event number

	v.Detail = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.RootX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RootY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.EventY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.State = xgb.Get16(buf[b:])
	b += 2

	if buf[b] == 1 {
		v.SameScreen = true
	} else {
		v.SameScreen = false
	}
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a MotionNotifyEvent value to a byte slice.
func (v MotionNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 6
	b += 1

	buf[b] = v.Detail
	b += 1

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Root))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Child))
	b += 4

	xgb.Put16(buf[b:], uint16(v.RootX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.RootY))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventX))
	b += 2

	xgb.Put16(buf[b:], uint16(v.EventY))
	b += 2

	xgb.Put16(buf[b:], v.State)
	b += 2

	if v.SameScreen {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the MotionNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v MotionNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of MotionNotifyEvent.
func (v MotionNotifyEvent) String() string {
	fieldVals := make([]string, 0, 12)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Detail: %d", v.Detail))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Root: %d", v.Root))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Child: %d", v.Child))
	fieldVals = append(fieldVals, xgb.Sprintf("RootX: %d", v.RootX))
	fieldVals = append(fieldVals, xgb.Sprintf("RootY: %d", v.RootY))
	fieldVals = append(fieldVals, xgb.Sprintf("EventX: %d", v.EventX))
	fieldVals = append(fieldVals, xgb.Sprintf("EventY: %d", v.EventY))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	fieldVals = append(fieldVals, xgb.Sprintf("SameScreen: %t", v.SameScreen))
	return "MotionNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[6] = MotionNotifyEventNew
}

// BadName is the error number for a BadName.
const BadName = 15

type NameError RequestError

// NameErrorNew constructs a NameError value that implements xgb.Error from a byte slice.
func NameErrorNew(buf []byte) xgb.Error {
	v := NameError(RequestErrorNew(buf).(RequestError))
	v.NiceName = "Name"
	return v
}

// SequenceId returns the sequence id attached to the BadName error.
// This is mostly used internally.
func (err NameError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadName error. If no bad value exists, 0 is returned.
func (err NameError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadName error.
func (err NameError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadName {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[15] = NameErrorNew
}

// NoExposure is the event number for a NoExposureEvent.
const NoExposure = 14

type NoExposureEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Drawable    Drawable
	MinorOpcode uint16
	MajorOpcode byte
	// padding: 1 bytes
}

// NoExposureEventNew constructs a NoExposureEvent value that implements xgb.Event from a byte slice.
func NoExposureEventNew(buf []byte) xgb.Event {
	v := NoExposureEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Drawable = Drawable(xgb.Get32(buf[b:]))
	b += 4

	v.MinorOpcode = xgb.Get16(buf[b:])
	b += 2

	v.MajorOpcode = buf[b]
	b += 1

	b += 1 // padding

	return v
}

// Bytes writes a NoExposureEvent value to a byte slice.
func (v NoExposureEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 14
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Drawable))
	b += 4

	xgb.Put16(buf[b:], v.MinorOpcode)
	b += 2

	buf[b] = v.MajorOpcode
	b += 1

	b += 1 // padding

	return buf
}

// SequenceId returns the sequence id attached to the NoExposure event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v NoExposureEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of NoExposureEvent.
func (v NoExposureEvent) String() string {
	fieldVals := make([]string, 0, 5)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Drawable: %d", v.Drawable))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", v.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", v.MajorOpcode))
	return "NoExposure {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[14] = NoExposureEventNew
}

const (
	NotifyDetailAncestor         = 0
	NotifyDetailVirtual          = 1
	NotifyDetailInferior         = 2
	NotifyDetailNonlinear        = 3
	NotifyDetailNonlinearVirtual = 4
	NotifyDetailPointer          = 5
	NotifyDetailPointerRoot      = 6
	NotifyDetailNone             = 7
)

const (
	NotifyModeNormal       = 0
	NotifyModeGrab         = 1
	NotifyModeUngrab       = 2
	NotifyModeWhileGrabbed = 3
)

type Pixmap uint32

func NewPixmapId(c *xgb.Conn) (Pixmap, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Pixmap(id), nil
}

// BadPixmap is the error number for a BadPixmap.
const BadPixmap = 4

type PixmapError ValueError

// PixmapErrorNew constructs a PixmapError value that implements xgb.Error from a byte slice.
func PixmapErrorNew(buf []byte) xgb.Error {
	v := PixmapError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Pixmap"
	return v
}

// SequenceId returns the sequence id attached to the BadPixmap error.
// This is mostly used internally.
func (err PixmapError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadPixmap error. If no bad value exists, 0 is returned.
func (err PixmapError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadPixmap error.
func (err PixmapError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadPixmap {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[4] = PixmapErrorNew
}

const (
	PixmapNone = 0
)

const (
	PlaceOnTop    = 0
	PlaceOnBottom = 1
)

type Point struct {
	X int16
	Y int16
}

// PointRead reads a byte slice into a Point value.
func PointRead(buf []byte, v *Point) int {
	b := 0

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	return b
}

// PointReadList reads a byte slice into a list of Point values.
func PointReadList(buf []byte, dest []Point) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Point{}
		b += PointRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Point value to a byte slice.
func (v Point) Bytes() []byte {
	buf := make([]byte, 4)
	b := 0

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	return buf[:b]
}

// PointListBytes writes a list of Point values to a byte slice.
func PointListBytes(buf []byte, list []Point) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

const (
	PolyShapeComplex   = 0
	PolyShapeNonconvex = 1
	PolyShapeConvex    = 2
)

const (
	PropModeReplace = 0
	PropModePrepend = 1
	PropModeAppend  = 2
)

const (
	PropertyNewValue = 0
	PropertyDelete   = 1
)

// PropertyNotify is the event number for a PropertyNotifyEvent.
const PropertyNotify = 28

type PropertyNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Window Window
	Atom   Atom
	Time   Timestamp
	State  byte
	// padding: 3 bytes
}

// PropertyNotifyEventNew constructs a PropertyNotifyEvent value that implements xgb.Event from a byte slice.
func PropertyNotifyEventNew(buf []byte) xgb.Event {
	v := PropertyNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Atom = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.State = buf[b]
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a PropertyNotifyEvent value to a byte slice.
func (v PropertyNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 28
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Atom))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	buf[b] = v.State
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the PropertyNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v PropertyNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of PropertyNotifyEvent.
func (v PropertyNotifyEvent) String() string {
	fieldVals := make([]string, 0, 6)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Atom: %d", v.Atom))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	return "PropertyNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[28] = PropertyNotifyEventNew
}

const (
	QueryShapeOfLargestCursor  = 0
	QueryShapeOfFastestTile    = 1
	QueryShapeOfFastestStipple = 2
)

type Rectangle struct {
	X      int16
	Y      int16
	Width  uint16
	Height uint16
}

// RectangleRead reads a byte slice into a Rectangle value.
func RectangleRead(buf []byte, v *Rectangle) int {
	b := 0

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	return b
}

// RectangleReadList reads a byte slice into a list of Rectangle values.
func RectangleReadList(buf []byte, dest []Rectangle) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Rectangle{}
		b += RectangleRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Rectangle value to a byte slice.
func (v Rectangle) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	return buf[:b]
}

// RectangleListBytes writes a list of Rectangle values to a byte slice.
func RectangleListBytes(buf []byte, list []Rectangle) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// ReparentNotify is the event number for a ReparentNotifyEvent.
const ReparentNotify = 21

type ReparentNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event            Window
	Window           Window
	Parent           Window
	X                int16
	Y                int16
	OverrideRedirect bool
	// padding: 3 bytes
}

// ReparentNotifyEventNew constructs a ReparentNotifyEvent value that implements xgb.Event from a byte slice.
func ReparentNotifyEventNew(buf []byte) xgb.Event {
	v := ReparentNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Parent = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	if buf[b] == 1 {
		v.OverrideRedirect = true
	} else {
		v.OverrideRedirect = false
	}
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a ReparentNotifyEvent value to a byte slice.
func (v ReparentNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 21
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Parent))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	if v.OverrideRedirect {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the ReparentNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ReparentNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ReparentNotifyEvent.
func (v ReparentNotifyEvent) String() string {
	fieldVals := make([]string, 0, 8)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Parent: %d", v.Parent))
	fieldVals = append(fieldVals, xgb.Sprintf("X: %d", v.X))
	fieldVals = append(fieldVals, xgb.Sprintf("Y: %d", v.Y))
	fieldVals = append(fieldVals, xgb.Sprintf("OverrideRedirect: %t", v.OverrideRedirect))
	return "ReparentNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[21] = ReparentNotifyEventNew
}

// BadRequest is the error number for a BadRequest.
const BadRequest = 1

type RequestError struct {
	Sequence    uint16
	NiceName    string
	BadValue    uint32
	MinorOpcode uint16
	MajorOpcode byte
	// padding: 1 bytes
}

// RequestErrorNew constructs a RequestError value that implements xgb.Error from a byte slice.
func RequestErrorNew(buf []byte) xgb.Error {
	v := RequestError{}
	v.NiceName = "Request"

	b := 1 // skip error determinant
	b += 1 // don't read error number

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.BadValue = xgb.Get32(buf[b:])
	b += 4

	v.MinorOpcode = xgb.Get16(buf[b:])
	b += 2

	v.MajorOpcode = buf[b]
	b += 1

	b += 1 // padding

	return v
}

// SequenceId returns the sequence id attached to the BadRequest error.
// This is mostly used internally.
func (err RequestError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadRequest error. If no bad value exists, 0 is returned.
func (err RequestError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadRequest error.

func (err RequestError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[1] = RequestErrorNew
}

// ResizeRequest is the event number for a ResizeRequestEvent.
const ResizeRequest = 25

type ResizeRequestEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Window Window
	Width  uint16
	Height uint16
}

// ResizeRequestEventNew constructs a ResizeRequestEvent value that implements xgb.Event from a byte slice.
func ResizeRequestEventNew(buf []byte) xgb.Event {
	v := ResizeRequestEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	return v
}

// Bytes writes a ResizeRequestEvent value to a byte slice.
func (v ResizeRequestEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 25
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	xgb.Put16(buf[b:], v.Width)
	b += 2

	xgb.Put16(buf[b:], v.Height)
	b += 2

	return buf
}

// SequenceId returns the sequence id attached to the ResizeRequest event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v ResizeRequestEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of ResizeRequestEvent.
func (v ResizeRequestEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("Width: %d", v.Width))
	fieldVals = append(fieldVals, xgb.Sprintf("Height: %d", v.Height))
	return "ResizeRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[25] = ResizeRequestEventNew
}

type Rgb struct {
	Red   uint16
	Green uint16
	Blue  uint16
	// padding: 2 bytes
}

// RgbRead reads a byte slice into a Rgb value.
func RgbRead(buf []byte, v *Rgb) int {
	b := 0

	v.Red = xgb.Get16(buf[b:])
	b += 2

	v.Green = xgb.Get16(buf[b:])
	b += 2

	v.Blue = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	return b
}

// RgbReadList reads a byte slice into a list of Rgb values.
func RgbReadList(buf []byte, dest []Rgb) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Rgb{}
		b += RgbRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Rgb value to a byte slice.
func (v Rgb) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	xgb.Put16(buf[b:], v.Red)
	b += 2

	xgb.Put16(buf[b:], v.Green)
	b += 2

	xgb.Put16(buf[b:], v.Blue)
	b += 2

	b += 2 // padding

	return buf[:b]
}

// RgbListBytes writes a list of Rgb values to a byte slice.
func RgbListBytes(buf []byte, list []Rgb) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type ScreenInfo struct {
	Root                Window
	DefaultColormap     Colormap
	WhitePixel          uint32
	BlackPixel          uint32
	CurrentInputMasks   uint32
	WidthInPixels       uint16
	HeightInPixels      uint16
	WidthInMillimeters  uint16
	HeightInMillimeters uint16
	MinInstalledMaps    uint16
	MaxInstalledMaps    uint16
	RootVisual          Visualid
	BackingStores       byte
	SaveUnders          bool
	RootDepth           byte
	AllowedDepthsLen    byte
	AllowedDepths       []DepthInfo // size: DepthInfoListSize(AllowedDepths)
}

// ScreenInfoRead reads a byte slice into a ScreenInfo value.
func ScreenInfoRead(buf []byte, v *ScreenInfo) int {
	b := 0

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.DefaultColormap = Colormap(xgb.Get32(buf[b:]))
	b += 4

	v.WhitePixel = xgb.Get32(buf[b:])
	b += 4

	v.BlackPixel = xgb.Get32(buf[b:])
	b += 4

	v.CurrentInputMasks = xgb.Get32(buf[b:])
	b += 4

	v.WidthInPixels = xgb.Get16(buf[b:])
	b += 2

	v.HeightInPixels = xgb.Get16(buf[b:])
	b += 2

	v.WidthInMillimeters = xgb.Get16(buf[b:])
	b += 2

	v.HeightInMillimeters = xgb.Get16(buf[b:])
	b += 2

	v.MinInstalledMaps = xgb.Get16(buf[b:])
	b += 2

	v.MaxInstalledMaps = xgb.Get16(buf[b:])
	b += 2

	v.RootVisual = Visualid(xgb.Get32(buf[b:]))
	b += 4

	v.BackingStores = buf[b]
	b += 1

	if buf[b] == 1 {
		v.SaveUnders = true
	} else {
		v.SaveUnders = false
	}
	b += 1

	v.RootDepth = buf[b]
	b += 1

	v.AllowedDepthsLen = buf[b]
	b += 1

	v.AllowedDepths = make([]DepthInfo, v.AllowedDepthsLen)
	b += DepthInfoReadList(buf[b:], v.AllowedDepths)

	return b
}

// ScreenInfoReadList reads a byte slice into a list of ScreenInfo values.
func ScreenInfoReadList(buf []byte, dest []ScreenInfo) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = ScreenInfo{}
		b += ScreenInfoRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a ScreenInfo value to a byte slice.
func (v ScreenInfo) Bytes() []byte {
	buf := make([]byte, (40 + DepthInfoListSize(v.AllowedDepths)))
	b := 0

	xgb.Put32(buf[b:], uint32(v.Root))
	b += 4

	xgb.Put32(buf[b:], uint32(v.DefaultColormap))
	b += 4

	xgb.Put32(buf[b:], v.WhitePixel)
	b += 4

	xgb.Put32(buf[b:], v.BlackPixel)
	b += 4

	xgb.Put32(buf[b:], v.CurrentInputMasks)
	b += 4

	xgb.Put16(buf[b:], v.WidthInPixels)
	b += 2

	xgb.Put16(buf[b:], v.HeightInPixels)
	b += 2

	xgb.Put16(buf[b:], v.WidthInMillimeters)
	b += 2

	xgb.Put16(buf[b:], v.HeightInMillimeters)
	b += 2

	xgb.Put16(buf[b:], v.MinInstalledMaps)
	b += 2

	xgb.Put16(buf[b:], v.MaxInstalledMaps)
	b += 2

	xgb.Put32(buf[b:], uint32(v.RootVisual))
	b += 4

	buf[b] = v.BackingStores
	b += 1

	if v.SaveUnders {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	buf[b] = v.RootDepth
	b += 1

	buf[b] = v.AllowedDepthsLen
	b += 1

	b += DepthInfoListBytes(buf[b:], v.AllowedDepths)

	return buf[:b]
}

// ScreenInfoListBytes writes a list of ScreenInfo values to a byte slice.
func ScreenInfoListBytes(buf []byte, list []ScreenInfo) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// ScreenInfoListSize computes the size (bytes) of a list of ScreenInfo values.
func ScreenInfoListSize(list []ScreenInfo) int {
	size := 0
	for _, item := range list {
		size += (40 + DepthInfoListSize(item.AllowedDepths))
	}
	return size
}

const (
	ScreenSaverReset  = 0
	ScreenSaverActive = 1
)

type Segment struct {
	X1 int16
	Y1 int16
	X2 int16
	Y2 int16
}

// SegmentRead reads a byte slice into a Segment value.
func SegmentRead(buf []byte, v *Segment) int {
	b := 0

	v.X1 = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y1 = int16(xgb.Get16(buf[b:]))
	b += 2

	v.X2 = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y2 = int16(xgb.Get16(buf[b:]))
	b += 2

	return b
}

// SegmentReadList reads a byte slice into a list of Segment values.
func SegmentReadList(buf []byte, dest []Segment) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Segment{}
		b += SegmentRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Segment value to a byte slice.
func (v Segment) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	xgb.Put16(buf[b:], uint16(v.X1))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y1))
	b += 2

	xgb.Put16(buf[b:], uint16(v.X2))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y2))
	b += 2

	return buf[:b]
}

// SegmentListBytes writes a list of Segment values to a byte slice.
func SegmentListBytes(buf []byte, list []Segment) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// SelectionClear is the event number for a SelectionClearEvent.
const SelectionClear = 29

type SelectionClearEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Time      Timestamp
	Owner     Window
	Selection Atom
}

// SelectionClearEventNew constructs a SelectionClearEvent value that implements xgb.Event from a byte slice.
func SelectionClearEventNew(buf []byte) xgb.Event {
	v := SelectionClearEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Owner = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Selection = Atom(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Bytes writes a SelectionClearEvent value to a byte slice.
func (v SelectionClearEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 29
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Owner))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Selection))
	b += 4

	return buf
}

// SequenceId returns the sequence id attached to the SelectionClear event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v SelectionClearEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of SelectionClearEvent.
func (v SelectionClearEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Owner: %d", v.Owner))
	fieldVals = append(fieldVals, xgb.Sprintf("Selection: %d", v.Selection))
	return "SelectionClear {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[29] = SelectionClearEventNew
}

// SelectionNotify is the event number for a SelectionNotifyEvent.
const SelectionNotify = 31

type SelectionNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Time      Timestamp
	Requestor Window
	Selection Atom
	Target    Atom
	Property  Atom
}

// SelectionNotifyEventNew constructs a SelectionNotifyEvent value that implements xgb.Event from a byte slice.
func SelectionNotifyEventNew(buf []byte) xgb.Event {
	v := SelectionNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Requestor = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Selection = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Target = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Property = Atom(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Bytes writes a SelectionNotifyEvent value to a byte slice.
func (v SelectionNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 31
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Requestor))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Selection))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Target))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Property))
	b += 4

	return buf
}

// SequenceId returns the sequence id attached to the SelectionNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v SelectionNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of SelectionNotifyEvent.
func (v SelectionNotifyEvent) String() string {
	fieldVals := make([]string, 0, 6)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Requestor: %d", v.Requestor))
	fieldVals = append(fieldVals, xgb.Sprintf("Selection: %d", v.Selection))
	fieldVals = append(fieldVals, xgb.Sprintf("Target: %d", v.Target))
	fieldVals = append(fieldVals, xgb.Sprintf("Property: %d", v.Property))
	return "SelectionNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[31] = SelectionNotifyEventNew
}

// SelectionRequest is the event number for a SelectionRequestEvent.
const SelectionRequest = 30

type SelectionRequestEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Time      Timestamp
	Owner     Window
	Requestor Window
	Selection Atom
	Target    Atom
	Property  Atom
}

// SelectionRequestEventNew constructs a SelectionRequestEvent value that implements xgb.Event from a byte slice.
func SelectionRequestEventNew(buf []byte) xgb.Event {
	v := SelectionRequestEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.Owner = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Requestor = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Selection = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Target = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.Property = Atom(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Bytes writes a SelectionRequestEvent value to a byte slice.
func (v SelectionRequestEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 30
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Owner))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Requestor))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Selection))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Target))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Property))
	b += 4

	return buf
}

// SequenceId returns the sequence id attached to the SelectionRequest event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v SelectionRequestEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of SelectionRequestEvent.
func (v SelectionRequestEvent) String() string {
	fieldVals := make([]string, 0, 7)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Time: %d", v.Time))
	fieldVals = append(fieldVals, xgb.Sprintf("Owner: %d", v.Owner))
	fieldVals = append(fieldVals, xgb.Sprintf("Requestor: %d", v.Requestor))
	fieldVals = append(fieldVals, xgb.Sprintf("Selection: %d", v.Selection))
	fieldVals = append(fieldVals, xgb.Sprintf("Target: %d", v.Target))
	fieldVals = append(fieldVals, xgb.Sprintf("Property: %d", v.Property))
	return "SelectionRequest {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[30] = SelectionRequestEventNew
}

const (
	SendEventDestPointerWindow = 0
	SendEventDestItemFocus     = 1
)

const (
	SetModeInsert = 0
	SetModeDelete = 1
)

type SetupAuthenticate struct {
	Status byte
	// padding: 5 bytes
	Length uint16
	Reason string // size: xgb.Pad(((int(Length) * 4) * 1))
}

// SetupAuthenticateRead reads a byte slice into a SetupAuthenticate value.
func SetupAuthenticateRead(buf []byte, v *SetupAuthenticate) int {
	b := 0

	v.Status = buf[b]
	b += 1

	b += 5 // padding

	v.Length = xgb.Get16(buf[b:])
	b += 2

	{
		byteString := make([]byte, (int(v.Length) * 4))
		copy(byteString[:(int(v.Length)*4)], buf[b:])
		v.Reason = string(byteString)
		b += int((int(v.Length) * 4))
	}

	return b
}

// SetupAuthenticateReadList reads a byte slice into a list of SetupAuthenticate values.
func SetupAuthenticateReadList(buf []byte, dest []SetupAuthenticate) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = SetupAuthenticate{}
		b += SetupAuthenticateRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a SetupAuthenticate value to a byte slice.
func (v SetupAuthenticate) Bytes() []byte {
	buf := make([]byte, (8 + xgb.Pad(((int(v.Length) * 4) * 1))))
	b := 0

	buf[b] = v.Status
	b += 1

	b += 5 // padding

	xgb.Put16(buf[b:], v.Length)
	b += 2

	copy(buf[b:], v.Reason[:(int(v.Length)*4)])
	b += int((int(v.Length) * 4))

	return buf[:b]
}

// SetupAuthenticateListBytes writes a list of SetupAuthenticate values to a byte slice.
func SetupAuthenticateListBytes(buf []byte, list []SetupAuthenticate) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// SetupAuthenticateListSize computes the size (bytes) of a list of SetupAuthenticate values.
func SetupAuthenticateListSize(list []SetupAuthenticate) int {
	size := 0
	for _, item := range list {
		size += (8 + xgb.Pad(((int(item.Length) * 4) * 1)))
	}
	return size
}

type SetupFailed struct {
	Status               byte
	ReasonLen            byte
	ProtocolMajorVersion uint16
	ProtocolMinorVersion uint16
	Length               uint16
	Reason               string // size: xgb.Pad((int(ReasonLen) * 1))
}

// SetupFailedRead reads a byte slice into a SetupFailed value.
func SetupFailedRead(buf []byte, v *SetupFailed) int {
	b := 0

	v.Status = buf[b]
	b += 1

	v.ReasonLen = buf[b]
	b += 1

	v.ProtocolMajorVersion = xgb.Get16(buf[b:])
	b += 2

	v.ProtocolMinorVersion = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get16(buf[b:])
	b += 2

	{
		byteString := make([]byte, v.ReasonLen)
		copy(byteString[:v.ReasonLen], buf[b:])
		v.Reason = string(byteString)
		b += int(v.ReasonLen)
	}

	return b
}

// SetupFailedReadList reads a byte slice into a list of SetupFailed values.
func SetupFailedReadList(buf []byte, dest []SetupFailed) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = SetupFailed{}
		b += SetupFailedRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a SetupFailed value to a byte slice.
func (v SetupFailed) Bytes() []byte {
	buf := make([]byte, (8 + xgb.Pad((int(v.ReasonLen) * 1))))
	b := 0

	buf[b] = v.Status
	b += 1

	buf[b] = v.ReasonLen
	b += 1

	xgb.Put16(buf[b:], v.ProtocolMajorVersion)
	b += 2

	xgb.Put16(buf[b:], v.ProtocolMinorVersion)
	b += 2

	xgb.Put16(buf[b:], v.Length)
	b += 2

	copy(buf[b:], v.Reason[:v.ReasonLen])
	b += int(v.ReasonLen)

	return buf[:b]
}

// SetupFailedListBytes writes a list of SetupFailed values to a byte slice.
func SetupFailedListBytes(buf []byte, list []SetupFailed) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// SetupFailedListSize computes the size (bytes) of a list of SetupFailed values.
func SetupFailedListSize(list []SetupFailed) int {
	size := 0
	for _, item := range list {
		size += (8 + xgb.Pad((int(item.ReasonLen) * 1)))
	}
	return size
}

type SetupInfo struct {
	Status byte
	// padding: 1 bytes
	ProtocolMajorVersion     uint16
	ProtocolMinorVersion     uint16
	Length                   uint16
	ReleaseNumber            uint32
	ResourceIdBase           uint32
	ResourceIdMask           uint32
	MotionBufferSize         uint32
	VendorLen                uint16
	MaximumRequestLength     uint16
	RootsLen                 byte
	PixmapFormatsLen         byte
	ImageByteOrder           byte
	BitmapFormatBitOrder     byte
	BitmapFormatScanlineUnit byte
	BitmapFormatScanlinePad  byte
	MinKeycode               Keycode
	MaxKeycode               Keycode
	// padding: 4 bytes
	Vendor string // size: xgb.Pad((int(VendorLen) * 1))
	// alignment gap to multiple of 4
	PixmapFormats []Format     // size: xgb.Pad((int(PixmapFormatsLen) * 8))
	Roots         []ScreenInfo // size: ScreenInfoListSize(Roots)
}

// SetupInfoRead reads a byte slice into a SetupInfo value.
func SetupInfoRead(buf []byte, v *SetupInfo) int {
	b := 0

	v.Status = buf[b]
	b += 1

	b += 1 // padding

	v.ProtocolMajorVersion = xgb.Get16(buf[b:])
	b += 2

	v.ProtocolMinorVersion = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get16(buf[b:])
	b += 2

	v.ReleaseNumber = xgb.Get32(buf[b:])
	b += 4

	v.ResourceIdBase = xgb.Get32(buf[b:])
	b += 4

	v.ResourceIdMask = xgb.Get32(buf[b:])
	b += 4

	v.MotionBufferSize = xgb.Get32(buf[b:])
	b += 4

	v.VendorLen = xgb.Get16(buf[b:])
	b += 2

	v.MaximumRequestLength = xgb.Get16(buf[b:])
	b += 2

	v.RootsLen = buf[b]
	b += 1

	v.PixmapFormatsLen = buf[b]
	b += 1

	v.ImageByteOrder = buf[b]
	b += 1

	v.BitmapFormatBitOrder = buf[b]
	b += 1

	v.BitmapFormatScanlineUnit = buf[b]
	b += 1

	v.BitmapFormatScanlinePad = buf[b]
	b += 1

	v.MinKeycode = Keycode(buf[b])
	b += 1

	v.MaxKeycode = Keycode(buf[b])
	b += 1

	b += 4 // padding

	{
		byteString := make([]byte, v.VendorLen)
		copy(byteString[:v.VendorLen], buf[b:])
		v.Vendor = string(byteString)
		b += int(v.VendorLen)
	}

	b = (b + 3) & ^3 // alignment gap

	v.PixmapFormats = make([]Format, v.PixmapFormatsLen)
	b += FormatReadList(buf[b:], v.PixmapFormats)

	v.Roots = make([]ScreenInfo, v.RootsLen)
	b += ScreenInfoReadList(buf[b:], v.Roots)

	return b
}

// SetupInfoReadList reads a byte slice into a list of SetupInfo values.
func SetupInfoReadList(buf []byte, dest []SetupInfo) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = SetupInfo{}
		b += SetupInfoRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a SetupInfo value to a byte slice.
func (v SetupInfo) Bytes() []byte {
	buf := make([]byte, ((((40 + xgb.Pad((int(v.VendorLen) * 1))) + 4) + xgb.Pad((int(v.PixmapFormatsLen) * 8))) + ScreenInfoListSize(v.Roots)))
	b := 0

	buf[b] = v.Status
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], v.ProtocolMajorVersion)
	b += 2

	xgb.Put16(buf[b:], v.ProtocolMinorVersion)
	b += 2

	xgb.Put16(buf[b:], v.Length)
	b += 2

	xgb.Put32(buf[b:], v.ReleaseNumber)
	b += 4

	xgb.Put32(buf[b:], v.ResourceIdBase)
	b += 4

	xgb.Put32(buf[b:], v.ResourceIdMask)
	b += 4

	xgb.Put32(buf[b:], v.MotionBufferSize)
	b += 4

	xgb.Put16(buf[b:], v.VendorLen)
	b += 2

	xgb.Put16(buf[b:], v.MaximumRequestLength)
	b += 2

	buf[b] = v.RootsLen
	b += 1

	buf[b] = v.PixmapFormatsLen
	b += 1

	buf[b] = v.ImageByteOrder
	b += 1

	buf[b] = v.BitmapFormatBitOrder
	b += 1

	buf[b] = v.BitmapFormatScanlineUnit
	b += 1

	buf[b] = v.BitmapFormatScanlinePad
	b += 1

	buf[b] = byte(v.MinKeycode)
	b += 1

	buf[b] = byte(v.MaxKeycode)
	b += 1

	b += 4 // padding

	copy(buf[b:], v.Vendor[:v.VendorLen])
	b += int(v.VendorLen)

	b = (b + 3) & ^3 // alignment gap

	b += FormatListBytes(buf[b:], v.PixmapFormats)

	b += ScreenInfoListBytes(buf[b:], v.Roots)

	return buf[:b]
}

// SetupInfoListBytes writes a list of SetupInfo values to a byte slice.
func SetupInfoListBytes(buf []byte, list []SetupInfo) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// SetupInfoListSize computes the size (bytes) of a list of SetupInfo values.
func SetupInfoListSize(list []SetupInfo) int {
	size := 0
	for _, item := range list {
		size += ((((40 + xgb.Pad((int(item.VendorLen) * 1))) + 4) + xgb.Pad((int(item.PixmapFormatsLen) * 8))) + ScreenInfoListSize(item.Roots))
	}
	return size
}

type SetupRequest struct {
	ByteOrder byte
	// padding: 1 bytes
	ProtocolMajorVersion         uint16
	ProtocolMinorVersion         uint16
	AuthorizationProtocolNameLen uint16
	AuthorizationProtocolDataLen uint16
	// padding: 2 bytes
	AuthorizationProtocolName string // size: xgb.Pad((int(AuthorizationProtocolNameLen) * 1))
	// alignment gap to multiple of 4
	AuthorizationProtocolData string // size: xgb.Pad((int(AuthorizationProtocolDataLen) * 1))
	// alignment gap to multiple of 4
}

// SetupRequestRead reads a byte slice into a SetupRequest value.
func SetupRequestRead(buf []byte, v *SetupRequest) int {
	b := 0

	v.ByteOrder = buf[b]
	b += 1

	b += 1 // padding

	v.ProtocolMajorVersion = xgb.Get16(buf[b:])
	b += 2

	v.ProtocolMinorVersion = xgb.Get16(buf[b:])
	b += 2

	v.AuthorizationProtocolNameLen = xgb.Get16(buf[b:])
	b += 2

	v.AuthorizationProtocolDataLen = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	{
		byteString := make([]byte, v.AuthorizationProtocolNameLen)
		copy(byteString[:v.AuthorizationProtocolNameLen], buf[b:])
		v.AuthorizationProtocolName = string(byteString)
		b += int(v.AuthorizationProtocolNameLen)
	}

	b = (b + 3) & ^3 // alignment gap

	{
		byteString := make([]byte, v.AuthorizationProtocolDataLen)
		copy(byteString[:v.AuthorizationProtocolDataLen], buf[b:])
		v.AuthorizationProtocolData = string(byteString)
		b += int(v.AuthorizationProtocolDataLen)
	}

	b = (b + 3) & ^3 // alignment gap

	return b
}

// SetupRequestReadList reads a byte slice into a list of SetupRequest values.
func SetupRequestReadList(buf []byte, dest []SetupRequest) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = SetupRequest{}
		b += SetupRequestRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a SetupRequest value to a byte slice.
func (v SetupRequest) Bytes() []byte {
	buf := make([]byte, ((((12 + xgb.Pad((int(v.AuthorizationProtocolNameLen) * 1))) + 4) + xgb.Pad((int(v.AuthorizationProtocolDataLen) * 1))) + 4))
	b := 0

	buf[b] = v.ByteOrder
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], v.ProtocolMajorVersion)
	b += 2

	xgb.Put16(buf[b:], v.ProtocolMinorVersion)
	b += 2

	xgb.Put16(buf[b:], v.AuthorizationProtocolNameLen)
	b += 2

	xgb.Put16(buf[b:], v.AuthorizationProtocolDataLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], v.AuthorizationProtocolName[:v.AuthorizationProtocolNameLen])
	b += int(v.AuthorizationProtocolNameLen)

	b = (b + 3) & ^3 // alignment gap

	copy(buf[b:], v.AuthorizationProtocolData[:v.AuthorizationProtocolDataLen])
	b += int(v.AuthorizationProtocolDataLen)

	b = (b + 3) & ^3 // alignment gap

	return buf[:b]
}

// SetupRequestListBytes writes a list of SetupRequest values to a byte slice.
func SetupRequestListBytes(buf []byte, list []SetupRequest) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// SetupRequestListSize computes the size (bytes) of a list of SetupRequest values.
func SetupRequestListSize(list []SetupRequest) int {
	size := 0
	for _, item := range list {
		size += ((((12 + xgb.Pad((int(item.AuthorizationProtocolNameLen) * 1))) + 4) + xgb.Pad((int(item.AuthorizationProtocolDataLen) * 1))) + 4)
	}
	return size
}

const (
	StackModeAbove    = 0
	StackModeBelow    = 1
	StackModeTopIf    = 2
	StackModeBottomIf = 3
	StackModeOpposite = 4
)

type Str struct {
	NameLen byte
	Name    string // size: xgb.Pad((int(NameLen) * 1))
}

// StrRead reads a byte slice into a Str value.
func StrRead(buf []byte, v *Str) int {
	b := 0

	v.NameLen = buf[b]
	b += 1

	{
		byteString := make([]byte, v.NameLen)
		copy(byteString[:v.NameLen], buf[b:])
		v.Name = string(byteString)
		b += int(v.NameLen)
	}

	return b
}

// StrReadList reads a byte slice into a list of Str values.
func StrReadList(buf []byte, dest []Str) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Str{}
		b += StrRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Str value to a byte slice.
func (v Str) Bytes() []byte {
	buf := make([]byte, (1 + xgb.Pad((int(v.NameLen) * 1))))
	b := 0

	buf[b] = v.NameLen
	b += 1

	copy(buf[b:], v.Name[:v.NameLen])
	b += int(v.NameLen)

	return buf[:b]
}

// StrListBytes writes a list of Str values to a byte slice.
func StrListBytes(buf []byte, list []Str) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

// StrListSize computes the size (bytes) of a list of Str values.
func StrListSize(list []Str) int {
	size := 0
	for _, item := range list {
		size += (1 + xgb.Pad((int(item.NameLen) * 1)))
	}
	return size
}

const (
	SubwindowModeClipByChildren   = 0
	SubwindowModeIncludeInferiors = 1
)

const (
	TimeCurrentTime = 0
)

type Timecoord struct {
	Time Timestamp
	X    int16
	Y    int16
}

// TimecoordRead reads a byte slice into a Timecoord value.
func TimecoordRead(buf []byte, v *Timecoord) int {
	b := 0

	v.Time = Timestamp(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	return b
}

// TimecoordReadList reads a byte slice into a list of Timecoord values.
func TimecoordReadList(buf []byte, dest []Timecoord) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = Timecoord{}
		b += TimecoordRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a Timecoord value to a byte slice.
func (v Timecoord) Bytes() []byte {
	buf := make([]byte, 8)
	b := 0

	xgb.Put32(buf[b:], uint32(v.Time))
	b += 4

	xgb.Put16(buf[b:], uint16(v.X))
	b += 2

	xgb.Put16(buf[b:], uint16(v.Y))
	b += 2

	return buf[:b]
}

// TimecoordListBytes writes a list of Timecoord values to a byte slice.
func TimecoordListBytes(buf []byte, list []Timecoord) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type Timestamp uint32

// UnmapNotify is the event number for a UnmapNotifyEvent.
const UnmapNotify = 18

type UnmapNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Event         Window
	Window        Window
	FromConfigure bool
	// padding: 3 bytes
}

// UnmapNotifyEventNew constructs a UnmapNotifyEvent value that implements xgb.Event from a byte slice.
func UnmapNotifyEventNew(buf []byte) xgb.Event {
	v := UnmapNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Event = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	if buf[b] == 1 {
		v.FromConfigure = true
	} else {
		v.FromConfigure = false
	}
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a UnmapNotifyEvent value to a byte slice.
func (v UnmapNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 18
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Event))
	b += 4

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	if v.FromConfigure {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the UnmapNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v UnmapNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of UnmapNotifyEvent.
func (v UnmapNotifyEvent) String() string {
	fieldVals := make([]string, 0, 5)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Event: %d", v.Event))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("FromConfigure: %t", v.FromConfigure))
	return "UnmapNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[18] = UnmapNotifyEventNew
}

// BadValue is the error number for a BadValue.
const BadValue = 2

type ValueError struct {
	Sequence    uint16
	NiceName    string
	BadValue    uint32
	MinorOpcode uint16
	MajorOpcode byte
	// padding: 1 bytes
}

// ValueErrorNew constructs a ValueError value that implements xgb.Error from a byte slice.
func ValueErrorNew(buf []byte) xgb.Error {
	v := ValueError{}
	v.NiceName = "Value"

	b := 1 // skip error determinant
	b += 1 // don't read error number

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.BadValue = xgb.Get32(buf[b:])
	b += 4

	v.MinorOpcode = xgb.Get16(buf[b:])
	b += 2

	v.MajorOpcode = buf[b]
	b += 1

	b += 1 // padding

	return v
}

// SequenceId returns the sequence id attached to the BadValue error.
// This is mostly used internally.
func (err ValueError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadValue error. If no bad value exists, 0 is returned.
func (err ValueError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadValue error.

func (err ValueError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadValue {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[2] = ValueErrorNew
}

const (
	VisibilityUnobscured        = 0
	VisibilityPartiallyObscured = 1
	VisibilityFullyObscured     = 2
)

// VisibilityNotify is the event number for a VisibilityNotifyEvent.
const VisibilityNotify = 15

type VisibilityNotifyEvent struct {
	Sequence uint16
	// padding: 1 bytes
	Window Window
	State  byte
	// padding: 3 bytes
}

// VisibilityNotifyEventNew constructs a VisibilityNotifyEvent value that implements xgb.Event from a byte slice.
func VisibilityNotifyEventNew(buf []byte) xgb.Event {
	v := VisibilityNotifyEvent{}
	b := 1 // don't read event number

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Window = Window(xgb.Get32(buf[b:]))
	b += 4

	v.State = buf[b]
	b += 1

	b += 3 // padding

	return v
}

// Bytes writes a VisibilityNotifyEvent value to a byte slice.
func (v VisibilityNotifyEvent) Bytes() []byte {
	buf := make([]byte, 32)
	b := 0

	// write event number
	buf[b] = 15
	b += 1

	b += 1 // padding

	b += 2 // skip sequence number

	xgb.Put32(buf[b:], uint32(v.Window))
	b += 4

	buf[b] = v.State
	b += 1

	b += 3 // padding

	return buf
}

// SequenceId returns the sequence id attached to the VisibilityNotify event.
// Events without a sequence number (KeymapNotify) return 0.
// This is mostly used internally.
func (v VisibilityNotifyEvent) SequenceId() uint16 {
	return v.Sequence
}

// String is a rudimentary string representation of VisibilityNotifyEvent.
func (v VisibilityNotifyEvent) String() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", v.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("Window: %d", v.Window))
	fieldVals = append(fieldVals, xgb.Sprintf("State: %d", v.State))
	return "VisibilityNotify {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewEventFuncs[15] = VisibilityNotifyEventNew
}

const (
	VisualClassStaticGray  = 0
	VisualClassGrayScale   = 1
	VisualClassStaticColor = 2
	VisualClassPseudoColor = 3
	VisualClassTrueColor   = 4
	VisualClassDirectColor = 5
)

type VisualInfo struct {
	VisualId        Visualid
	Class           byte
	BitsPerRgbValue byte
	ColormapEntries uint16
	RedMask         uint32
	GreenMask       uint32
	BlueMask        uint32
	// padding: 4 bytes
}

// VisualInfoRead reads a byte slice into a VisualInfo value.
func VisualInfoRead(buf []byte, v *VisualInfo) int {
	b := 0

	v.VisualId = Visualid(xgb.Get32(buf[b:]))
	b += 4

	v.Class = buf[b]
	b += 1

	v.BitsPerRgbValue = buf[b]
	b += 1

	v.ColormapEntries = xgb.Get16(buf[b:])
	b += 2

	v.RedMask = xgb.Get32(buf[b:])
	b += 4

	v.GreenMask = xgb.Get32(buf[b:])
	b += 4

	v.BlueMask = xgb.Get32(buf[b:])
	b += 4

	b += 4 // padding

	return b
}

// VisualInfoReadList reads a byte slice into a list of VisualInfo values.
func VisualInfoReadList(buf []byte, dest []VisualInfo) int {
	b := 0
	for i := 0; i < len(dest); i++ {
		dest[i] = VisualInfo{}
		b += VisualInfoRead(buf[b:], &dest[i])
	}
	return xgb.Pad(b)
}

// Bytes writes a VisualInfo value to a byte slice.
func (v VisualInfo) Bytes() []byte {
	buf := make([]byte, 24)
	b := 0

	xgb.Put32(buf[b:], uint32(v.VisualId))
	b += 4

	buf[b] = v.Class
	b += 1

	buf[b] = v.BitsPerRgbValue
	b += 1

	xgb.Put16(buf[b:], v.ColormapEntries)
	b += 2

	xgb.Put32(buf[b:], v.RedMask)
	b += 4

	xgb.Put32(buf[b:], v.GreenMask)
	b += 4

	xgb.Put32(buf[b:], v.BlueMask)
	b += 4

	b += 4 // padding

	return buf[:b]
}

// VisualInfoListBytes writes a list of VisualInfo values to a byte slice.
func VisualInfoListBytes(buf []byte, list []VisualInfo) int {
	b := 0
	var structBytes []byte
	for _, item := range list {
		structBytes = item.Bytes()
		copy(buf[b:], structBytes)
		b += len(structBytes)
	}
	return xgb.Pad(b)
}

type Visualid uint32

type Window uint32

func NewWindowId(c *xgb.Conn) (Window, error) {
	id, err := c.NewId()
	if err != nil {
		return 0, err
	}
	return Window(id), nil
}

const (
	WindowNone = 0
)

// BadWindow is the error number for a BadWindow.
const BadWindow = 3

type WindowError ValueError

// WindowErrorNew constructs a WindowError value that implements xgb.Error from a byte slice.
func WindowErrorNew(buf []byte) xgb.Error {
	v := WindowError(ValueErrorNew(buf).(ValueError))
	v.NiceName = "Window"
	return v
}

// SequenceId returns the sequence id attached to the BadWindow error.
// This is mostly used internally.
func (err WindowError) SequenceId() uint16 {
	return err.Sequence
}

// BadId returns the 'BadValue' number if one exists for the BadWindow error. If no bad value exists, 0 is returned.
func (err WindowError) BadId() uint32 {
	return err.BadValue
}

// Error returns a rudimentary string representation of the BadWindow error.
func (err WindowError) Error() string {
	fieldVals := make([]string, 0, 4)
	fieldVals = append(fieldVals, "NiceName: "+err.NiceName)
	fieldVals = append(fieldVals, xgb.Sprintf("Sequence: %d", err.Sequence))
	fieldVals = append(fieldVals, xgb.Sprintf("BadValue: %d", err.BadValue))
	fieldVals = append(fieldVals, xgb.Sprintf("MinorOpcode: %d", err.MinorOpcode))
	fieldVals = append(fieldVals, xgb.Sprintf("MajorOpcode: %d", err.MajorOpcode))
	return "BadWindow {" + xgb.StringsJoin(fieldVals, ", ") + "}"
}

func init() {
	xgb.NewErrorFuncs[3] = WindowErrorNew
}

const (
	WindowClassCopyFromParent = 0
	WindowClassInputOutput    = 1
	WindowClassInputOnly      = 2
)

// Skipping definition for base type 'Bool'

// Skipping definition for base type 'Byte'

// Skipping definition for base type 'Card8'

// Skipping definition for base type 'Char'

// Skipping definition for base type 'Void'

// Skipping definition for base type 'Double'

// Skipping definition for base type 'Float'

// Skipping definition for base type 'Int16'

// Skipping definition for base type 'Int32'

// Skipping definition for base type 'Int8'

// Skipping definition for base type 'Card16'

// Skipping definition for base type 'Card32'

// AllocColorCookie is a cookie used only for AllocColor requests.
type AllocColorCookie struct {
	*xgb.Cookie
}

// AllocColor sends a checked request.
// If an error occurs, it will be returned with the reply by calling AllocColorCookie.Reply()
func AllocColor(c *xgb.Conn, Cmap Colormap, Red uint16, Green uint16, Blue uint16) AllocColorCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(allocColorRequest(c, Cmap, Red, Green, Blue), cookie)
	return AllocColorCookie{cookie}
}

// AllocColorUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func AllocColorUnchecked(c *xgb.Conn, Cmap Colormap, Red uint16, Green uint16, Blue uint16) AllocColorCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(allocColorRequest(c, Cmap, Red, Green, Blue), cookie)
	return AllocColorCookie{cookie}
}

// AllocColorReply represents the data returned from a AllocColor request.
type AllocColorReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Red   uint16
	Green uint16
	Blue  uint16
	// padding: 2 bytes
	Pixel uint32
}

// Reply blocks and returns the reply data for a AllocColor request.
func (cook AllocColorCookie) Reply() (*AllocColorReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return allocColorReply(buf), nil
}

// allocColorReply reads a byte slice into a AllocColorReply value.
func allocColorReply(buf []byte) *AllocColorReply {
	v := new(AllocColorReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Red = xgb.Get16(buf[b:])
	b += 2

	v.Green = xgb.Get16(buf[b:])
	b += 2

	v.Blue = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	v.Pixel = xgb.Get32(buf[b:])
	b += 4

	return v
}

// Write request to wire for AllocColor
// allocColorRequest writes a AllocColor request to a byte slice.
func allocColorRequest(c *xgb.Conn, Cmap Colormap, Red uint16, Green uint16, Blue uint16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 84 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put16(buf[b:], Red)
	b += 2

	xgb.Put16(buf[b:], Green)
	b += 2

	xgb.Put16(buf[b:], Blue)
	b += 2

	b += 2 // padding

	return buf
}

// AllocColorCellsCookie is a cookie used only for AllocColorCells requests.
type AllocColorCellsCookie struct {
	*xgb.Cookie
}

// AllocColorCells sends a checked request.
// If an error occurs, it will be returned with the reply by calling AllocColorCellsCookie.Reply()
func AllocColorCells(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Planes uint16) AllocColorCellsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(allocColorCellsRequest(c, Contiguous, Cmap, Colors, Planes), cookie)
	return AllocColorCellsCookie{cookie}
}

// AllocColorCellsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func AllocColorCellsUnchecked(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Planes uint16) AllocColorCellsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(allocColorCellsRequest(c, Contiguous, Cmap, Colors, Planes), cookie)
	return AllocColorCellsCookie{cookie}
}

// AllocColorCellsReply represents the data returned from a AllocColorCells request.
type AllocColorCellsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	PixelsLen uint16
	MasksLen  uint16
	// padding: 20 bytes
	Pixels []uint32 // size: xgb.Pad((int(PixelsLen) * 4))
	Masks  []uint32 // size: xgb.Pad((int(MasksLen) * 4))
}

// Reply blocks and returns the reply data for a AllocColorCells request.
func (cook AllocColorCellsCookie) Reply() (*AllocColorCellsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return allocColorCellsReply(buf), nil
}

// allocColorCellsReply reads a byte slice into a AllocColorCellsReply value.
func allocColorCellsReply(buf []byte) *AllocColorCellsReply {
	v := new(AllocColorCellsReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.PixelsLen = xgb.Get16(buf[b:])
	b += 2

	v.MasksLen = xgb.Get16(buf[b:])
	b += 2

	b += 20 // padding

	v.Pixels = make([]uint32, v.PixelsLen)
	for i := 0; i < int(v.PixelsLen); i++ {
		v.Pixels[i] = xgb.Get32(buf[b:])
		b += 4
	}

	v.Masks = make([]uint32, v.MasksLen)
	for i := 0; i < int(v.MasksLen); i++ {
		v.Masks[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return v
}

// Write request to wire for AllocColorCells
// allocColorCellsRequest writes a AllocColorCells request to a byte slice.
func allocColorCellsRequest(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Planes uint16) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 86 // request opcode
	b += 1

	if Contiguous {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put16(buf[b:], Colors)
	b += 2

	xgb.Put16(buf[b:], Planes)
	b += 2

	return buf
}

// AllocColorPlanesCookie is a cookie used only for AllocColorPlanes requests.
type AllocColorPlanesCookie struct {
	*xgb.Cookie
}

// AllocColorPlanes sends a checked request.
// If an error occurs, it will be returned with the reply by calling AllocColorPlanesCookie.Reply()
func AllocColorPlanes(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Reds uint16, Greens uint16, Blues uint16) AllocColorPlanesCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(allocColorPlanesRequest(c, Contiguous, Cmap, Colors, Reds, Greens, Blues), cookie)
	return AllocColorPlanesCookie{cookie}
}

// AllocColorPlanesUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func AllocColorPlanesUnchecked(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Reds uint16, Greens uint16, Blues uint16) AllocColorPlanesCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(allocColorPlanesRequest(c, Contiguous, Cmap, Colors, Reds, Greens, Blues), cookie)
	return AllocColorPlanesCookie{cookie}
}

// AllocColorPlanesReply represents the data returned from a AllocColorPlanes request.
type AllocColorPlanesReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	PixelsLen uint16
	// padding: 2 bytes
	RedMask   uint32
	GreenMask uint32
	BlueMask  uint32
	// padding: 8 bytes
	Pixels []uint32 // size: xgb.Pad((int(PixelsLen) * 4))
}

// Reply blocks and returns the reply data for a AllocColorPlanes request.
func (cook AllocColorPlanesCookie) Reply() (*AllocColorPlanesReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return allocColorPlanesReply(buf), nil
}

// allocColorPlanesReply reads a byte slice into a AllocColorPlanesReply value.
func allocColorPlanesReply(buf []byte) *AllocColorPlanesReply {
	v := new(AllocColorPlanesReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.PixelsLen = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	v.RedMask = xgb.Get32(buf[b:])
	b += 4

	v.GreenMask = xgb.Get32(buf[b:])
	b += 4

	v.BlueMask = xgb.Get32(buf[b:])
	b += 4

	b += 8 // padding

	v.Pixels = make([]uint32, v.PixelsLen)
	for i := 0; i < int(v.PixelsLen); i++ {
		v.Pixels[i] = xgb.Get32(buf[b:])
		b += 4
	}

	return v
}

// Write request to wire for AllocColorPlanes
// allocColorPlanesRequest writes a AllocColorPlanes request to a byte slice.
func allocColorPlanesRequest(c *xgb.Conn, Contiguous bool, Cmap Colormap, Colors uint16, Reds uint16, Greens uint16, Blues uint16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 87 // request opcode
	b += 1

	if Contiguous {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put16(buf[b:], Colors)
	b += 2

	xgb.Put16(buf[b:], Reds)
	b += 2

	xgb.Put16(buf[b:], Greens)
	b += 2

	xgb.Put16(buf[b:], Blues)
	b += 2

	return buf
}

// AllocNamedColorCookie is a cookie used only for AllocNamedColor requests.
type AllocNamedColorCookie struct {
	*xgb.Cookie
}

// AllocNamedColor sends a checked request.
// If an error occurs, it will be returned with the reply by calling AllocNamedColorCookie.Reply()
func AllocNamedColor(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) AllocNamedColorCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(allocNamedColorRequest(c, Cmap, NameLen, Name), cookie)
	return AllocNamedColorCookie{cookie}
}

// AllocNamedColorUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func AllocNamedColorUnchecked(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) AllocNamedColorCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(allocNamedColorRequest(c, Cmap, NameLen, Name), cookie)
	return AllocNamedColorCookie{cookie}
}

// AllocNamedColorReply represents the data returned from a AllocNamedColor request.
type AllocNamedColorReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Pixel       uint32
	ExactRed    uint16
	ExactGreen  uint16
	ExactBlue   uint16
	VisualRed   uint16
	VisualGreen uint16
	VisualBlue  uint16
}

// Reply blocks and returns the reply data for a AllocNamedColor request.
func (cook AllocNamedColorCookie) Reply() (*AllocNamedColorReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return allocNamedColorReply(buf), nil
}

// allocNamedColorReply reads a byte slice into a AllocNamedColorReply value.
func allocNamedColorReply(buf []byte) *AllocNamedColorReply {
	v := new(AllocNamedColorReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Pixel = xgb.Get32(buf[b:])
	b += 4

	v.ExactRed = xgb.Get16(buf[b:])
	b += 2

	v.ExactGreen = xgb.Get16(buf[b:])
	b += 2

	v.ExactBlue = xgb.Get16(buf[b:])
	b += 2

	v.VisualRed = xgb.Get16(buf[b:])
	b += 2

	v.VisualGreen = xgb.Get16(buf[b:])
	b += 2

	v.VisualBlue = xgb.Get16(buf[b:])
	b += 2

	return v
}

// Write request to wire for AllocNamedColor
// allocNamedColorRequest writes a AllocNamedColor request to a byte slice.
func allocNamedColorRequest(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) []byte {
	size := xgb.Pad((12 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 85 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// AllowEventsCookie is a cookie used only for AllowEvents requests.
type AllowEventsCookie struct {
	*xgb.Cookie
}

// AllowEvents sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func AllowEvents(c *xgb.Conn, Mode byte, Time Timestamp) AllowEventsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(allowEventsRequest(c, Mode, Time), cookie)
	return AllowEventsCookie{cookie}
}

// AllowEventsChecked sends a checked request.
// If an error occurs, it can be retrieved using AllowEventsCookie.Check()
func AllowEventsChecked(c *xgb.Conn, Mode byte, Time Timestamp) AllowEventsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(allowEventsRequest(c, Mode, Time), cookie)
	return AllowEventsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook AllowEventsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for AllowEvents
// allowEventsRequest writes a AllowEvents request to a byte slice.
func allowEventsRequest(c *xgb.Conn, Mode byte, Time Timestamp) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 35 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// BellCookie is a cookie used only for Bell requests.
type BellCookie struct {
	*xgb.Cookie
}

// Bell sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func Bell(c *xgb.Conn, Percent int8) BellCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(bellRequest(c, Percent), cookie)
	return BellCookie{cookie}
}

// BellChecked sends a checked request.
// If an error occurs, it can be retrieved using BellCookie.Check()
func BellChecked(c *xgb.Conn, Percent int8) BellCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(bellRequest(c, Percent), cookie)
	return BellCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook BellCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for Bell
// bellRequest writes a Bell request to a byte slice.
func bellRequest(c *xgb.Conn, Percent int8) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 104 // request opcode
	b += 1

	buf[b] = byte(Percent)
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// ChangeActivePointerGrabCookie is a cookie used only for ChangeActivePointerGrab requests.
type ChangeActivePointerGrabCookie struct {
	*xgb.Cookie
}

// ChangeActivePointerGrab sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeActivePointerGrab(c *xgb.Conn, Cursor Cursor, Time Timestamp, EventMask uint16) ChangeActivePointerGrabCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeActivePointerGrabRequest(c, Cursor, Time, EventMask), cookie)
	return ChangeActivePointerGrabCookie{cookie}
}

// ChangeActivePointerGrabChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeActivePointerGrabCookie.Check()
func ChangeActivePointerGrabChecked(c *xgb.Conn, Cursor Cursor, Time Timestamp, EventMask uint16) ChangeActivePointerGrabCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeActivePointerGrabRequest(c, Cursor, Time, EventMask), cookie)
	return ChangeActivePointerGrabCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeActivePointerGrabCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeActivePointerGrab
// changeActivePointerGrabRequest writes a ChangeActivePointerGrab request to a byte slice.
func changeActivePointerGrabRequest(c *xgb.Conn, Cursor Cursor, Time Timestamp, EventMask uint16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 30 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cursor))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	xgb.Put16(buf[b:], EventMask)
	b += 2

	b += 2 // padding

	return buf
}

// ChangeGCCookie is a cookie used only for ChangeGC requests.
type ChangeGCCookie struct {
	*xgb.Cookie
}

// ChangeGC sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeGC(c *xgb.Conn, Gc Gcontext, ValueMask uint32, ValueList []uint32) ChangeGCCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeGCRequest(c, Gc, ValueMask, ValueList), cookie)
	return ChangeGCCookie{cookie}
}

// ChangeGCChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeGCCookie.Check()
func ChangeGCChecked(c *xgb.Conn, Gc Gcontext, ValueMask uint32, ValueList []uint32) ChangeGCCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeGCRequest(c, Gc, ValueMask, ValueList), cookie)
	return ChangeGCCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeGCCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeGC
// changeGCRequest writes a ChangeGC request to a byte slice.
func changeGCRequest(c *xgb.Conn, Gc Gcontext, ValueMask uint32, ValueList []uint32) []byte {
	size := xgb.Pad((12 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 56 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// ChangeHostsCookie is a cookie used only for ChangeHosts requests.
type ChangeHostsCookie struct {
	*xgb.Cookie
}

// ChangeHosts sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeHosts(c *xgb.Conn, Mode byte, Family byte, AddressLen uint16, Address []byte) ChangeHostsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeHostsRequest(c, Mode, Family, AddressLen, Address), cookie)
	return ChangeHostsCookie{cookie}
}

// ChangeHostsChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeHostsCookie.Check()
func ChangeHostsChecked(c *xgb.Conn, Mode byte, Family byte, AddressLen uint16, Address []byte) ChangeHostsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeHostsRequest(c, Mode, Family, AddressLen, Address), cookie)
	return ChangeHostsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeHostsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeHosts
// changeHostsRequest writes a ChangeHosts request to a byte slice.
func changeHostsRequest(c *xgb.Conn, Mode byte, Family byte, AddressLen uint16, Address []byte) []byte {
	size := xgb.Pad((8 + xgb.Pad((int(AddressLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 109 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	buf[b] = Family
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], AddressLen)
	b += 2

	copy(buf[b:], Address[:AddressLen])
	b += int(AddressLen)

	return buf
}

// ChangeKeyboardControlCookie is a cookie used only for ChangeKeyboardControl requests.
type ChangeKeyboardControlCookie struct {
	*xgb.Cookie
}

// ChangeKeyboardControl sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeKeyboardControl(c *xgb.Conn, ValueMask uint32, ValueList []uint32) ChangeKeyboardControlCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeKeyboardControlRequest(c, ValueMask, ValueList), cookie)
	return ChangeKeyboardControlCookie{cookie}
}

// ChangeKeyboardControlChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeKeyboardControlCookie.Check()
func ChangeKeyboardControlChecked(c *xgb.Conn, ValueMask uint32, ValueList []uint32) ChangeKeyboardControlCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeKeyboardControlRequest(c, ValueMask, ValueList), cookie)
	return ChangeKeyboardControlCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeKeyboardControlCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeKeyboardControl
// changeKeyboardControlRequest writes a ChangeKeyboardControl request to a byte slice.
func changeKeyboardControlRequest(c *xgb.Conn, ValueMask uint32, ValueList []uint32) []byte {
	size := xgb.Pad((8 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 102 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// ChangeKeyboardMappingCookie is a cookie used only for ChangeKeyboardMapping requests.
type ChangeKeyboardMappingCookie struct {
	*xgb.Cookie
}

// ChangeKeyboardMapping sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeKeyboardMapping(c *xgb.Conn, KeycodeCount byte, FirstKeycode Keycode, KeysymsPerKeycode byte, Keysyms []Keysym) ChangeKeyboardMappingCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeKeyboardMappingRequest(c, KeycodeCount, FirstKeycode, KeysymsPerKeycode, Keysyms), cookie)
	return ChangeKeyboardMappingCookie{cookie}
}

// ChangeKeyboardMappingChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeKeyboardMappingCookie.Check()
func ChangeKeyboardMappingChecked(c *xgb.Conn, KeycodeCount byte, FirstKeycode Keycode, KeysymsPerKeycode byte, Keysyms []Keysym) ChangeKeyboardMappingCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeKeyboardMappingRequest(c, KeycodeCount, FirstKeycode, KeysymsPerKeycode, Keysyms), cookie)
	return ChangeKeyboardMappingCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeKeyboardMappingCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeKeyboardMapping
// changeKeyboardMappingRequest writes a ChangeKeyboardMapping request to a byte slice.
func changeKeyboardMappingRequest(c *xgb.Conn, KeycodeCount byte, FirstKeycode Keycode, KeysymsPerKeycode byte, Keysyms []Keysym) []byte {
	size := xgb.Pad((8 + xgb.Pad(((int(KeycodeCount) * int(KeysymsPerKeycode)) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 100 // request opcode
	b += 1

	buf[b] = KeycodeCount
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	buf[b] = byte(FirstKeycode)
	b += 1

	buf[b] = KeysymsPerKeycode
	b += 1

	b += 2 // padding

	for i := 0; i < int((int(KeycodeCount) * int(KeysymsPerKeycode))); i++ {
		xgb.Put32(buf[b:], uint32(Keysyms[i]))
		b += 4
	}

	return buf
}

// ChangePointerControlCookie is a cookie used only for ChangePointerControl requests.
type ChangePointerControlCookie struct {
	*xgb.Cookie
}

// ChangePointerControl sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangePointerControl(c *xgb.Conn, AccelerationNumerator int16, AccelerationDenominator int16, Threshold int16, DoAcceleration bool, DoThreshold bool) ChangePointerControlCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changePointerControlRequest(c, AccelerationNumerator, AccelerationDenominator, Threshold, DoAcceleration, DoThreshold), cookie)
	return ChangePointerControlCookie{cookie}
}

// ChangePointerControlChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangePointerControlCookie.Check()
func ChangePointerControlChecked(c *xgb.Conn, AccelerationNumerator int16, AccelerationDenominator int16, Threshold int16, DoAcceleration bool, DoThreshold bool) ChangePointerControlCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changePointerControlRequest(c, AccelerationNumerator, AccelerationDenominator, Threshold, DoAcceleration, DoThreshold), cookie)
	return ChangePointerControlCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangePointerControlCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangePointerControl
// changePointerControlRequest writes a ChangePointerControl request to a byte slice.
func changePointerControlRequest(c *xgb.Conn, AccelerationNumerator int16, AccelerationDenominator int16, Threshold int16, DoAcceleration bool, DoThreshold bool) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 105 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], uint16(AccelerationNumerator))
	b += 2

	xgb.Put16(buf[b:], uint16(AccelerationDenominator))
	b += 2

	xgb.Put16(buf[b:], uint16(Threshold))
	b += 2

	if DoAcceleration {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	if DoThreshold {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	return buf
}

// ChangePropertyCookie is a cookie used only for ChangeProperty requests.
type ChangePropertyCookie struct {
	*xgb.Cookie
}

// ChangeProperty sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeProperty(c *xgb.Conn, Mode byte, Window Window, Property Atom, Type Atom, Format byte, DataLen uint32, Data []byte) ChangePropertyCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changePropertyRequest(c, Mode, Window, Property, Type, Format, DataLen, Data), cookie)
	return ChangePropertyCookie{cookie}
}

// ChangePropertyChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangePropertyCookie.Check()
func ChangePropertyChecked(c *xgb.Conn, Mode byte, Window Window, Property Atom, Type Atom, Format byte, DataLen uint32, Data []byte) ChangePropertyCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changePropertyRequest(c, Mode, Window, Property, Type, Format, DataLen, Data), cookie)
	return ChangePropertyCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangePropertyCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeProperty
// changePropertyRequest writes a ChangeProperty request to a byte slice.
func changePropertyRequest(c *xgb.Conn, Mode byte, Window Window, Property Atom, Type Atom, Format byte, DataLen uint32, Data []byte) []byte {
	size := xgb.Pad((24 + xgb.Pad((((int(DataLen) * int(Format)) / 8) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 18 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Property))
	b += 4

	xgb.Put32(buf[b:], uint32(Type))
	b += 4

	buf[b] = Format
	b += 1

	b += 3 // padding

	xgb.Put32(buf[b:], DataLen)
	b += 4

	copy(buf[b:], Data[:((int(DataLen)*int(Format))/8)])
	b += int(((int(DataLen) * int(Format)) / 8))

	return buf
}

// ChangeSaveSetCookie is a cookie used only for ChangeSaveSet requests.
type ChangeSaveSetCookie struct {
	*xgb.Cookie
}

// ChangeSaveSet sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeSaveSet(c *xgb.Conn, Mode byte, Window Window) ChangeSaveSetCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeSaveSetRequest(c, Mode, Window), cookie)
	return ChangeSaveSetCookie{cookie}
}

// ChangeSaveSetChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeSaveSetCookie.Check()
func ChangeSaveSetChecked(c *xgb.Conn, Mode byte, Window Window) ChangeSaveSetCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeSaveSetRequest(c, Mode, Window), cookie)
	return ChangeSaveSetCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeSaveSetCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeSaveSet
// changeSaveSetRequest writes a ChangeSaveSet request to a byte slice.
func changeSaveSetRequest(c *xgb.Conn, Mode byte, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 6 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// ChangeWindowAttributesCookie is a cookie used only for ChangeWindowAttributes requests.
type ChangeWindowAttributesCookie struct {
	*xgb.Cookie
}

// ChangeWindowAttributes sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ChangeWindowAttributes(c *xgb.Conn, Window Window, ValueMask uint32, ValueList []uint32) ChangeWindowAttributesCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(changeWindowAttributesRequest(c, Window, ValueMask, ValueList), cookie)
	return ChangeWindowAttributesCookie{cookie}
}

// ChangeWindowAttributesChecked sends a checked request.
// If an error occurs, it can be retrieved using ChangeWindowAttributesCookie.Check()
func ChangeWindowAttributesChecked(c *xgb.Conn, Window Window, ValueMask uint32, ValueList []uint32) ChangeWindowAttributesCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(changeWindowAttributesRequest(c, Window, ValueMask, ValueList), cookie)
	return ChangeWindowAttributesCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ChangeWindowAttributesCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ChangeWindowAttributes
// changeWindowAttributesRequest writes a ChangeWindowAttributes request to a byte slice.
func changeWindowAttributesRequest(c *xgb.Conn, Window Window, ValueMask uint32, ValueList []uint32) []byte {
	size := xgb.Pad((12 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 2 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// CirculateWindowCookie is a cookie used only for CirculateWindow requests.
type CirculateWindowCookie struct {
	*xgb.Cookie
}

// CirculateWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CirculateWindow(c *xgb.Conn, Direction byte, Window Window) CirculateWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(circulateWindowRequest(c, Direction, Window), cookie)
	return CirculateWindowCookie{cookie}
}

// CirculateWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using CirculateWindowCookie.Check()
func CirculateWindowChecked(c *xgb.Conn, Direction byte, Window Window) CirculateWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(circulateWindowRequest(c, Direction, Window), cookie)
	return CirculateWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CirculateWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CirculateWindow
// circulateWindowRequest writes a CirculateWindow request to a byte slice.
func circulateWindowRequest(c *xgb.Conn, Direction byte, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 13 // request opcode
	b += 1

	buf[b] = Direction
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// ClearAreaCookie is a cookie used only for ClearArea requests.
type ClearAreaCookie struct {
	*xgb.Cookie
}

// ClearArea sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ClearArea(c *xgb.Conn, Exposures bool, Window Window, X int16, Y int16, Width uint16, Height uint16) ClearAreaCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(clearAreaRequest(c, Exposures, Window, X, Y, Width, Height), cookie)
	return ClearAreaCookie{cookie}
}

// ClearAreaChecked sends a checked request.
// If an error occurs, it can be retrieved using ClearAreaCookie.Check()
func ClearAreaChecked(c *xgb.Conn, Exposures bool, Window Window, X int16, Y int16, Width uint16, Height uint16) ClearAreaCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(clearAreaRequest(c, Exposures, Window, X, Y, Width, Height), cookie)
	return ClearAreaCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ClearAreaCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ClearArea
// clearAreaRequest writes a ClearArea request to a byte slice.
func clearAreaRequest(c *xgb.Conn, Exposures bool, Window Window, X int16, Y int16, Width uint16, Height uint16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 61 // request opcode
	b += 1

	if Exposures {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	return buf
}

// CloseFontCookie is a cookie used only for CloseFont requests.
type CloseFontCookie struct {
	*xgb.Cookie
}

// CloseFont sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CloseFont(c *xgb.Conn, Font Font) CloseFontCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(closeFontRequest(c, Font), cookie)
	return CloseFontCookie{cookie}
}

// CloseFontChecked sends a checked request.
// If an error occurs, it can be retrieved using CloseFontCookie.Check()
func CloseFontChecked(c *xgb.Conn, Font Font) CloseFontCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(closeFontRequest(c, Font), cookie)
	return CloseFontCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CloseFontCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CloseFont
// closeFontRequest writes a CloseFont request to a byte slice.
func closeFontRequest(c *xgb.Conn, Font Font) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 46 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Font))
	b += 4

	return buf
}

// ConfigureWindowCookie is a cookie used only for ConfigureWindow requests.
type ConfigureWindowCookie struct {
	*xgb.Cookie
}

// ConfigureWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ConfigureWindow(c *xgb.Conn, Window Window, ValueMask uint16, ValueList []uint32) ConfigureWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(configureWindowRequest(c, Window, ValueMask, ValueList), cookie)
	return ConfigureWindowCookie{cookie}
}

// ConfigureWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using ConfigureWindowCookie.Check()
func ConfigureWindowChecked(c *xgb.Conn, Window Window, ValueMask uint16, ValueList []uint32) ConfigureWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(configureWindowRequest(c, Window, ValueMask, ValueList), cookie)
	return ConfigureWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ConfigureWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ConfigureWindow
// configureWindowRequest writes a ConfigureWindow request to a byte slice.
func configureWindowRequest(c *xgb.Conn, Window Window, ValueMask uint16, ValueList []uint32) []byte {
	size := xgb.Pad((12 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 12 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put16(buf[b:], ValueMask)
	b += 2

	b += 2 // padding

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// ConvertSelectionCookie is a cookie used only for ConvertSelection requests.
type ConvertSelectionCookie struct {
	*xgb.Cookie
}

// ConvertSelection sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ConvertSelection(c *xgb.Conn, Requestor Window, Selection Atom, Target Atom, Property Atom, Time Timestamp) ConvertSelectionCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(convertSelectionRequest(c, Requestor, Selection, Target, Property, Time), cookie)
	return ConvertSelectionCookie{cookie}
}

// ConvertSelectionChecked sends a checked request.
// If an error occurs, it can be retrieved using ConvertSelectionCookie.Check()
func ConvertSelectionChecked(c *xgb.Conn, Requestor Window, Selection Atom, Target Atom, Property Atom, Time Timestamp) ConvertSelectionCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(convertSelectionRequest(c, Requestor, Selection, Target, Property, Time), cookie)
	return ConvertSelectionCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ConvertSelectionCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ConvertSelection
// convertSelectionRequest writes a ConvertSelection request to a byte slice.
func convertSelectionRequest(c *xgb.Conn, Requestor Window, Selection Atom, Target Atom, Property Atom, Time Timestamp) []byte {
	size := 24
	b := 0
	buf := make([]byte, size)

	buf[b] = 24 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Requestor))
	b += 4

	xgb.Put32(buf[b:], uint32(Selection))
	b += 4

	xgb.Put32(buf[b:], uint32(Target))
	b += 4

	xgb.Put32(buf[b:], uint32(Property))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// CopyAreaCookie is a cookie used only for CopyArea requests.
type CopyAreaCookie struct {
	*xgb.Cookie
}

// CopyArea sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CopyArea(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16) CopyAreaCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(copyAreaRequest(c, SrcDrawable, DstDrawable, Gc, SrcX, SrcY, DstX, DstY, Width, Height), cookie)
	return CopyAreaCookie{cookie}
}

// CopyAreaChecked sends a checked request.
// If an error occurs, it can be retrieved using CopyAreaCookie.Check()
func CopyAreaChecked(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16) CopyAreaCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(copyAreaRequest(c, SrcDrawable, DstDrawable, Gc, SrcX, SrcY, DstX, DstY, Width, Height), cookie)
	return CopyAreaCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CopyAreaCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CopyArea
// copyAreaRequest writes a CopyArea request to a byte slice.
func copyAreaRequest(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16) []byte {
	size := 28
	b := 0
	buf := make([]byte, size)

	buf[b] = 62 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(SrcDrawable))
	b += 4

	xgb.Put32(buf[b:], uint32(DstDrawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(SrcX))
	b += 2

	xgb.Put16(buf[b:], uint16(SrcY))
	b += 2

	xgb.Put16(buf[b:], uint16(DstX))
	b += 2

	xgb.Put16(buf[b:], uint16(DstY))
	b += 2

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	return buf
}

// CopyColormapAndFreeCookie is a cookie used only for CopyColormapAndFree requests.
type CopyColormapAndFreeCookie struct {
	*xgb.Cookie
}

// CopyColormapAndFree sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CopyColormapAndFree(c *xgb.Conn, Mid Colormap, SrcCmap Colormap) CopyColormapAndFreeCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(copyColormapAndFreeRequest(c, Mid, SrcCmap), cookie)
	return CopyColormapAndFreeCookie{cookie}
}

// CopyColormapAndFreeChecked sends a checked request.
// If an error occurs, it can be retrieved using CopyColormapAndFreeCookie.Check()
func CopyColormapAndFreeChecked(c *xgb.Conn, Mid Colormap, SrcCmap Colormap) CopyColormapAndFreeCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(copyColormapAndFreeRequest(c, Mid, SrcCmap), cookie)
	return CopyColormapAndFreeCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CopyColormapAndFreeCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CopyColormapAndFree
// copyColormapAndFreeRequest writes a CopyColormapAndFree request to a byte slice.
func copyColormapAndFreeRequest(c *xgb.Conn, Mid Colormap, SrcCmap Colormap) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 80 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Mid))
	b += 4

	xgb.Put32(buf[b:], uint32(SrcCmap))
	b += 4

	return buf
}

// CopyGCCookie is a cookie used only for CopyGC requests.
type CopyGCCookie struct {
	*xgb.Cookie
}

// CopyGC sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CopyGC(c *xgb.Conn, SrcGc Gcontext, DstGc Gcontext, ValueMask uint32) CopyGCCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(copyGCRequest(c, SrcGc, DstGc, ValueMask), cookie)
	return CopyGCCookie{cookie}
}

// CopyGCChecked sends a checked request.
// If an error occurs, it can be retrieved using CopyGCCookie.Check()
func CopyGCChecked(c *xgb.Conn, SrcGc Gcontext, DstGc Gcontext, ValueMask uint32) CopyGCCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(copyGCRequest(c, SrcGc, DstGc, ValueMask), cookie)
	return CopyGCCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CopyGCCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CopyGC
// copyGCRequest writes a CopyGC request to a byte slice.
func copyGCRequest(c *xgb.Conn, SrcGc Gcontext, DstGc Gcontext, ValueMask uint32) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 57 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(SrcGc))
	b += 4

	xgb.Put32(buf[b:], uint32(DstGc))
	b += 4

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	return buf
}

// CopyPlaneCookie is a cookie used only for CopyPlane requests.
type CopyPlaneCookie struct {
	*xgb.Cookie
}

// CopyPlane sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CopyPlane(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16, BitPlane uint32) CopyPlaneCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(copyPlaneRequest(c, SrcDrawable, DstDrawable, Gc, SrcX, SrcY, DstX, DstY, Width, Height, BitPlane), cookie)
	return CopyPlaneCookie{cookie}
}

// CopyPlaneChecked sends a checked request.
// If an error occurs, it can be retrieved using CopyPlaneCookie.Check()
func CopyPlaneChecked(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16, BitPlane uint32) CopyPlaneCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(copyPlaneRequest(c, SrcDrawable, DstDrawable, Gc, SrcX, SrcY, DstX, DstY, Width, Height, BitPlane), cookie)
	return CopyPlaneCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CopyPlaneCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CopyPlane
// copyPlaneRequest writes a CopyPlane request to a byte slice.
func copyPlaneRequest(c *xgb.Conn, SrcDrawable Drawable, DstDrawable Drawable, Gc Gcontext, SrcX int16, SrcY int16, DstX int16, DstY int16, Width uint16, Height uint16, BitPlane uint32) []byte {
	size := 32
	b := 0
	buf := make([]byte, size)

	buf[b] = 63 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(SrcDrawable))
	b += 4

	xgb.Put32(buf[b:], uint32(DstDrawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(SrcX))
	b += 2

	xgb.Put16(buf[b:], uint16(SrcY))
	b += 2

	xgb.Put16(buf[b:], uint16(DstX))
	b += 2

	xgb.Put16(buf[b:], uint16(DstY))
	b += 2

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	xgb.Put32(buf[b:], BitPlane)
	b += 4

	return buf
}

// CreateColormapCookie is a cookie used only for CreateColormap requests.
type CreateColormapCookie struct {
	*xgb.Cookie
}

// CreateColormap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreateColormap(c *xgb.Conn, Alloc byte, Mid Colormap, Window Window, Visual Visualid) CreateColormapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createColormapRequest(c, Alloc, Mid, Window, Visual), cookie)
	return CreateColormapCookie{cookie}
}

// CreateColormapChecked sends a checked request.
// If an error occurs, it can be retrieved using CreateColormapCookie.Check()
func CreateColormapChecked(c *xgb.Conn, Alloc byte, Mid Colormap, Window Window, Visual Visualid) CreateColormapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createColormapRequest(c, Alloc, Mid, Window, Visual), cookie)
	return CreateColormapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreateColormapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreateColormap
// createColormapRequest writes a CreateColormap request to a byte slice.
func createColormapRequest(c *xgb.Conn, Alloc byte, Mid Colormap, Window Window, Visual Visualid) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 78 // request opcode
	b += 1

	buf[b] = Alloc
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Mid))
	b += 4

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Visual))
	b += 4

	return buf
}

// CreateCursorCookie is a cookie used only for CreateCursor requests.
type CreateCursorCookie struct {
	*xgb.Cookie
}

// CreateCursor sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreateCursor(c *xgb.Conn, Cid Cursor, Source Pixmap, Mask Pixmap, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16, X uint16, Y uint16) CreateCursorCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createCursorRequest(c, Cid, Source, Mask, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue, X, Y), cookie)
	return CreateCursorCookie{cookie}
}

// CreateCursorChecked sends a checked request.
// If an error occurs, it can be retrieved using CreateCursorCookie.Check()
func CreateCursorChecked(c *xgb.Conn, Cid Cursor, Source Pixmap, Mask Pixmap, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16, X uint16, Y uint16) CreateCursorCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createCursorRequest(c, Cid, Source, Mask, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue, X, Y), cookie)
	return CreateCursorCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreateCursorCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreateCursor
// createCursorRequest writes a CreateCursor request to a byte slice.
func createCursorRequest(c *xgb.Conn, Cid Cursor, Source Pixmap, Mask Pixmap, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16, X uint16, Y uint16) []byte {
	size := 32
	b := 0
	buf := make([]byte, size)

	buf[b] = 93 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cid))
	b += 4

	xgb.Put32(buf[b:], uint32(Source))
	b += 4

	xgb.Put32(buf[b:], uint32(Mask))
	b += 4

	xgb.Put16(buf[b:], ForeRed)
	b += 2

	xgb.Put16(buf[b:], ForeGreen)
	b += 2

	xgb.Put16(buf[b:], ForeBlue)
	b += 2

	xgb.Put16(buf[b:], BackRed)
	b += 2

	xgb.Put16(buf[b:], BackGreen)
	b += 2

	xgb.Put16(buf[b:], BackBlue)
	b += 2

	xgb.Put16(buf[b:], X)
	b += 2

	xgb.Put16(buf[b:], Y)
	b += 2

	return buf
}

// CreateGCCookie is a cookie used only for CreateGC requests.
type CreateGCCookie struct {
	*xgb.Cookie
}

// CreateGC sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreateGC(c *xgb.Conn, Cid Gcontext, Drawable Drawable, ValueMask uint32, ValueList []uint32) CreateGCCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createGCRequest(c, Cid, Drawable, ValueMask, ValueList), cookie)
	return CreateGCCookie{cookie}
}

// CreateGCChecked sends a checked request.
// If an error occurs, it can be retrieved using CreateGCCookie.Check()
func CreateGCChecked(c *xgb.Conn, Cid Gcontext, Drawable Drawable, ValueMask uint32, ValueList []uint32) CreateGCCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createGCRequest(c, Cid, Drawable, ValueMask, ValueList), cookie)
	return CreateGCCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreateGCCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreateGC
// createGCRequest writes a CreateGC request to a byte slice.
func createGCRequest(c *xgb.Conn, Cid Gcontext, Drawable Drawable, ValueMask uint32, ValueList []uint32) []byte {
	size := xgb.Pad((16 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 55 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cid))
	b += 4

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// CreateGlyphCursorCookie is a cookie used only for CreateGlyphCursor requests.
type CreateGlyphCursorCookie struct {
	*xgb.Cookie
}

// CreateGlyphCursor sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreateGlyphCursor(c *xgb.Conn, Cid Cursor, SourceFont Font, MaskFont Font, SourceChar uint16, MaskChar uint16, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) CreateGlyphCursorCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createGlyphCursorRequest(c, Cid, SourceFont, MaskFont, SourceChar, MaskChar, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue), cookie)
	return CreateGlyphCursorCookie{cookie}
}

// CreateGlyphCursorChecked sends a checked request.
// If an error occurs, it can be retrieved using CreateGlyphCursorCookie.Check()
func CreateGlyphCursorChecked(c *xgb.Conn, Cid Cursor, SourceFont Font, MaskFont Font, SourceChar uint16, MaskChar uint16, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) CreateGlyphCursorCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createGlyphCursorRequest(c, Cid, SourceFont, MaskFont, SourceChar, MaskChar, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue), cookie)
	return CreateGlyphCursorCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreateGlyphCursorCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreateGlyphCursor
// createGlyphCursorRequest writes a CreateGlyphCursor request to a byte slice.
func createGlyphCursorRequest(c *xgb.Conn, Cid Cursor, SourceFont Font, MaskFont Font, SourceChar uint16, MaskChar uint16, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) []byte {
	size := 32
	b := 0
	buf := make([]byte, size)

	buf[b] = 94 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cid))
	b += 4

	xgb.Put32(buf[b:], uint32(SourceFont))
	b += 4

	xgb.Put32(buf[b:], uint32(MaskFont))
	b += 4

	xgb.Put16(buf[b:], SourceChar)
	b += 2

	xgb.Put16(buf[b:], MaskChar)
	b += 2

	xgb.Put16(buf[b:], ForeRed)
	b += 2

	xgb.Put16(buf[b:], ForeGreen)
	b += 2

	xgb.Put16(buf[b:], ForeBlue)
	b += 2

	xgb.Put16(buf[b:], BackRed)
	b += 2

	xgb.Put16(buf[b:], BackGreen)
	b += 2

	xgb.Put16(buf[b:], BackBlue)
	b += 2

	return buf
}

// CreatePixmapCookie is a cookie used only for CreatePixmap requests.
type CreatePixmapCookie struct {
	*xgb.Cookie
}

// CreatePixmap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreatePixmap(c *xgb.Conn, Depth byte, Pid Pixmap, Drawable Drawable, Width uint16, Height uint16) CreatePixmapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createPixmapRequest(c, Depth, Pid, Drawable, Width, Height), cookie)
	return CreatePixmapCookie{cookie}
}

// CreatePixmapChecked sends a checked request.
// If an error occurs, it can be retrieved using CreatePixmapCookie.Check()
func CreatePixmapChecked(c *xgb.Conn, Depth byte, Pid Pixmap, Drawable Drawable, Width uint16, Height uint16) CreatePixmapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createPixmapRequest(c, Depth, Pid, Drawable, Width, Height), cookie)
	return CreatePixmapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreatePixmapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreatePixmap
// createPixmapRequest writes a CreatePixmap request to a byte slice.
func createPixmapRequest(c *xgb.Conn, Depth byte, Pid Pixmap, Drawable Drawable, Width uint16, Height uint16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 53 // request opcode
	b += 1

	buf[b] = Depth
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Pid))
	b += 4

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	return buf
}

// CreateWindowCookie is a cookie used only for CreateWindow requests.
type CreateWindowCookie struct {
	*xgb.Cookie
}

// CreateWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func CreateWindow(c *xgb.Conn, Depth byte, Wid Window, Parent Window, X int16, Y int16, Width uint16, Height uint16, BorderWidth uint16, Class uint16, Visual Visualid, ValueMask uint32, ValueList []uint32) CreateWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(createWindowRequest(c, Depth, Wid, Parent, X, Y, Width, Height, BorderWidth, Class, Visual, ValueMask, ValueList), cookie)
	return CreateWindowCookie{cookie}
}

// CreateWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using CreateWindowCookie.Check()
func CreateWindowChecked(c *xgb.Conn, Depth byte, Wid Window, Parent Window, X int16, Y int16, Width uint16, Height uint16, BorderWidth uint16, Class uint16, Visual Visualid, ValueMask uint32, ValueList []uint32) CreateWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(createWindowRequest(c, Depth, Wid, Parent, X, Y, Width, Height, BorderWidth, Class, Visual, ValueMask, ValueList), cookie)
	return CreateWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook CreateWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for CreateWindow
// createWindowRequest writes a CreateWindow request to a byte slice.
func createWindowRequest(c *xgb.Conn, Depth byte, Wid Window, Parent Window, X int16, Y int16, Width uint16, Height uint16, BorderWidth uint16, Class uint16, Visual Visualid, ValueMask uint32, ValueList []uint32) []byte {
	size := xgb.Pad((32 + xgb.Pad((4 * xgb.PopCount(int(ValueMask))))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 1 // request opcode
	b += 1

	buf[b] = Depth
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Wid))
	b += 4

	xgb.Put32(buf[b:], uint32(Parent))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	xgb.Put16(buf[b:], BorderWidth)
	b += 2

	xgb.Put16(buf[b:], Class)
	b += 2

	xgb.Put32(buf[b:], uint32(Visual))
	b += 4

	xgb.Put32(buf[b:], ValueMask)
	b += 4

	for i := 0; i < xgb.PopCount(int(ValueMask)); i++ {
		xgb.Put32(buf[b:], ValueList[i])
		b += 4
	}
	b = xgb.Pad(b)

	return buf
}

// DeletePropertyCookie is a cookie used only for DeleteProperty requests.
type DeletePropertyCookie struct {
	*xgb.Cookie
}

// DeleteProperty sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func DeleteProperty(c *xgb.Conn, Window Window, Property Atom) DeletePropertyCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(deletePropertyRequest(c, Window, Property), cookie)
	return DeletePropertyCookie{cookie}
}

// DeletePropertyChecked sends a checked request.
// If an error occurs, it can be retrieved using DeletePropertyCookie.Check()
func DeletePropertyChecked(c *xgb.Conn, Window Window, Property Atom) DeletePropertyCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(deletePropertyRequest(c, Window, Property), cookie)
	return DeletePropertyCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook DeletePropertyCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for DeleteProperty
// deletePropertyRequest writes a DeleteProperty request to a byte slice.
func deletePropertyRequest(c *xgb.Conn, Window Window, Property Atom) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 19 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Property))
	b += 4

	return buf
}

// DestroySubwindowsCookie is a cookie used only for DestroySubwindows requests.
type DestroySubwindowsCookie struct {
	*xgb.Cookie
}

// DestroySubwindows sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func DestroySubwindows(c *xgb.Conn, Window Window) DestroySubwindowsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(destroySubwindowsRequest(c, Window), cookie)
	return DestroySubwindowsCookie{cookie}
}

// DestroySubwindowsChecked sends a checked request.
// If an error occurs, it can be retrieved using DestroySubwindowsCookie.Check()
func DestroySubwindowsChecked(c *xgb.Conn, Window Window) DestroySubwindowsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(destroySubwindowsRequest(c, Window), cookie)
	return DestroySubwindowsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook DestroySubwindowsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for DestroySubwindows
// destroySubwindowsRequest writes a DestroySubwindows request to a byte slice.
func destroySubwindowsRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 5 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// DestroyWindowCookie is a cookie used only for DestroyWindow requests.
type DestroyWindowCookie struct {
	*xgb.Cookie
}

// DestroyWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func DestroyWindow(c *xgb.Conn, Window Window) DestroyWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(destroyWindowRequest(c, Window), cookie)
	return DestroyWindowCookie{cookie}
}

// DestroyWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using DestroyWindowCookie.Check()
func DestroyWindowChecked(c *xgb.Conn, Window Window) DestroyWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(destroyWindowRequest(c, Window), cookie)
	return DestroyWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook DestroyWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for DestroyWindow
// destroyWindowRequest writes a DestroyWindow request to a byte slice.
func destroyWindowRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 4 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// FillPolyCookie is a cookie used only for FillPoly requests.
type FillPolyCookie struct {
	*xgb.Cookie
}

// FillPoly sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FillPoly(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Shape byte, CoordinateMode byte, Points []Point) FillPolyCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(fillPolyRequest(c, Drawable, Gc, Shape, CoordinateMode, Points), cookie)
	return FillPolyCookie{cookie}
}

// FillPolyChecked sends a checked request.
// If an error occurs, it can be retrieved using FillPolyCookie.Check()
func FillPolyChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Shape byte, CoordinateMode byte, Points []Point) FillPolyCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(fillPolyRequest(c, Drawable, Gc, Shape, CoordinateMode, Points), cookie)
	return FillPolyCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FillPolyCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FillPoly
// fillPolyRequest writes a FillPoly request to a byte slice.
func fillPolyRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Shape byte, CoordinateMode byte, Points []Point) []byte {
	size := xgb.Pad((16 + xgb.Pad((len(Points) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 69 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	buf[b] = Shape
	b += 1

	buf[b] = CoordinateMode
	b += 1

	b += 2 // padding

	b += PointListBytes(buf[b:], Points)

	return buf
}

// ForceScreenSaverCookie is a cookie used only for ForceScreenSaver requests.
type ForceScreenSaverCookie struct {
	*xgb.Cookie
}

// ForceScreenSaver sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ForceScreenSaver(c *xgb.Conn, Mode byte) ForceScreenSaverCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(forceScreenSaverRequest(c, Mode), cookie)
	return ForceScreenSaverCookie{cookie}
}

// ForceScreenSaverChecked sends a checked request.
// If an error occurs, it can be retrieved using ForceScreenSaverCookie.Check()
func ForceScreenSaverChecked(c *xgb.Conn, Mode byte) ForceScreenSaverCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(forceScreenSaverRequest(c, Mode), cookie)
	return ForceScreenSaverCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ForceScreenSaverCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ForceScreenSaver
// forceScreenSaverRequest writes a ForceScreenSaver request to a byte slice.
func forceScreenSaverRequest(c *xgb.Conn, Mode byte) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 115 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// FreeColormapCookie is a cookie used only for FreeColormap requests.
type FreeColormapCookie struct {
	*xgb.Cookie
}

// FreeColormap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FreeColormap(c *xgb.Conn, Cmap Colormap) FreeColormapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(freeColormapRequest(c, Cmap), cookie)
	return FreeColormapCookie{cookie}
}

// FreeColormapChecked sends a checked request.
// If an error occurs, it can be retrieved using FreeColormapCookie.Check()
func FreeColormapChecked(c *xgb.Conn, Cmap Colormap) FreeColormapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(freeColormapRequest(c, Cmap), cookie)
	return FreeColormapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FreeColormapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FreeColormap
// freeColormapRequest writes a FreeColormap request to a byte slice.
func freeColormapRequest(c *xgb.Conn, Cmap Colormap) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 79 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	return buf
}

// FreeColorsCookie is a cookie used only for FreeColors requests.
type FreeColorsCookie struct {
	*xgb.Cookie
}

// FreeColors sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FreeColors(c *xgb.Conn, Cmap Colormap, PlaneMask uint32, Pixels []uint32) FreeColorsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(freeColorsRequest(c, Cmap, PlaneMask, Pixels), cookie)
	return FreeColorsCookie{cookie}
}

// FreeColorsChecked sends a checked request.
// If an error occurs, it can be retrieved using FreeColorsCookie.Check()
func FreeColorsChecked(c *xgb.Conn, Cmap Colormap, PlaneMask uint32, Pixels []uint32) FreeColorsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(freeColorsRequest(c, Cmap, PlaneMask, Pixels), cookie)
	return FreeColorsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FreeColorsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FreeColors
// freeColorsRequest writes a FreeColors request to a byte slice.
func freeColorsRequest(c *xgb.Conn, Cmap Colormap, PlaneMask uint32, Pixels []uint32) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Pixels) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 88 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put32(buf[b:], PlaneMask)
	b += 4

	for i := 0; i < int(len(Pixels)); i++ {
		xgb.Put32(buf[b:], Pixels[i])
		b += 4
	}

	return buf
}

// FreeCursorCookie is a cookie used only for FreeCursor requests.
type FreeCursorCookie struct {
	*xgb.Cookie
}

// FreeCursor sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FreeCursor(c *xgb.Conn, Cursor Cursor) FreeCursorCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(freeCursorRequest(c, Cursor), cookie)
	return FreeCursorCookie{cookie}
}

// FreeCursorChecked sends a checked request.
// If an error occurs, it can be retrieved using FreeCursorCookie.Check()
func FreeCursorChecked(c *xgb.Conn, Cursor Cursor) FreeCursorCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(freeCursorRequest(c, Cursor), cookie)
	return FreeCursorCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FreeCursorCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FreeCursor
// freeCursorRequest writes a FreeCursor request to a byte slice.
func freeCursorRequest(c *xgb.Conn, Cursor Cursor) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 95 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cursor))
	b += 4

	return buf
}

// FreeGCCookie is a cookie used only for FreeGC requests.
type FreeGCCookie struct {
	*xgb.Cookie
}

// FreeGC sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FreeGC(c *xgb.Conn, Gc Gcontext) FreeGCCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(freeGCRequest(c, Gc), cookie)
	return FreeGCCookie{cookie}
}

// FreeGCChecked sends a checked request.
// If an error occurs, it can be retrieved using FreeGCCookie.Check()
func FreeGCChecked(c *xgb.Conn, Gc Gcontext) FreeGCCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(freeGCRequest(c, Gc), cookie)
	return FreeGCCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FreeGCCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FreeGC
// freeGCRequest writes a FreeGC request to a byte slice.
func freeGCRequest(c *xgb.Conn, Gc Gcontext) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 60 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	return buf
}

// FreePixmapCookie is a cookie used only for FreePixmap requests.
type FreePixmapCookie struct {
	*xgb.Cookie
}

// FreePixmap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func FreePixmap(c *xgb.Conn, Pixmap Pixmap) FreePixmapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(freePixmapRequest(c, Pixmap), cookie)
	return FreePixmapCookie{cookie}
}

// FreePixmapChecked sends a checked request.
// If an error occurs, it can be retrieved using FreePixmapCookie.Check()
func FreePixmapChecked(c *xgb.Conn, Pixmap Pixmap) FreePixmapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(freePixmapRequest(c, Pixmap), cookie)
	return FreePixmapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook FreePixmapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for FreePixmap
// freePixmapRequest writes a FreePixmap request to a byte slice.
func freePixmapRequest(c *xgb.Conn, Pixmap Pixmap) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 54 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Pixmap))
	b += 4

	return buf
}

// GetAtomNameCookie is a cookie used only for GetAtomName requests.
type GetAtomNameCookie struct {
	*xgb.Cookie
}

// GetAtomName sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetAtomNameCookie.Reply()
func GetAtomName(c *xgb.Conn, Atom Atom) GetAtomNameCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getAtomNameRequest(c, Atom), cookie)
	return GetAtomNameCookie{cookie}
}

// GetAtomNameUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetAtomNameUnchecked(c *xgb.Conn, Atom Atom) GetAtomNameCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getAtomNameRequest(c, Atom), cookie)
	return GetAtomNameCookie{cookie}
}

// GetAtomNameReply represents the data returned from a GetAtomName request.
type GetAtomNameReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	NameLen uint16
	// padding: 22 bytes
	Name string // size: xgb.Pad((int(NameLen) * 1))
}

// Reply blocks and returns the reply data for a GetAtomName request.
func (cook GetAtomNameCookie) Reply() (*GetAtomNameReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getAtomNameReply(buf), nil
}

// getAtomNameReply reads a byte slice into a GetAtomNameReply value.
func getAtomNameReply(buf []byte) *GetAtomNameReply {
	v := new(GetAtomNameReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.NameLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	{
		byteString := make([]byte, v.NameLen)
		copy(byteString[:v.NameLen], buf[b:])
		v.Name = string(byteString)
		b += int(v.NameLen)
	}

	return v
}

// Write request to wire for GetAtomName
// getAtomNameRequest writes a GetAtomName request to a byte slice.
func getAtomNameRequest(c *xgb.Conn, Atom Atom) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 17 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Atom))
	b += 4

	return buf
}

// GetFontPathCookie is a cookie used only for GetFontPath requests.
type GetFontPathCookie struct {
	*xgb.Cookie
}

// GetFontPath sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetFontPathCookie.Reply()
func GetFontPath(c *xgb.Conn) GetFontPathCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getFontPathRequest(c), cookie)
	return GetFontPathCookie{cookie}
}

// GetFontPathUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetFontPathUnchecked(c *xgb.Conn) GetFontPathCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getFontPathRequest(c), cookie)
	return GetFontPathCookie{cookie}
}

// GetFontPathReply represents the data returned from a GetFontPath request.
type GetFontPathReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	PathLen uint16
	// padding: 22 bytes
	Path []Str // size: StrListSize(Path)
}

// Reply blocks and returns the reply data for a GetFontPath request.
func (cook GetFontPathCookie) Reply() (*GetFontPathReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getFontPathReply(buf), nil
}

// getFontPathReply reads a byte slice into a GetFontPathReply value.
func getFontPathReply(buf []byte) *GetFontPathReply {
	v := new(GetFontPathReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.PathLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Path = make([]Str, v.PathLen)
	b += StrReadList(buf[b:], v.Path)

	return v
}

// Write request to wire for GetFontPath
// getFontPathRequest writes a GetFontPath request to a byte slice.
func getFontPathRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 52 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetGeometryCookie is a cookie used only for GetGeometry requests.
type GetGeometryCookie struct {
	*xgb.Cookie
}

// GetGeometry sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetGeometryCookie.Reply()
func GetGeometry(c *xgb.Conn, Drawable Drawable) GetGeometryCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getGeometryRequest(c, Drawable), cookie)
	return GetGeometryCookie{cookie}
}

// GetGeometryUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetGeometryUnchecked(c *xgb.Conn, Drawable Drawable) GetGeometryCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getGeometryRequest(c, Drawable), cookie)
	return GetGeometryCookie{cookie}
}

// GetGeometryReply represents the data returned from a GetGeometry request.
type GetGeometryReply struct {
	Sequence    uint16 // sequence number of the request for this reply
	Length      uint32 // number of bytes in this reply
	Depth       byte
	Root        Window
	X           int16
	Y           int16
	Width       uint16
	Height      uint16
	BorderWidth uint16
	// padding: 2 bytes
}

// Reply blocks and returns the reply data for a GetGeometry request.
func (cook GetGeometryCookie) Reply() (*GetGeometryReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getGeometryReply(buf), nil
}

// getGeometryReply reads a byte slice into a GetGeometryReply value.
func getGeometryReply(buf []byte) *GetGeometryReply {
	v := new(GetGeometryReply)
	b := 1 // skip reply determinant

	v.Depth = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.X = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Y = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	v.BorderWidth = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	return v
}

// Write request to wire for GetGeometry
// getGeometryRequest writes a GetGeometry request to a byte slice.
func getGeometryRequest(c *xgb.Conn, Drawable Drawable) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 14 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	return buf
}

// GetImageCookie is a cookie used only for GetImage requests.
type GetImageCookie struct {
	*xgb.Cookie
}

// GetImage sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetImageCookie.Reply()
func GetImage(c *xgb.Conn, Format byte, Drawable Drawable, X int16, Y int16, Width uint16, Height uint16, PlaneMask uint32) GetImageCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getImageRequest(c, Format, Drawable, X, Y, Width, Height, PlaneMask), cookie)
	return GetImageCookie{cookie}
}

// GetImageUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetImageUnchecked(c *xgb.Conn, Format byte, Drawable Drawable, X int16, Y int16, Width uint16, Height uint16, PlaneMask uint32) GetImageCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getImageRequest(c, Format, Drawable, X, Y, Width, Height, PlaneMask), cookie)
	return GetImageCookie{cookie}
}

// GetImageReply represents the data returned from a GetImage request.
type GetImageReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Depth    byte
	Visual   Visualid
	// padding: 20 bytes
	Data []byte // size: xgb.Pad(((int(Length) * 4) * 1))
}

// Reply blocks and returns the reply data for a GetImage request.
func (cook GetImageCookie) Reply() (*GetImageReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getImageReply(buf), nil
}

// getImageReply reads a byte slice into a GetImageReply value.
func getImageReply(buf []byte) *GetImageReply {
	v := new(GetImageReply)
	b := 1 // skip reply determinant

	v.Depth = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Visual = Visualid(xgb.Get32(buf[b:]))
	b += 4

	b += 20 // padding

	v.Data = make([]byte, (int(v.Length) * 4))
	copy(v.Data[:(int(v.Length)*4)], buf[b:])
	b += int((int(v.Length) * 4))

	return v
}

// Write request to wire for GetImage
// getImageRequest writes a GetImage request to a byte slice.
func getImageRequest(c *xgb.Conn, Format byte, Drawable Drawable, X int16, Y int16, Width uint16, Height uint16, PlaneMask uint32) []byte {
	size := 20
	b := 0
	buf := make([]byte, size)

	buf[b] = 73 // request opcode
	b += 1

	buf[b] = Format
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	xgb.Put32(buf[b:], PlaneMask)
	b += 4

	return buf
}

// GetInputFocusCookie is a cookie used only for GetInputFocus requests.
type GetInputFocusCookie struct {
	*xgb.Cookie
}

// GetInputFocus sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetInputFocusCookie.Reply()
func GetInputFocus(c *xgb.Conn) GetInputFocusCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getInputFocusRequest(c), cookie)
	return GetInputFocusCookie{cookie}
}

// GetInputFocusUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetInputFocusUnchecked(c *xgb.Conn) GetInputFocusCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getInputFocusRequest(c), cookie)
	return GetInputFocusCookie{cookie}
}

// GetInputFocusReply represents the data returned from a GetInputFocus request.
type GetInputFocusReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	RevertTo byte
	Focus    Window
}

// Reply blocks and returns the reply data for a GetInputFocus request.
func (cook GetInputFocusCookie) Reply() (*GetInputFocusReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getInputFocusReply(buf), nil
}

// getInputFocusReply reads a byte slice into a GetInputFocusReply value.
func getInputFocusReply(buf []byte) *GetInputFocusReply {
	v := new(GetInputFocusReply)
	b := 1 // skip reply determinant

	v.RevertTo = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Focus = Window(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Write request to wire for GetInputFocus
// getInputFocusRequest writes a GetInputFocus request to a byte slice.
func getInputFocusRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 43 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetKeyboardControlCookie is a cookie used only for GetKeyboardControl requests.
type GetKeyboardControlCookie struct {
	*xgb.Cookie
}

// GetKeyboardControl sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetKeyboardControlCookie.Reply()
func GetKeyboardControl(c *xgb.Conn) GetKeyboardControlCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getKeyboardControlRequest(c), cookie)
	return GetKeyboardControlCookie{cookie}
}

// GetKeyboardControlUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetKeyboardControlUnchecked(c *xgb.Conn) GetKeyboardControlCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getKeyboardControlRequest(c), cookie)
	return GetKeyboardControlCookie{cookie}
}

// GetKeyboardControlReply represents the data returned from a GetKeyboardControl request.
type GetKeyboardControlReply struct {
	Sequence         uint16 // sequence number of the request for this reply
	Length           uint32 // number of bytes in this reply
	GlobalAutoRepeat byte
	LedMask          uint32
	KeyClickPercent  byte
	BellPercent      byte
	BellPitch        uint16
	BellDuration     uint16
	// padding: 2 bytes
	AutoRepeats []byte // size: 32
}

// Reply blocks and returns the reply data for a GetKeyboardControl request.
func (cook GetKeyboardControlCookie) Reply() (*GetKeyboardControlReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getKeyboardControlReply(buf), nil
}

// getKeyboardControlReply reads a byte slice into a GetKeyboardControlReply value.
func getKeyboardControlReply(buf []byte) *GetKeyboardControlReply {
	v := new(GetKeyboardControlReply)
	b := 1 // skip reply determinant

	v.GlobalAutoRepeat = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.LedMask = xgb.Get32(buf[b:])
	b += 4

	v.KeyClickPercent = buf[b]
	b += 1

	v.BellPercent = buf[b]
	b += 1

	v.BellPitch = xgb.Get16(buf[b:])
	b += 2

	v.BellDuration = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	v.AutoRepeats = make([]byte, 32)
	copy(v.AutoRepeats[:32], buf[b:])
	b += int(32)

	return v
}

// Write request to wire for GetKeyboardControl
// getKeyboardControlRequest writes a GetKeyboardControl request to a byte slice.
func getKeyboardControlRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 103 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetKeyboardMappingCookie is a cookie used only for GetKeyboardMapping requests.
type GetKeyboardMappingCookie struct {
	*xgb.Cookie
}

// GetKeyboardMapping sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetKeyboardMappingCookie.Reply()
func GetKeyboardMapping(c *xgb.Conn, FirstKeycode Keycode, Count byte) GetKeyboardMappingCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getKeyboardMappingRequest(c, FirstKeycode, Count), cookie)
	return GetKeyboardMappingCookie{cookie}
}

// GetKeyboardMappingUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetKeyboardMappingUnchecked(c *xgb.Conn, FirstKeycode Keycode, Count byte) GetKeyboardMappingCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getKeyboardMappingRequest(c, FirstKeycode, Count), cookie)
	return GetKeyboardMappingCookie{cookie}
}

// GetKeyboardMappingReply represents the data returned from a GetKeyboardMapping request.
type GetKeyboardMappingReply struct {
	Sequence          uint16 // sequence number of the request for this reply
	Length            uint32 // number of bytes in this reply
	KeysymsPerKeycode byte
	// padding: 24 bytes
	Keysyms []Keysym // size: xgb.Pad((int(Length) * 4))
}

// Reply blocks and returns the reply data for a GetKeyboardMapping request.
func (cook GetKeyboardMappingCookie) Reply() (*GetKeyboardMappingReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getKeyboardMappingReply(buf), nil
}

// getKeyboardMappingReply reads a byte slice into a GetKeyboardMappingReply value.
func getKeyboardMappingReply(buf []byte) *GetKeyboardMappingReply {
	v := new(GetKeyboardMappingReply)
	b := 1 // skip reply determinant

	v.KeysymsPerKeycode = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	b += 24 // padding

	v.Keysyms = make([]Keysym, v.Length)
	for i := 0; i < int(v.Length); i++ {
		v.Keysyms[i] = Keysym(xgb.Get32(buf[b:]))
		b += 4
	}

	return v
}

// Write request to wire for GetKeyboardMapping
// getKeyboardMappingRequest writes a GetKeyboardMapping request to a byte slice.
func getKeyboardMappingRequest(c *xgb.Conn, FirstKeycode Keycode, Count byte) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 101 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	buf[b] = byte(FirstKeycode)
	b += 1

	buf[b] = Count
	b += 1

	return buf
}

// GetModifierMappingCookie is a cookie used only for GetModifierMapping requests.
type GetModifierMappingCookie struct {
	*xgb.Cookie
}

// GetModifierMapping sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetModifierMappingCookie.Reply()
func GetModifierMapping(c *xgb.Conn) GetModifierMappingCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getModifierMappingRequest(c), cookie)
	return GetModifierMappingCookie{cookie}
}

// GetModifierMappingUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetModifierMappingUnchecked(c *xgb.Conn) GetModifierMappingCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getModifierMappingRequest(c), cookie)
	return GetModifierMappingCookie{cookie}
}

// GetModifierMappingReply represents the data returned from a GetModifierMapping request.
type GetModifierMappingReply struct {
	Sequence            uint16 // sequence number of the request for this reply
	Length              uint32 // number of bytes in this reply
	KeycodesPerModifier byte
	// padding: 24 bytes
	Keycodes []Keycode // size: xgb.Pad(((int(KeycodesPerModifier) * 8) * 1))
}

// Reply blocks and returns the reply data for a GetModifierMapping request.
func (cook GetModifierMappingCookie) Reply() (*GetModifierMappingReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getModifierMappingReply(buf), nil
}

// getModifierMappingReply reads a byte slice into a GetModifierMappingReply value.
func getModifierMappingReply(buf []byte) *GetModifierMappingReply {
	v := new(GetModifierMappingReply)
	b := 1 // skip reply determinant

	v.KeycodesPerModifier = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	b += 24 // padding

	v.Keycodes = make([]Keycode, (int(v.KeycodesPerModifier) * 8))
	for i := 0; i < int((int(v.KeycodesPerModifier) * 8)); i++ {
		v.Keycodes[i] = Keycode(buf[b])
		b += 1
	}

	return v
}

// Write request to wire for GetModifierMapping
// getModifierMappingRequest writes a GetModifierMapping request to a byte slice.
func getModifierMappingRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 119 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetMotionEventsCookie is a cookie used only for GetMotionEvents requests.
type GetMotionEventsCookie struct {
	*xgb.Cookie
}

// GetMotionEvents sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetMotionEventsCookie.Reply()
func GetMotionEvents(c *xgb.Conn, Window Window, Start Timestamp, Stop Timestamp) GetMotionEventsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getMotionEventsRequest(c, Window, Start, Stop), cookie)
	return GetMotionEventsCookie{cookie}
}

// GetMotionEventsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetMotionEventsUnchecked(c *xgb.Conn, Window Window, Start Timestamp, Stop Timestamp) GetMotionEventsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getMotionEventsRequest(c, Window, Start, Stop), cookie)
	return GetMotionEventsCookie{cookie}
}

// GetMotionEventsReply represents the data returned from a GetMotionEvents request.
type GetMotionEventsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	EventsLen uint32
	// padding: 20 bytes
	Events []Timecoord // size: xgb.Pad((int(EventsLen) * 8))
}

// Reply blocks and returns the reply data for a GetMotionEvents request.
func (cook GetMotionEventsCookie) Reply() (*GetMotionEventsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getMotionEventsReply(buf), nil
}

// getMotionEventsReply reads a byte slice into a GetMotionEventsReply value.
func getMotionEventsReply(buf []byte) *GetMotionEventsReply {
	v := new(GetMotionEventsReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.EventsLen = xgb.Get32(buf[b:])
	b += 4

	b += 20 // padding

	v.Events = make([]Timecoord, v.EventsLen)
	b += TimecoordReadList(buf[b:], v.Events)

	return v
}

// Write request to wire for GetMotionEvents
// getMotionEventsRequest writes a GetMotionEvents request to a byte slice.
func getMotionEventsRequest(c *xgb.Conn, Window Window, Start Timestamp, Stop Timestamp) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 39 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Start))
	b += 4

	xgb.Put32(buf[b:], uint32(Stop))
	b += 4

	return buf
}

// GetPointerControlCookie is a cookie used only for GetPointerControl requests.
type GetPointerControlCookie struct {
	*xgb.Cookie
}

// GetPointerControl sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetPointerControlCookie.Reply()
func GetPointerControl(c *xgb.Conn) GetPointerControlCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getPointerControlRequest(c), cookie)
	return GetPointerControlCookie{cookie}
}

// GetPointerControlUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetPointerControlUnchecked(c *xgb.Conn) GetPointerControlCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getPointerControlRequest(c), cookie)
	return GetPointerControlCookie{cookie}
}

// GetPointerControlReply represents the data returned from a GetPointerControl request.
type GetPointerControlReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	AccelerationNumerator   uint16
	AccelerationDenominator uint16
	Threshold               uint16
	// padding: 18 bytes
}

// Reply blocks and returns the reply data for a GetPointerControl request.
func (cook GetPointerControlCookie) Reply() (*GetPointerControlReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getPointerControlReply(buf), nil
}

// getPointerControlReply reads a byte slice into a GetPointerControlReply value.
func getPointerControlReply(buf []byte) *GetPointerControlReply {
	v := new(GetPointerControlReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.AccelerationNumerator = xgb.Get16(buf[b:])
	b += 2

	v.AccelerationDenominator = xgb.Get16(buf[b:])
	b += 2

	v.Threshold = xgb.Get16(buf[b:])
	b += 2

	b += 18 // padding

	return v
}

// Write request to wire for GetPointerControl
// getPointerControlRequest writes a GetPointerControl request to a byte slice.
func getPointerControlRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 106 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetPointerMappingCookie is a cookie used only for GetPointerMapping requests.
type GetPointerMappingCookie struct {
	*xgb.Cookie
}

// GetPointerMapping sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetPointerMappingCookie.Reply()
func GetPointerMapping(c *xgb.Conn) GetPointerMappingCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getPointerMappingRequest(c), cookie)
	return GetPointerMappingCookie{cookie}
}

// GetPointerMappingUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetPointerMappingUnchecked(c *xgb.Conn) GetPointerMappingCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getPointerMappingRequest(c), cookie)
	return GetPointerMappingCookie{cookie}
}

// GetPointerMappingReply represents the data returned from a GetPointerMapping request.
type GetPointerMappingReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	MapLen   byte
	// padding: 24 bytes
	Map []byte // size: xgb.Pad((int(MapLen) * 1))
}

// Reply blocks and returns the reply data for a GetPointerMapping request.
func (cook GetPointerMappingCookie) Reply() (*GetPointerMappingReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getPointerMappingReply(buf), nil
}

// getPointerMappingReply reads a byte slice into a GetPointerMappingReply value.
func getPointerMappingReply(buf []byte) *GetPointerMappingReply {
	v := new(GetPointerMappingReply)
	b := 1 // skip reply determinant

	v.MapLen = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	b += 24 // padding

	v.Map = make([]byte, v.MapLen)
	copy(v.Map[:v.MapLen], buf[b:])
	b += int(v.MapLen)

	return v
}

// Write request to wire for GetPointerMapping
// getPointerMappingRequest writes a GetPointerMapping request to a byte slice.
func getPointerMappingRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 117 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetPropertyCookie is a cookie used only for GetProperty requests.
type GetPropertyCookie struct {
	*xgb.Cookie
}

// GetProperty sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetPropertyCookie.Reply()
func GetProperty(c *xgb.Conn, Delete bool, Window Window, Property Atom, Type Atom, LongOffset uint32, LongLength uint32) GetPropertyCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getPropertyRequest(c, Delete, Window, Property, Type, LongOffset, LongLength), cookie)
	return GetPropertyCookie{cookie}
}

// GetPropertyUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetPropertyUnchecked(c *xgb.Conn, Delete bool, Window Window, Property Atom, Type Atom, LongOffset uint32, LongLength uint32) GetPropertyCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getPropertyRequest(c, Delete, Window, Property, Type, LongOffset, LongLength), cookie)
	return GetPropertyCookie{cookie}
}

// GetPropertyReply represents the data returned from a GetProperty request.
type GetPropertyReply struct {
	Sequence   uint16 // sequence number of the request for this reply
	Length     uint32 // number of bytes in this reply
	Format     byte
	Type       Atom
	BytesAfter uint32
	ValueLen   uint32
	// padding: 12 bytes
	Value []byte // size: xgb.Pad(((int(ValueLen) * (int(Format) / 8)) * 1))
}

// Reply blocks and returns the reply data for a GetProperty request.
func (cook GetPropertyCookie) Reply() (*GetPropertyReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getPropertyReply(buf), nil
}

// getPropertyReply reads a byte slice into a GetPropertyReply value.
func getPropertyReply(buf []byte) *GetPropertyReply {
	v := new(GetPropertyReply)
	b := 1 // skip reply determinant

	v.Format = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Type = Atom(xgb.Get32(buf[b:]))
	b += 4

	v.BytesAfter = xgb.Get32(buf[b:])
	b += 4

	v.ValueLen = xgb.Get32(buf[b:])
	b += 4

	b += 12 // padding

	v.Value = make([]byte, (int(v.ValueLen) * (int(v.Format) / 8)))
	copy(v.Value[:(int(v.ValueLen)*(int(v.Format)/8))], buf[b:])
	b += int((int(v.ValueLen) * (int(v.Format) / 8)))

	return v
}

// Write request to wire for GetProperty
// getPropertyRequest writes a GetProperty request to a byte slice.
func getPropertyRequest(c *xgb.Conn, Delete bool, Window Window, Property Atom, Type Atom, LongOffset uint32, LongLength uint32) []byte {
	size := 24
	b := 0
	buf := make([]byte, size)

	buf[b] = 20 // request opcode
	b += 1

	if Delete {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Property))
	b += 4

	xgb.Put32(buf[b:], uint32(Type))
	b += 4

	xgb.Put32(buf[b:], LongOffset)
	b += 4

	xgb.Put32(buf[b:], LongLength)
	b += 4

	return buf
}

// GetScreenSaverCookie is a cookie used only for GetScreenSaver requests.
type GetScreenSaverCookie struct {
	*xgb.Cookie
}

// GetScreenSaver sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetScreenSaverCookie.Reply()
func GetScreenSaver(c *xgb.Conn) GetScreenSaverCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getScreenSaverRequest(c), cookie)
	return GetScreenSaverCookie{cookie}
}

// GetScreenSaverUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetScreenSaverUnchecked(c *xgb.Conn) GetScreenSaverCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getScreenSaverRequest(c), cookie)
	return GetScreenSaverCookie{cookie}
}

// GetScreenSaverReply represents the data returned from a GetScreenSaver request.
type GetScreenSaverReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Timeout        uint16
	Interval       uint16
	PreferBlanking byte
	AllowExposures byte
	// padding: 18 bytes
}

// Reply blocks and returns the reply data for a GetScreenSaver request.
func (cook GetScreenSaverCookie) Reply() (*GetScreenSaverReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getScreenSaverReply(buf), nil
}

// getScreenSaverReply reads a byte slice into a GetScreenSaverReply value.
func getScreenSaverReply(buf []byte) *GetScreenSaverReply {
	v := new(GetScreenSaverReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Timeout = xgb.Get16(buf[b:])
	b += 2

	v.Interval = xgb.Get16(buf[b:])
	b += 2

	v.PreferBlanking = buf[b]
	b += 1

	v.AllowExposures = buf[b]
	b += 1

	b += 18 // padding

	return v
}

// Write request to wire for GetScreenSaver
// getScreenSaverRequest writes a GetScreenSaver request to a byte slice.
func getScreenSaverRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 108 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// GetSelectionOwnerCookie is a cookie used only for GetSelectionOwner requests.
type GetSelectionOwnerCookie struct {
	*xgb.Cookie
}

// GetSelectionOwner sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetSelectionOwnerCookie.Reply()
func GetSelectionOwner(c *xgb.Conn, Selection Atom) GetSelectionOwnerCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getSelectionOwnerRequest(c, Selection), cookie)
	return GetSelectionOwnerCookie{cookie}
}

// GetSelectionOwnerUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetSelectionOwnerUnchecked(c *xgb.Conn, Selection Atom) GetSelectionOwnerCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getSelectionOwnerRequest(c, Selection), cookie)
	return GetSelectionOwnerCookie{cookie}
}

// GetSelectionOwnerReply represents the data returned from a GetSelectionOwner request.
type GetSelectionOwnerReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Owner Window
}

// Reply blocks and returns the reply data for a GetSelectionOwner request.
func (cook GetSelectionOwnerCookie) Reply() (*GetSelectionOwnerReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getSelectionOwnerReply(buf), nil
}

// getSelectionOwnerReply reads a byte slice into a GetSelectionOwnerReply value.
func getSelectionOwnerReply(buf []byte) *GetSelectionOwnerReply {
	v := new(GetSelectionOwnerReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Owner = Window(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Write request to wire for GetSelectionOwner
// getSelectionOwnerRequest writes a GetSelectionOwner request to a byte slice.
func getSelectionOwnerRequest(c *xgb.Conn, Selection Atom) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 23 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Selection))
	b += 4

	return buf
}

// GetWindowAttributesCookie is a cookie used only for GetWindowAttributes requests.
type GetWindowAttributesCookie struct {
	*xgb.Cookie
}

// GetWindowAttributes sends a checked request.
// If an error occurs, it will be returned with the reply by calling GetWindowAttributesCookie.Reply()
func GetWindowAttributes(c *xgb.Conn, Window Window) GetWindowAttributesCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(getWindowAttributesRequest(c, Window), cookie)
	return GetWindowAttributesCookie{cookie}
}

// GetWindowAttributesUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GetWindowAttributesUnchecked(c *xgb.Conn, Window Window) GetWindowAttributesCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(getWindowAttributesRequest(c, Window), cookie)
	return GetWindowAttributesCookie{cookie}
}

// GetWindowAttributesReply represents the data returned from a GetWindowAttributes request.
type GetWindowAttributesReply struct {
	Sequence           uint16 // sequence number of the request for this reply
	Length             uint32 // number of bytes in this reply
	BackingStore       byte
	Visual             Visualid
	Class              uint16
	BitGravity         byte
	WinGravity         byte
	BackingPlanes      uint32
	BackingPixel       uint32
	SaveUnder          bool
	MapIsInstalled     bool
	MapState           byte
	OverrideRedirect   bool
	Colormap           Colormap
	AllEventMasks      uint32
	YourEventMask      uint32
	DoNotPropagateMask uint16
	// padding: 2 bytes
}

// Reply blocks and returns the reply data for a GetWindowAttributes request.
func (cook GetWindowAttributesCookie) Reply() (*GetWindowAttributesReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return getWindowAttributesReply(buf), nil
}

// getWindowAttributesReply reads a byte slice into a GetWindowAttributesReply value.
func getWindowAttributesReply(buf []byte) *GetWindowAttributesReply {
	v := new(GetWindowAttributesReply)
	b := 1 // skip reply determinant

	v.BackingStore = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Visual = Visualid(xgb.Get32(buf[b:]))
	b += 4

	v.Class = xgb.Get16(buf[b:])
	b += 2

	v.BitGravity = buf[b]
	b += 1

	v.WinGravity = buf[b]
	b += 1

	v.BackingPlanes = xgb.Get32(buf[b:])
	b += 4

	v.BackingPixel = xgb.Get32(buf[b:])
	b += 4

	if buf[b] == 1 {
		v.SaveUnder = true
	} else {
		v.SaveUnder = false
	}
	b += 1

	if buf[b] == 1 {
		v.MapIsInstalled = true
	} else {
		v.MapIsInstalled = false
	}
	b += 1

	v.MapState = buf[b]
	b += 1

	if buf[b] == 1 {
		v.OverrideRedirect = true
	} else {
		v.OverrideRedirect = false
	}
	b += 1

	v.Colormap = Colormap(xgb.Get32(buf[b:]))
	b += 4

	v.AllEventMasks = xgb.Get32(buf[b:])
	b += 4

	v.YourEventMask = xgb.Get32(buf[b:])
	b += 4

	v.DoNotPropagateMask = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	return v
}

// Write request to wire for GetWindowAttributes
// getWindowAttributesRequest writes a GetWindowAttributes request to a byte slice.
func getWindowAttributesRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 3 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// GrabButtonCookie is a cookie used only for GrabButton requests.
type GrabButtonCookie struct {
	*xgb.Cookie
}

// GrabButton sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GrabButton(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Button byte, Modifiers uint16) GrabButtonCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(grabButtonRequest(c, OwnerEvents, GrabWindow, EventMask, PointerMode, KeyboardMode, ConfineTo, Cursor, Button, Modifiers), cookie)
	return GrabButtonCookie{cookie}
}

// GrabButtonChecked sends a checked request.
// If an error occurs, it can be retrieved using GrabButtonCookie.Check()
func GrabButtonChecked(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Button byte, Modifiers uint16) GrabButtonCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(grabButtonRequest(c, OwnerEvents, GrabWindow, EventMask, PointerMode, KeyboardMode, ConfineTo, Cursor, Button, Modifiers), cookie)
	return GrabButtonCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook GrabButtonCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for GrabButton
// grabButtonRequest writes a GrabButton request to a byte slice.
func grabButtonRequest(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Button byte, Modifiers uint16) []byte {
	size := 24
	b := 0
	buf := make([]byte, size)

	buf[b] = 28 // request opcode
	b += 1

	if OwnerEvents {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put16(buf[b:], EventMask)
	b += 2

	buf[b] = PointerMode
	b += 1

	buf[b] = KeyboardMode
	b += 1

	xgb.Put32(buf[b:], uint32(ConfineTo))
	b += 4

	xgb.Put32(buf[b:], uint32(Cursor))
	b += 4

	buf[b] = Button
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], Modifiers)
	b += 2

	return buf
}

// GrabKeyCookie is a cookie used only for GrabKey requests.
type GrabKeyCookie struct {
	*xgb.Cookie
}

// GrabKey sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GrabKey(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Modifiers uint16, Key Keycode, PointerMode byte, KeyboardMode byte) GrabKeyCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(grabKeyRequest(c, OwnerEvents, GrabWindow, Modifiers, Key, PointerMode, KeyboardMode), cookie)
	return GrabKeyCookie{cookie}
}

// GrabKeyChecked sends a checked request.
// If an error occurs, it can be retrieved using GrabKeyCookie.Check()
func GrabKeyChecked(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Modifiers uint16, Key Keycode, PointerMode byte, KeyboardMode byte) GrabKeyCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(grabKeyRequest(c, OwnerEvents, GrabWindow, Modifiers, Key, PointerMode, KeyboardMode), cookie)
	return GrabKeyCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook GrabKeyCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for GrabKey
// grabKeyRequest writes a GrabKey request to a byte slice.
func grabKeyRequest(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Modifiers uint16, Key Keycode, PointerMode byte, KeyboardMode byte) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 33 // request opcode
	b += 1

	if OwnerEvents {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put16(buf[b:], Modifiers)
	b += 2

	buf[b] = byte(Key)
	b += 1

	buf[b] = PointerMode
	b += 1

	buf[b] = KeyboardMode
	b += 1

	b += 3 // padding

	return buf
}

// GrabKeyboardCookie is a cookie used only for GrabKeyboard requests.
type GrabKeyboardCookie struct {
	*xgb.Cookie
}

// GrabKeyboard sends a checked request.
// If an error occurs, it will be returned with the reply by calling GrabKeyboardCookie.Reply()
func GrabKeyboard(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Time Timestamp, PointerMode byte, KeyboardMode byte) GrabKeyboardCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(grabKeyboardRequest(c, OwnerEvents, GrabWindow, Time, PointerMode, KeyboardMode), cookie)
	return GrabKeyboardCookie{cookie}
}

// GrabKeyboardUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GrabKeyboardUnchecked(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Time Timestamp, PointerMode byte, KeyboardMode byte) GrabKeyboardCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(grabKeyboardRequest(c, OwnerEvents, GrabWindow, Time, PointerMode, KeyboardMode), cookie)
	return GrabKeyboardCookie{cookie}
}

// GrabKeyboardReply represents the data returned from a GrabKeyboard request.
type GrabKeyboardReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Status   byte
}

// Reply blocks and returns the reply data for a GrabKeyboard request.
func (cook GrabKeyboardCookie) Reply() (*GrabKeyboardReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return grabKeyboardReply(buf), nil
}

// grabKeyboardReply reads a byte slice into a GrabKeyboardReply value.
func grabKeyboardReply(buf []byte) *GrabKeyboardReply {
	v := new(GrabKeyboardReply)
	b := 1 // skip reply determinant

	v.Status = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	return v
}

// Write request to wire for GrabKeyboard
// grabKeyboardRequest writes a GrabKeyboard request to a byte slice.
func grabKeyboardRequest(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, Time Timestamp, PointerMode byte, KeyboardMode byte) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 31 // request opcode
	b += 1

	if OwnerEvents {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	buf[b] = PointerMode
	b += 1

	buf[b] = KeyboardMode
	b += 1

	b += 2 // padding

	return buf
}

// GrabPointerCookie is a cookie used only for GrabPointer requests.
type GrabPointerCookie struct {
	*xgb.Cookie
}

// GrabPointer sends a checked request.
// If an error occurs, it will be returned with the reply by calling GrabPointerCookie.Reply()
func GrabPointer(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Time Timestamp) GrabPointerCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(grabPointerRequest(c, OwnerEvents, GrabWindow, EventMask, PointerMode, KeyboardMode, ConfineTo, Cursor, Time), cookie)
	return GrabPointerCookie{cookie}
}

// GrabPointerUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GrabPointerUnchecked(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Time Timestamp) GrabPointerCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(grabPointerRequest(c, OwnerEvents, GrabWindow, EventMask, PointerMode, KeyboardMode, ConfineTo, Cursor, Time), cookie)
	return GrabPointerCookie{cookie}
}

// GrabPointerReply represents the data returned from a GrabPointer request.
type GrabPointerReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Status   byte
}

// Reply blocks and returns the reply data for a GrabPointer request.
func (cook GrabPointerCookie) Reply() (*GrabPointerReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return grabPointerReply(buf), nil
}

// grabPointerReply reads a byte slice into a GrabPointerReply value.
func grabPointerReply(buf []byte) *GrabPointerReply {
	v := new(GrabPointerReply)
	b := 1 // skip reply determinant

	v.Status = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	return v
}

// Write request to wire for GrabPointer
// grabPointerRequest writes a GrabPointer request to a byte slice.
func grabPointerRequest(c *xgb.Conn, OwnerEvents bool, GrabWindow Window, EventMask uint16, PointerMode byte, KeyboardMode byte, ConfineTo Window, Cursor Cursor, Time Timestamp) []byte {
	size := 24
	b := 0
	buf := make([]byte, size)

	buf[b] = 26 // request opcode
	b += 1

	if OwnerEvents {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put16(buf[b:], EventMask)
	b += 2

	buf[b] = PointerMode
	b += 1

	buf[b] = KeyboardMode
	b += 1

	xgb.Put32(buf[b:], uint32(ConfineTo))
	b += 4

	xgb.Put32(buf[b:], uint32(Cursor))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// GrabServerCookie is a cookie used only for GrabServer requests.
type GrabServerCookie struct {
	*xgb.Cookie
}

// GrabServer sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func GrabServer(c *xgb.Conn) GrabServerCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(grabServerRequest(c), cookie)
	return GrabServerCookie{cookie}
}

// GrabServerChecked sends a checked request.
// If an error occurs, it can be retrieved using GrabServerCookie.Check()
func GrabServerChecked(c *xgb.Conn) GrabServerCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(grabServerRequest(c), cookie)
	return GrabServerCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook GrabServerCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for GrabServer
// grabServerRequest writes a GrabServer request to a byte slice.
func grabServerRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 36 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// ImageText16Cookie is a cookie used only for ImageText16 requests.
type ImageText16Cookie struct {
	*xgb.Cookie
}

// ImageText16 sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ImageText16(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String []Char2b) ImageText16Cookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(imageText16Request(c, StringLen, Drawable, Gc, X, Y, String), cookie)
	return ImageText16Cookie{cookie}
}

// ImageText16Checked sends a checked request.
// If an error occurs, it can be retrieved using ImageText16Cookie.Check()
func ImageText16Checked(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String []Char2b) ImageText16Cookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(imageText16Request(c, StringLen, Drawable, Gc, X, Y, String), cookie)
	return ImageText16Cookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ImageText16Cookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ImageText16
// imageText16Request writes a ImageText16 request to a byte slice.
func imageText16Request(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String []Char2b) []byte {
	size := xgb.Pad((16 + xgb.Pad((int(StringLen) * 2))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 77 // request opcode
	b += 1

	buf[b] = StringLen
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	b += Char2bListBytes(buf[b:], String)

	return buf
}

// ImageText8Cookie is a cookie used only for ImageText8 requests.
type ImageText8Cookie struct {
	*xgb.Cookie
}

// ImageText8 sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ImageText8(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String string) ImageText8Cookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(imageText8Request(c, StringLen, Drawable, Gc, X, Y, String), cookie)
	return ImageText8Cookie{cookie}
}

// ImageText8Checked sends a checked request.
// If an error occurs, it can be retrieved using ImageText8Cookie.Check()
func ImageText8Checked(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String string) ImageText8Cookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(imageText8Request(c, StringLen, Drawable, Gc, X, Y, String), cookie)
	return ImageText8Cookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ImageText8Cookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ImageText8
// imageText8Request writes a ImageText8 request to a byte slice.
func imageText8Request(c *xgb.Conn, StringLen byte, Drawable Drawable, Gc Gcontext, X int16, Y int16, String string) []byte {
	size := xgb.Pad((16 + xgb.Pad((int(StringLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 76 // request opcode
	b += 1

	buf[b] = StringLen
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	copy(buf[b:], String[:StringLen])
	b += int(StringLen)

	return buf
}

// InstallColormapCookie is a cookie used only for InstallColormap requests.
type InstallColormapCookie struct {
	*xgb.Cookie
}

// InstallColormap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func InstallColormap(c *xgb.Conn, Cmap Colormap) InstallColormapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(installColormapRequest(c, Cmap), cookie)
	return InstallColormapCookie{cookie}
}

// InstallColormapChecked sends a checked request.
// If an error occurs, it can be retrieved using InstallColormapCookie.Check()
func InstallColormapChecked(c *xgb.Conn, Cmap Colormap) InstallColormapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(installColormapRequest(c, Cmap), cookie)
	return InstallColormapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook InstallColormapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for InstallColormap
// installColormapRequest writes a InstallColormap request to a byte slice.
func installColormapRequest(c *xgb.Conn, Cmap Colormap) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 81 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	return buf
}

// InternAtomCookie is a cookie used only for InternAtom requests.
type InternAtomCookie struct {
	*xgb.Cookie
}

// InternAtom sends a checked request.
// If an error occurs, it will be returned with the reply by calling InternAtomCookie.Reply()
func InternAtom(c *xgb.Conn, OnlyIfExists bool, NameLen uint16, Name string) InternAtomCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(internAtomRequest(c, OnlyIfExists, NameLen, Name), cookie)
	return InternAtomCookie{cookie}
}

// InternAtomUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func InternAtomUnchecked(c *xgb.Conn, OnlyIfExists bool, NameLen uint16, Name string) InternAtomCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(internAtomRequest(c, OnlyIfExists, NameLen, Name), cookie)
	return InternAtomCookie{cookie}
}

// InternAtomReply represents the data returned from a InternAtom request.
type InternAtomReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Atom Atom
}

// Reply blocks and returns the reply data for a InternAtom request.
func (cook InternAtomCookie) Reply() (*InternAtomReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return internAtomReply(buf), nil
}

// internAtomReply reads a byte slice into a InternAtomReply value.
func internAtomReply(buf []byte) *InternAtomReply {
	v := new(InternAtomReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Atom = Atom(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Write request to wire for InternAtom
// internAtomRequest writes a InternAtom request to a byte slice.
func internAtomRequest(c *xgb.Conn, OnlyIfExists bool, NameLen uint16, Name string) []byte {
	size := xgb.Pad((8 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 16 // request opcode
	b += 1

	if OnlyIfExists {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// KillClientCookie is a cookie used only for KillClient requests.
type KillClientCookie struct {
	*xgb.Cookie
}

// KillClient sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func KillClient(c *xgb.Conn, Resource uint32) KillClientCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(killClientRequest(c, Resource), cookie)
	return KillClientCookie{cookie}
}

// KillClientChecked sends a checked request.
// If an error occurs, it can be retrieved using KillClientCookie.Check()
func KillClientChecked(c *xgb.Conn, Resource uint32) KillClientCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(killClientRequest(c, Resource), cookie)
	return KillClientCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook KillClientCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for KillClient
// killClientRequest writes a KillClient request to a byte slice.
func killClientRequest(c *xgb.Conn, Resource uint32) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 113 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], Resource)
	b += 4

	return buf
}

// ListExtensionsCookie is a cookie used only for ListExtensions requests.
type ListExtensionsCookie struct {
	*xgb.Cookie
}

// ListExtensions sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListExtensionsCookie.Reply()
func ListExtensions(c *xgb.Conn) ListExtensionsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listExtensionsRequest(c), cookie)
	return ListExtensionsCookie{cookie}
}

// ListExtensionsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListExtensionsUnchecked(c *xgb.Conn) ListExtensionsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listExtensionsRequest(c), cookie)
	return ListExtensionsCookie{cookie}
}

// ListExtensionsReply represents the data returned from a ListExtensions request.
type ListExtensionsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	NamesLen byte
	// padding: 24 bytes
	Names []Str // size: StrListSize(Names)
}

// Reply blocks and returns the reply data for a ListExtensions request.
func (cook ListExtensionsCookie) Reply() (*ListExtensionsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listExtensionsReply(buf), nil
}

// listExtensionsReply reads a byte slice into a ListExtensionsReply value.
func listExtensionsReply(buf []byte) *ListExtensionsReply {
	v := new(ListExtensionsReply)
	b := 1 // skip reply determinant

	v.NamesLen = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	b += 24 // padding

	v.Names = make([]Str, v.NamesLen)
	b += StrReadList(buf[b:], v.Names)

	return v
}

// Write request to wire for ListExtensions
// listExtensionsRequest writes a ListExtensions request to a byte slice.
func listExtensionsRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 99 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// ListFontsCookie is a cookie used only for ListFonts requests.
type ListFontsCookie struct {
	*xgb.Cookie
}

// ListFonts sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListFontsCookie.Reply()
func ListFonts(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) ListFontsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listFontsRequest(c, MaxNames, PatternLen, Pattern), cookie)
	return ListFontsCookie{cookie}
}

// ListFontsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListFontsUnchecked(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) ListFontsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listFontsRequest(c, MaxNames, PatternLen, Pattern), cookie)
	return ListFontsCookie{cookie}
}

// ListFontsReply represents the data returned from a ListFonts request.
type ListFontsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	NamesLen uint16
	// padding: 22 bytes
	Names []Str // size: StrListSize(Names)
}

// Reply blocks and returns the reply data for a ListFonts request.
func (cook ListFontsCookie) Reply() (*ListFontsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listFontsReply(buf), nil
}

// listFontsReply reads a byte slice into a ListFontsReply value.
func listFontsReply(buf []byte) *ListFontsReply {
	v := new(ListFontsReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.NamesLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Names = make([]Str, v.NamesLen)
	b += StrReadList(buf[b:], v.Names)

	return v
}

// Write request to wire for ListFonts
// listFontsRequest writes a ListFonts request to a byte slice.
func listFontsRequest(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) []byte {
	size := xgb.Pad((8 + xgb.Pad((int(PatternLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 49 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], MaxNames)
	b += 2

	xgb.Put16(buf[b:], PatternLen)
	b += 2

	copy(buf[b:], Pattern[:PatternLen])
	b += int(PatternLen)

	return buf
}

// ListFontsWithInfoCookie is a cookie used only for ListFontsWithInfo requests.
type ListFontsWithInfoCookie struct {
	*xgb.Cookie
}

// ListFontsWithInfo sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListFontsWithInfoCookie.Reply()
func ListFontsWithInfo(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) ListFontsWithInfoCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listFontsWithInfoRequest(c, MaxNames, PatternLen, Pattern), cookie)
	return ListFontsWithInfoCookie{cookie}
}

// ListFontsWithInfoUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListFontsWithInfoUnchecked(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) ListFontsWithInfoCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listFontsWithInfoRequest(c, MaxNames, PatternLen, Pattern), cookie)
	return ListFontsWithInfoCookie{cookie}
}

// ListFontsWithInfoReply represents the data returned from a ListFontsWithInfo request.
type ListFontsWithInfoReply struct {
	Sequence  uint16 // sequence number of the request for this reply
	Length    uint32 // number of bytes in this reply
	NameLen   byte
	MinBounds Charinfo
	// padding: 4 bytes
	MaxBounds Charinfo
	// padding: 4 bytes
	MinCharOrByte2 uint16
	MaxCharOrByte2 uint16
	DefaultChar    uint16
	PropertiesLen  uint16
	DrawDirection  byte
	MinByte1       byte
	MaxByte1       byte
	AllCharsExist  bool
	FontAscent     int16
	FontDescent    int16
	RepliesHint    uint32
	Properties     []Fontprop // size: xgb.Pad((int(PropertiesLen) * 8))
	Name           string     // size: xgb.Pad((int(NameLen) * 1))
}

// Reply blocks and returns the reply data for a ListFontsWithInfo request.
func (cook ListFontsWithInfoCookie) Reply() (*ListFontsWithInfoReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listFontsWithInfoReply(buf), nil
}

// listFontsWithInfoReply reads a byte slice into a ListFontsWithInfoReply value.
func listFontsWithInfoReply(buf []byte) *ListFontsWithInfoReply {
	v := new(ListFontsWithInfoReply)
	b := 1 // skip reply determinant

	v.NameLen = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.MinBounds = Charinfo{}
	b += CharinfoRead(buf[b:], &v.MinBounds)

	b += 4 // padding

	v.MaxBounds = Charinfo{}
	b += CharinfoRead(buf[b:], &v.MaxBounds)

	b += 4 // padding

	v.MinCharOrByte2 = xgb.Get16(buf[b:])
	b += 2

	v.MaxCharOrByte2 = xgb.Get16(buf[b:])
	b += 2

	v.DefaultChar = xgb.Get16(buf[b:])
	b += 2

	v.PropertiesLen = xgb.Get16(buf[b:])
	b += 2

	v.DrawDirection = buf[b]
	b += 1

	v.MinByte1 = buf[b]
	b += 1

	v.MaxByte1 = buf[b]
	b += 1

	if buf[b] == 1 {
		v.AllCharsExist = true
	} else {
		v.AllCharsExist = false
	}
	b += 1

	v.FontAscent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.FontDescent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RepliesHint = xgb.Get32(buf[b:])
	b += 4

	v.Properties = make([]Fontprop, v.PropertiesLen)
	b += FontpropReadList(buf[b:], v.Properties)

	{
		byteString := make([]byte, v.NameLen)
		copy(byteString[:v.NameLen], buf[b:])
		v.Name = string(byteString)
		b += int(v.NameLen)
	}

	return v
}

// Write request to wire for ListFontsWithInfo
// listFontsWithInfoRequest writes a ListFontsWithInfo request to a byte slice.
func listFontsWithInfoRequest(c *xgb.Conn, MaxNames uint16, PatternLen uint16, Pattern string) []byte {
	size := xgb.Pad((8 + xgb.Pad((int(PatternLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 50 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], MaxNames)
	b += 2

	xgb.Put16(buf[b:], PatternLen)
	b += 2

	copy(buf[b:], Pattern[:PatternLen])
	b += int(PatternLen)

	return buf
}

// ListHostsCookie is a cookie used only for ListHosts requests.
type ListHostsCookie struct {
	*xgb.Cookie
}

// ListHosts sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListHostsCookie.Reply()
func ListHosts(c *xgb.Conn) ListHostsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listHostsRequest(c), cookie)
	return ListHostsCookie{cookie}
}

// ListHostsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListHostsUnchecked(c *xgb.Conn) ListHostsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listHostsRequest(c), cookie)
	return ListHostsCookie{cookie}
}

// ListHostsReply represents the data returned from a ListHosts request.
type ListHostsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Mode     byte
	HostsLen uint16
	// padding: 22 bytes
	Hosts []Host // size: HostListSize(Hosts)
}

// Reply blocks and returns the reply data for a ListHosts request.
func (cook ListHostsCookie) Reply() (*ListHostsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listHostsReply(buf), nil
}

// listHostsReply reads a byte slice into a ListHostsReply value.
func listHostsReply(buf []byte) *ListHostsReply {
	v := new(ListHostsReply)
	b := 1 // skip reply determinant

	v.Mode = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.HostsLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Hosts = make([]Host, v.HostsLen)
	b += HostReadList(buf[b:], v.Hosts)

	return v
}

// Write request to wire for ListHosts
// listHostsRequest writes a ListHosts request to a byte slice.
func listHostsRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 110 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// ListInstalledColormapsCookie is a cookie used only for ListInstalledColormaps requests.
type ListInstalledColormapsCookie struct {
	*xgb.Cookie
}

// ListInstalledColormaps sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListInstalledColormapsCookie.Reply()
func ListInstalledColormaps(c *xgb.Conn, Window Window) ListInstalledColormapsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listInstalledColormapsRequest(c, Window), cookie)
	return ListInstalledColormapsCookie{cookie}
}

// ListInstalledColormapsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListInstalledColormapsUnchecked(c *xgb.Conn, Window Window) ListInstalledColormapsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listInstalledColormapsRequest(c, Window), cookie)
	return ListInstalledColormapsCookie{cookie}
}

// ListInstalledColormapsReply represents the data returned from a ListInstalledColormaps request.
type ListInstalledColormapsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	CmapsLen uint16
	// padding: 22 bytes
	Cmaps []Colormap // size: xgb.Pad((int(CmapsLen) * 4))
}

// Reply blocks and returns the reply data for a ListInstalledColormaps request.
func (cook ListInstalledColormapsCookie) Reply() (*ListInstalledColormapsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listInstalledColormapsReply(buf), nil
}

// listInstalledColormapsReply reads a byte slice into a ListInstalledColormapsReply value.
func listInstalledColormapsReply(buf []byte) *ListInstalledColormapsReply {
	v := new(ListInstalledColormapsReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.CmapsLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Cmaps = make([]Colormap, v.CmapsLen)
	for i := 0; i < int(v.CmapsLen); i++ {
		v.Cmaps[i] = Colormap(xgb.Get32(buf[b:]))
		b += 4
	}

	return v
}

// Write request to wire for ListInstalledColormaps
// listInstalledColormapsRequest writes a ListInstalledColormaps request to a byte slice.
func listInstalledColormapsRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 83 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// ListPropertiesCookie is a cookie used only for ListProperties requests.
type ListPropertiesCookie struct {
	*xgb.Cookie
}

// ListProperties sends a checked request.
// If an error occurs, it will be returned with the reply by calling ListPropertiesCookie.Reply()
func ListProperties(c *xgb.Conn, Window Window) ListPropertiesCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(listPropertiesRequest(c, Window), cookie)
	return ListPropertiesCookie{cookie}
}

// ListPropertiesUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ListPropertiesUnchecked(c *xgb.Conn, Window Window) ListPropertiesCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(listPropertiesRequest(c, Window), cookie)
	return ListPropertiesCookie{cookie}
}

// ListPropertiesReply represents the data returned from a ListProperties request.
type ListPropertiesReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	AtomsLen uint16
	// padding: 22 bytes
	Atoms []Atom // size: xgb.Pad((int(AtomsLen) * 4))
}

// Reply blocks and returns the reply data for a ListProperties request.
func (cook ListPropertiesCookie) Reply() (*ListPropertiesReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return listPropertiesReply(buf), nil
}

// listPropertiesReply reads a byte slice into a ListPropertiesReply value.
func listPropertiesReply(buf []byte) *ListPropertiesReply {
	v := new(ListPropertiesReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.AtomsLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Atoms = make([]Atom, v.AtomsLen)
	for i := 0; i < int(v.AtomsLen); i++ {
		v.Atoms[i] = Atom(xgb.Get32(buf[b:]))
		b += 4
	}

	return v
}

// Write request to wire for ListProperties
// listPropertiesRequest writes a ListProperties request to a byte slice.
func listPropertiesRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 21 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// LookupColorCookie is a cookie used only for LookupColor requests.
type LookupColorCookie struct {
	*xgb.Cookie
}

// LookupColor sends a checked request.
// If an error occurs, it will be returned with the reply by calling LookupColorCookie.Reply()
func LookupColor(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) LookupColorCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(lookupColorRequest(c, Cmap, NameLen, Name), cookie)
	return LookupColorCookie{cookie}
}

// LookupColorUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func LookupColorUnchecked(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) LookupColorCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(lookupColorRequest(c, Cmap, NameLen, Name), cookie)
	return LookupColorCookie{cookie}
}

// LookupColorReply represents the data returned from a LookupColor request.
type LookupColorReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	ExactRed    uint16
	ExactGreen  uint16
	ExactBlue   uint16
	VisualRed   uint16
	VisualGreen uint16
	VisualBlue  uint16
}

// Reply blocks and returns the reply data for a LookupColor request.
func (cook LookupColorCookie) Reply() (*LookupColorReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return lookupColorReply(buf), nil
}

// lookupColorReply reads a byte slice into a LookupColorReply value.
func lookupColorReply(buf []byte) *LookupColorReply {
	v := new(LookupColorReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.ExactRed = xgb.Get16(buf[b:])
	b += 2

	v.ExactGreen = xgb.Get16(buf[b:])
	b += 2

	v.ExactBlue = xgb.Get16(buf[b:])
	b += 2

	v.VisualRed = xgb.Get16(buf[b:])
	b += 2

	v.VisualGreen = xgb.Get16(buf[b:])
	b += 2

	v.VisualBlue = xgb.Get16(buf[b:])
	b += 2

	return v
}

// Write request to wire for LookupColor
// lookupColorRequest writes a LookupColor request to a byte slice.
func lookupColorRequest(c *xgb.Conn, Cmap Colormap, NameLen uint16, Name string) []byte {
	size := xgb.Pad((12 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 92 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// MapSubwindowsCookie is a cookie used only for MapSubwindows requests.
type MapSubwindowsCookie struct {
	*xgb.Cookie
}

// MapSubwindows sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func MapSubwindows(c *xgb.Conn, Window Window) MapSubwindowsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(mapSubwindowsRequest(c, Window), cookie)
	return MapSubwindowsCookie{cookie}
}

// MapSubwindowsChecked sends a checked request.
// If an error occurs, it can be retrieved using MapSubwindowsCookie.Check()
func MapSubwindowsChecked(c *xgb.Conn, Window Window) MapSubwindowsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(mapSubwindowsRequest(c, Window), cookie)
	return MapSubwindowsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook MapSubwindowsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for MapSubwindows
// mapSubwindowsRequest writes a MapSubwindows request to a byte slice.
func mapSubwindowsRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 9 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// MapWindowCookie is a cookie used only for MapWindow requests.
type MapWindowCookie struct {
	*xgb.Cookie
}

// MapWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func MapWindow(c *xgb.Conn, Window Window) MapWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(mapWindowRequest(c, Window), cookie)
	return MapWindowCookie{cookie}
}

// MapWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using MapWindowCookie.Check()
func MapWindowChecked(c *xgb.Conn, Window Window) MapWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(mapWindowRequest(c, Window), cookie)
	return MapWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook MapWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for MapWindow
// mapWindowRequest writes a MapWindow request to a byte slice.
func mapWindowRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 8 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// NoOperationCookie is a cookie used only for NoOperation requests.
type NoOperationCookie struct {
	*xgb.Cookie
}

// NoOperation sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func NoOperation(c *xgb.Conn) NoOperationCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(noOperationRequest(c), cookie)
	return NoOperationCookie{cookie}
}

// NoOperationChecked sends a checked request.
// If an error occurs, it can be retrieved using NoOperationCookie.Check()
func NoOperationChecked(c *xgb.Conn) NoOperationCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(noOperationRequest(c), cookie)
	return NoOperationCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook NoOperationCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for NoOperation
// noOperationRequest writes a NoOperation request to a byte slice.
func noOperationRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 127 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// OpenFontCookie is a cookie used only for OpenFont requests.
type OpenFontCookie struct {
	*xgb.Cookie
}

// OpenFont sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func OpenFont(c *xgb.Conn, Fid Font, NameLen uint16, Name string) OpenFontCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(openFontRequest(c, Fid, NameLen, Name), cookie)
	return OpenFontCookie{cookie}
}

// OpenFontChecked sends a checked request.
// If an error occurs, it can be retrieved using OpenFontCookie.Check()
func OpenFontChecked(c *xgb.Conn, Fid Font, NameLen uint16, Name string) OpenFontCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(openFontRequest(c, Fid, NameLen, Name), cookie)
	return OpenFontCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook OpenFontCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for OpenFont
// openFontRequest writes a OpenFont request to a byte slice.
func openFontRequest(c *xgb.Conn, Fid Font, NameLen uint16, Name string) []byte {
	size := xgb.Pad((12 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 45 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Fid))
	b += 4

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// PolyArcCookie is a cookie used only for PolyArc requests.
type PolyArcCookie struct {
	*xgb.Cookie
}

// PolyArc sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyArc(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) PolyArcCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyArcRequest(c, Drawable, Gc, Arcs), cookie)
	return PolyArcCookie{cookie}
}

// PolyArcChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyArcCookie.Check()
func PolyArcChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) PolyArcCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyArcRequest(c, Drawable, Gc, Arcs), cookie)
	return PolyArcCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyArcCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyArc
// polyArcRequest writes a PolyArc request to a byte slice.
func polyArcRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Arcs) * 12))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 68 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += ArcListBytes(buf[b:], Arcs)

	return buf
}

// PolyFillArcCookie is a cookie used only for PolyFillArc requests.
type PolyFillArcCookie struct {
	*xgb.Cookie
}

// PolyFillArc sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyFillArc(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) PolyFillArcCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyFillArcRequest(c, Drawable, Gc, Arcs), cookie)
	return PolyFillArcCookie{cookie}
}

// PolyFillArcChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyFillArcCookie.Check()
func PolyFillArcChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) PolyFillArcCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyFillArcRequest(c, Drawable, Gc, Arcs), cookie)
	return PolyFillArcCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyFillArcCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyFillArc
// polyFillArcRequest writes a PolyFillArc request to a byte slice.
func polyFillArcRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Arcs []Arc) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Arcs) * 12))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 71 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += ArcListBytes(buf[b:], Arcs)

	return buf
}

// PolyFillRectangleCookie is a cookie used only for PolyFillRectangle requests.
type PolyFillRectangleCookie struct {
	*xgb.Cookie
}

// PolyFillRectangle sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyFillRectangle(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) PolyFillRectangleCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyFillRectangleRequest(c, Drawable, Gc, Rectangles), cookie)
	return PolyFillRectangleCookie{cookie}
}

// PolyFillRectangleChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyFillRectangleCookie.Check()
func PolyFillRectangleChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) PolyFillRectangleCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyFillRectangleRequest(c, Drawable, Gc, Rectangles), cookie)
	return PolyFillRectangleCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyFillRectangleCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyFillRectangle
// polyFillRectangleRequest writes a PolyFillRectangle request to a byte slice.
func polyFillRectangleRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Rectangles) * 8))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 70 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += RectangleListBytes(buf[b:], Rectangles)

	return buf
}

// PolyLineCookie is a cookie used only for PolyLine requests.
type PolyLineCookie struct {
	*xgb.Cookie
}

// PolyLine sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyLine(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) PolyLineCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyLineRequest(c, CoordinateMode, Drawable, Gc, Points), cookie)
	return PolyLineCookie{cookie}
}

// PolyLineChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyLineCookie.Check()
func PolyLineChecked(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) PolyLineCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyLineRequest(c, CoordinateMode, Drawable, Gc, Points), cookie)
	return PolyLineCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyLineCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyLine
// polyLineRequest writes a PolyLine request to a byte slice.
func polyLineRequest(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Points) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 65 // request opcode
	b += 1

	buf[b] = CoordinateMode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += PointListBytes(buf[b:], Points)

	return buf
}

// PolyPointCookie is a cookie used only for PolyPoint requests.
type PolyPointCookie struct {
	*xgb.Cookie
}

// PolyPoint sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyPoint(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) PolyPointCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyPointRequest(c, CoordinateMode, Drawable, Gc, Points), cookie)
	return PolyPointCookie{cookie}
}

// PolyPointChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyPointCookie.Check()
func PolyPointChecked(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) PolyPointCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyPointRequest(c, CoordinateMode, Drawable, Gc, Points), cookie)
	return PolyPointCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyPointCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyPoint
// polyPointRequest writes a PolyPoint request to a byte slice.
func polyPointRequest(c *xgb.Conn, CoordinateMode byte, Drawable Drawable, Gc Gcontext, Points []Point) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Points) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 64 // request opcode
	b += 1

	buf[b] = CoordinateMode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += PointListBytes(buf[b:], Points)

	return buf
}

// PolyRectangleCookie is a cookie used only for PolyRectangle requests.
type PolyRectangleCookie struct {
	*xgb.Cookie
}

// PolyRectangle sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyRectangle(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) PolyRectangleCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyRectangleRequest(c, Drawable, Gc, Rectangles), cookie)
	return PolyRectangleCookie{cookie}
}

// PolyRectangleChecked sends a checked request.
// If an error occurs, it can be retrieved using PolyRectangleCookie.Check()
func PolyRectangleChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) PolyRectangleCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyRectangleRequest(c, Drawable, Gc, Rectangles), cookie)
	return PolyRectangleCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyRectangleCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyRectangle
// polyRectangleRequest writes a PolyRectangle request to a byte slice.
func polyRectangleRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Rectangles []Rectangle) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Rectangles) * 8))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 67 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += RectangleListBytes(buf[b:], Rectangles)

	return buf
}

// PolySegmentCookie is a cookie used only for PolySegment requests.
type PolySegmentCookie struct {
	*xgb.Cookie
}

// PolySegment sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolySegment(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Segments []Segment) PolySegmentCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polySegmentRequest(c, Drawable, Gc, Segments), cookie)
	return PolySegmentCookie{cookie}
}

// PolySegmentChecked sends a checked request.
// If an error occurs, it can be retrieved using PolySegmentCookie.Check()
func PolySegmentChecked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Segments []Segment) PolySegmentCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polySegmentRequest(c, Drawable, Gc, Segments), cookie)
	return PolySegmentCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolySegmentCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolySegment
// polySegmentRequest writes a PolySegment request to a byte slice.
func polySegmentRequest(c *xgb.Conn, Drawable Drawable, Gc Gcontext, Segments []Segment) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Segments) * 8))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 66 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	b += SegmentListBytes(buf[b:], Segments)

	return buf
}

// PolyText16Cookie is a cookie used only for PolyText16 requests.
type PolyText16Cookie struct {
	*xgb.Cookie
}

// PolyText16 sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyText16(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) PolyText16Cookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyText16Request(c, Drawable, Gc, X, Y, Items), cookie)
	return PolyText16Cookie{cookie}
}

// PolyText16Checked sends a checked request.
// If an error occurs, it can be retrieved using PolyText16Cookie.Check()
func PolyText16Checked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) PolyText16Cookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyText16Request(c, Drawable, Gc, X, Y, Items), cookie)
	return PolyText16Cookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyText16Cookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyText16
// polyText16Request writes a PolyText16 request to a byte slice.
func polyText16Request(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) []byte {
	size := xgb.Pad((16 + xgb.Pad((len(Items) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 75 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	copy(buf[b:], Items[:len(Items)])
	b += int(len(Items))

	return buf
}

// PolyText8Cookie is a cookie used only for PolyText8 requests.
type PolyText8Cookie struct {
	*xgb.Cookie
}

// PolyText8 sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PolyText8(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) PolyText8Cookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(polyText8Request(c, Drawable, Gc, X, Y, Items), cookie)
	return PolyText8Cookie{cookie}
}

// PolyText8Checked sends a checked request.
// If an error occurs, it can be retrieved using PolyText8Cookie.Check()
func PolyText8Checked(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) PolyText8Cookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(polyText8Request(c, Drawable, Gc, X, Y, Items), cookie)
	return PolyText8Cookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PolyText8Cookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PolyText8
// polyText8Request writes a PolyText8 request to a byte slice.
func polyText8Request(c *xgb.Conn, Drawable Drawable, Gc Gcontext, X int16, Y int16, Items []byte) []byte {
	size := xgb.Pad((16 + xgb.Pad((len(Items) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 74 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	copy(buf[b:], Items[:len(Items)])
	b += int(len(Items))

	return buf
}

// PutImageCookie is a cookie used only for PutImage requests.
type PutImageCookie struct {
	*xgb.Cookie
}

// PutImage sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func PutImage(c *xgb.Conn, Format byte, Drawable Drawable, Gc Gcontext, Width uint16, Height uint16, DstX int16, DstY int16, LeftPad byte, Depth byte, Data []byte) PutImageCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(putImageRequest(c, Format, Drawable, Gc, Width, Height, DstX, DstY, LeftPad, Depth, Data), cookie)
	return PutImageCookie{cookie}
}

// PutImageChecked sends a checked request.
// If an error occurs, it can be retrieved using PutImageCookie.Check()
func PutImageChecked(c *xgb.Conn, Format byte, Drawable Drawable, Gc Gcontext, Width uint16, Height uint16, DstX int16, DstY int16, LeftPad byte, Depth byte, Data []byte) PutImageCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(putImageRequest(c, Format, Drawable, Gc, Width, Height, DstX, DstY, LeftPad, Depth, Data), cookie)
	return PutImageCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook PutImageCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for PutImage
// putImageRequest writes a PutImage request to a byte slice.
func putImageRequest(c *xgb.Conn, Format byte, Drawable Drawable, Gc Gcontext, Width uint16, Height uint16, DstX int16, DstY int16, LeftPad byte, Depth byte, Data []byte) []byte {
	size := xgb.Pad((24 + xgb.Pad((len(Data) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 72 // request opcode
	b += 1

	buf[b] = Format
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	xgb.Put16(buf[b:], uint16(DstX))
	b += 2

	xgb.Put16(buf[b:], uint16(DstY))
	b += 2

	buf[b] = LeftPad
	b += 1

	buf[b] = Depth
	b += 1

	b += 2 // padding

	copy(buf[b:], Data[:len(Data)])
	b += int(len(Data))

	return buf
}

// QueryBestSizeCookie is a cookie used only for QueryBestSize requests.
type QueryBestSizeCookie struct {
	*xgb.Cookie
}

// QueryBestSize sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryBestSizeCookie.Reply()
func QueryBestSize(c *xgb.Conn, Class byte, Drawable Drawable, Width uint16, Height uint16) QueryBestSizeCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryBestSizeRequest(c, Class, Drawable, Width, Height), cookie)
	return QueryBestSizeCookie{cookie}
}

// QueryBestSizeUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryBestSizeUnchecked(c *xgb.Conn, Class byte, Drawable Drawable, Width uint16, Height uint16) QueryBestSizeCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryBestSizeRequest(c, Class, Drawable, Width, Height), cookie)
	return QueryBestSizeCookie{cookie}
}

// QueryBestSizeReply represents the data returned from a QueryBestSize request.
type QueryBestSizeReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Width  uint16
	Height uint16
}

// Reply blocks and returns the reply data for a QueryBestSize request.
func (cook QueryBestSizeCookie) Reply() (*QueryBestSizeReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryBestSizeReply(buf), nil
}

// queryBestSizeReply reads a byte slice into a QueryBestSizeReply value.
func queryBestSizeReply(buf []byte) *QueryBestSizeReply {
	v := new(QueryBestSizeReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Width = xgb.Get16(buf[b:])
	b += 2

	v.Height = xgb.Get16(buf[b:])
	b += 2

	return v
}

// Write request to wire for QueryBestSize
// queryBestSizeRequest writes a QueryBestSize request to a byte slice.
func queryBestSizeRequest(c *xgb.Conn, Class byte, Drawable Drawable, Width uint16, Height uint16) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 97 // request opcode
	b += 1

	buf[b] = Class
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Drawable))
	b += 4

	xgb.Put16(buf[b:], Width)
	b += 2

	xgb.Put16(buf[b:], Height)
	b += 2

	return buf
}

// QueryColorsCookie is a cookie used only for QueryColors requests.
type QueryColorsCookie struct {
	*xgb.Cookie
}

// QueryColors sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryColorsCookie.Reply()
func QueryColors(c *xgb.Conn, Cmap Colormap, Pixels []uint32) QueryColorsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryColorsRequest(c, Cmap, Pixels), cookie)
	return QueryColorsCookie{cookie}
}

// QueryColorsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryColorsUnchecked(c *xgb.Conn, Cmap Colormap, Pixels []uint32) QueryColorsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryColorsRequest(c, Cmap, Pixels), cookie)
	return QueryColorsCookie{cookie}
}

// QueryColorsReply represents the data returned from a QueryColors request.
type QueryColorsReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	ColorsLen uint16
	// padding: 22 bytes
	Colors []Rgb // size: xgb.Pad((int(ColorsLen) * 8))
}

// Reply blocks and returns the reply data for a QueryColors request.
func (cook QueryColorsCookie) Reply() (*QueryColorsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryColorsReply(buf), nil
}

// queryColorsReply reads a byte slice into a QueryColorsReply value.
func queryColorsReply(buf []byte) *QueryColorsReply {
	v := new(QueryColorsReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.ColorsLen = xgb.Get16(buf[b:])
	b += 2

	b += 22 // padding

	v.Colors = make([]Rgb, v.ColorsLen)
	b += RgbReadList(buf[b:], v.Colors)

	return v
}

// Write request to wire for QueryColors
// queryColorsRequest writes a QueryColors request to a byte slice.
func queryColorsRequest(c *xgb.Conn, Cmap Colormap, Pixels []uint32) []byte {
	size := xgb.Pad((8 + xgb.Pad((len(Pixels) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 91 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	for i := 0; i < int(len(Pixels)); i++ {
		xgb.Put32(buf[b:], Pixels[i])
		b += 4
	}

	return buf
}

// QueryExtensionCookie is a cookie used only for QueryExtension requests.
type QueryExtensionCookie struct {
	*xgb.Cookie
}

// QueryExtension sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryExtensionCookie.Reply()
func QueryExtension(c *xgb.Conn, NameLen uint16, Name string) QueryExtensionCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryExtensionRequest(c, NameLen, Name), cookie)
	return QueryExtensionCookie{cookie}
}

// QueryExtensionUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryExtensionUnchecked(c *xgb.Conn, NameLen uint16, Name string) QueryExtensionCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryExtensionRequest(c, NameLen, Name), cookie)
	return QueryExtensionCookie{cookie}
}

// QueryExtensionReply represents the data returned from a QueryExtension request.
type QueryExtensionReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Present     bool
	MajorOpcode byte
	FirstEvent  byte
	FirstError  byte
}

// Reply blocks and returns the reply data for a QueryExtension request.
func (cook QueryExtensionCookie) Reply() (*QueryExtensionReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryExtensionReply(buf), nil
}

// queryExtensionReply reads a byte slice into a QueryExtensionReply value.
func queryExtensionReply(buf []byte) *QueryExtensionReply {
	v := new(QueryExtensionReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	if buf[b] == 1 {
		v.Present = true
	} else {
		v.Present = false
	}
	b += 1

	v.MajorOpcode = buf[b]
	b += 1

	v.FirstEvent = buf[b]
	b += 1

	v.FirstError = buf[b]
	b += 1

	return v
}

// Write request to wire for QueryExtension
// queryExtensionRequest writes a QueryExtension request to a byte slice.
func queryExtensionRequest(c *xgb.Conn, NameLen uint16, Name string) []byte {
	size := xgb.Pad((8 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 98 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// QueryFontCookie is a cookie used only for QueryFont requests.
type QueryFontCookie struct {
	*xgb.Cookie
}

// QueryFont sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryFontCookie.Reply()
func QueryFont(c *xgb.Conn, Font Fontable) QueryFontCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryFontRequest(c, Font), cookie)
	return QueryFontCookie{cookie}
}

// QueryFontUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryFontUnchecked(c *xgb.Conn, Font Fontable) QueryFontCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryFontRequest(c, Font), cookie)
	return QueryFontCookie{cookie}
}

// QueryFontReply represents the data returned from a QueryFont request.
type QueryFontReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	MinBounds Charinfo
	// padding: 4 bytes
	MaxBounds Charinfo
	// padding: 4 bytes
	MinCharOrByte2 uint16
	MaxCharOrByte2 uint16
	DefaultChar    uint16
	PropertiesLen  uint16
	DrawDirection  byte
	MinByte1       byte
	MaxByte1       byte
	AllCharsExist  bool
	FontAscent     int16
	FontDescent    int16
	CharInfosLen   uint32
	Properties     []Fontprop // size: xgb.Pad((int(PropertiesLen) * 8))
	CharInfos      []Charinfo // size: xgb.Pad((int(CharInfosLen) * 12))
}

// Reply blocks and returns the reply data for a QueryFont request.
func (cook QueryFontCookie) Reply() (*QueryFontReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryFontReply(buf), nil
}

// queryFontReply reads a byte slice into a QueryFontReply value.
func queryFontReply(buf []byte) *QueryFontReply {
	v := new(QueryFontReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.MinBounds = Charinfo{}
	b += CharinfoRead(buf[b:], &v.MinBounds)

	b += 4 // padding

	v.MaxBounds = Charinfo{}
	b += CharinfoRead(buf[b:], &v.MaxBounds)

	b += 4 // padding

	v.MinCharOrByte2 = xgb.Get16(buf[b:])
	b += 2

	v.MaxCharOrByte2 = xgb.Get16(buf[b:])
	b += 2

	v.DefaultChar = xgb.Get16(buf[b:])
	b += 2

	v.PropertiesLen = xgb.Get16(buf[b:])
	b += 2

	v.DrawDirection = buf[b]
	b += 1

	v.MinByte1 = buf[b]
	b += 1

	v.MaxByte1 = buf[b]
	b += 1

	if buf[b] == 1 {
		v.AllCharsExist = true
	} else {
		v.AllCharsExist = false
	}
	b += 1

	v.FontAscent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.FontDescent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.CharInfosLen = xgb.Get32(buf[b:])
	b += 4

	v.Properties = make([]Fontprop, v.PropertiesLen)
	b += FontpropReadList(buf[b:], v.Properties)

	v.CharInfos = make([]Charinfo, v.CharInfosLen)
	b += CharinfoReadList(buf[b:], v.CharInfos)

	return v
}

// Write request to wire for QueryFont
// queryFontRequest writes a QueryFont request to a byte slice.
func queryFontRequest(c *xgb.Conn, Font Fontable) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 47 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Font))
	b += 4

	return buf
}

// QueryKeymapCookie is a cookie used only for QueryKeymap requests.
type QueryKeymapCookie struct {
	*xgb.Cookie
}

// QueryKeymap sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryKeymapCookie.Reply()
func QueryKeymap(c *xgb.Conn) QueryKeymapCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryKeymapRequest(c), cookie)
	return QueryKeymapCookie{cookie}
}

// QueryKeymapUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryKeymapUnchecked(c *xgb.Conn) QueryKeymapCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryKeymapRequest(c), cookie)
	return QueryKeymapCookie{cookie}
}

// QueryKeymapReply represents the data returned from a QueryKeymap request.
type QueryKeymapReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Keys []byte // size: 32
}

// Reply blocks and returns the reply data for a QueryKeymap request.
func (cook QueryKeymapCookie) Reply() (*QueryKeymapReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryKeymapReply(buf), nil
}

// queryKeymapReply reads a byte slice into a QueryKeymapReply value.
func queryKeymapReply(buf []byte) *QueryKeymapReply {
	v := new(QueryKeymapReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Keys = make([]byte, 32)
	copy(v.Keys[:32], buf[b:])
	b += int(32)

	return v
}

// Write request to wire for QueryKeymap
// queryKeymapRequest writes a QueryKeymap request to a byte slice.
func queryKeymapRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 44 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// QueryPointerCookie is a cookie used only for QueryPointer requests.
type QueryPointerCookie struct {
	*xgb.Cookie
}

// QueryPointer sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryPointerCookie.Reply()
func QueryPointer(c *xgb.Conn, Window Window) QueryPointerCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryPointerRequest(c, Window), cookie)
	return QueryPointerCookie{cookie}
}

// QueryPointerUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryPointerUnchecked(c *xgb.Conn, Window Window) QueryPointerCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryPointerRequest(c, Window), cookie)
	return QueryPointerCookie{cookie}
}

// QueryPointerReply represents the data returned from a QueryPointer request.
type QueryPointerReply struct {
	Sequence   uint16 // sequence number of the request for this reply
	Length     uint32 // number of bytes in this reply
	SameScreen bool
	Root       Window
	Child      Window
	RootX      int16
	RootY      int16
	WinX       int16
	WinY       int16
	Mask       uint16
	// padding: 2 bytes
}

// Reply blocks and returns the reply data for a QueryPointer request.
func (cook QueryPointerCookie) Reply() (*QueryPointerReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryPointerReply(buf), nil
}

// queryPointerReply reads a byte slice into a QueryPointerReply value.
func queryPointerReply(buf []byte) *QueryPointerReply {
	v := new(QueryPointerReply)
	b := 1 // skip reply determinant

	if buf[b] == 1 {
		v.SameScreen = true
	} else {
		v.SameScreen = false
	}
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.RootX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.RootY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.WinX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.WinY = int16(xgb.Get16(buf[b:]))
	b += 2

	v.Mask = xgb.Get16(buf[b:])
	b += 2

	b += 2 // padding

	return v
}

// Write request to wire for QueryPointer
// queryPointerRequest writes a QueryPointer request to a byte slice.
func queryPointerRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 38 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// QueryTextExtentsCookie is a cookie used only for QueryTextExtents requests.
type QueryTextExtentsCookie struct {
	*xgb.Cookie
}

// QueryTextExtents sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryTextExtentsCookie.Reply()
func QueryTextExtents(c *xgb.Conn, Font Fontable, String []Char2b, StringLen uint16) QueryTextExtentsCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryTextExtentsRequest(c, Font, String, StringLen), cookie)
	return QueryTextExtentsCookie{cookie}
}

// QueryTextExtentsUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryTextExtentsUnchecked(c *xgb.Conn, Font Fontable, String []Char2b, StringLen uint16) QueryTextExtentsCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryTextExtentsRequest(c, Font, String, StringLen), cookie)
	return QueryTextExtentsCookie{cookie}
}

// QueryTextExtentsReply represents the data returned from a QueryTextExtents request.
type QueryTextExtentsReply struct {
	Sequence       uint16 // sequence number of the request for this reply
	Length         uint32 // number of bytes in this reply
	DrawDirection  byte
	FontAscent     int16
	FontDescent    int16
	OverallAscent  int16
	OverallDescent int16
	OverallWidth   int32
	OverallLeft    int32
	OverallRight   int32
}

// Reply blocks and returns the reply data for a QueryTextExtents request.
func (cook QueryTextExtentsCookie) Reply() (*QueryTextExtentsReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryTextExtentsReply(buf), nil
}

// queryTextExtentsReply reads a byte slice into a QueryTextExtentsReply value.
func queryTextExtentsReply(buf []byte) *QueryTextExtentsReply {
	v := new(QueryTextExtentsReply)
	b := 1 // skip reply determinant

	v.DrawDirection = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.FontAscent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.FontDescent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.OverallAscent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.OverallDescent = int16(xgb.Get16(buf[b:]))
	b += 2

	v.OverallWidth = int32(xgb.Get32(buf[b:]))
	b += 4

	v.OverallLeft = int32(xgb.Get32(buf[b:]))
	b += 4

	v.OverallRight = int32(xgb.Get32(buf[b:]))
	b += 4

	return v
}

// Write request to wire for QueryTextExtents
// queryTextExtentsRequest writes a QueryTextExtents request to a byte slice.
func queryTextExtentsRequest(c *xgb.Conn, Font Fontable, String []Char2b, StringLen uint16) []byte {
	size := xgb.Pad((8 + xgb.Pad((len(String) * 2))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 48 // request opcode
	b += 1

	buf[b] = byte((int(StringLen) & 1))
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Font))
	b += 4

	b += Char2bListBytes(buf[b:], String)

	// skip writing local field: StringLen (2) :: uint16

	return buf
}

// QueryTreeCookie is a cookie used only for QueryTree requests.
type QueryTreeCookie struct {
	*xgb.Cookie
}

// QueryTree sends a checked request.
// If an error occurs, it will be returned with the reply by calling QueryTreeCookie.Reply()
func QueryTree(c *xgb.Conn, Window Window) QueryTreeCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(queryTreeRequest(c, Window), cookie)
	return QueryTreeCookie{cookie}
}

// QueryTreeUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func QueryTreeUnchecked(c *xgb.Conn, Window Window) QueryTreeCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(queryTreeRequest(c, Window), cookie)
	return QueryTreeCookie{cookie}
}

// QueryTreeReply represents the data returned from a QueryTree request.
type QueryTreeReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	// padding: 1 bytes
	Root        Window
	Parent      Window
	ChildrenLen uint16
	// padding: 14 bytes
	Children []Window // size: xgb.Pad((int(ChildrenLen) * 4))
}

// Reply blocks and returns the reply data for a QueryTree request.
func (cook QueryTreeCookie) Reply() (*QueryTreeReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return queryTreeReply(buf), nil
}

// queryTreeReply reads a byte slice into a QueryTreeReply value.
func queryTreeReply(buf []byte) *QueryTreeReply {
	v := new(QueryTreeReply)
	b := 1 // skip reply determinant

	b += 1 // padding

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Root = Window(xgb.Get32(buf[b:]))
	b += 4

	v.Parent = Window(xgb.Get32(buf[b:]))
	b += 4

	v.ChildrenLen = xgb.Get16(buf[b:])
	b += 2

	b += 14 // padding

	v.Children = make([]Window, v.ChildrenLen)
	for i := 0; i < int(v.ChildrenLen); i++ {
		v.Children[i] = Window(xgb.Get32(buf[b:]))
		b += 4
	}

	return v
}

// Write request to wire for QueryTree
// queryTreeRequest writes a QueryTree request to a byte slice.
func queryTreeRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 15 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// RecolorCursorCookie is a cookie used only for RecolorCursor requests.
type RecolorCursorCookie struct {
	*xgb.Cookie
}

// RecolorCursor sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func RecolorCursor(c *xgb.Conn, Cursor Cursor, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) RecolorCursorCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(recolorCursorRequest(c, Cursor, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue), cookie)
	return RecolorCursorCookie{cookie}
}

// RecolorCursorChecked sends a checked request.
// If an error occurs, it can be retrieved using RecolorCursorCookie.Check()
func RecolorCursorChecked(c *xgb.Conn, Cursor Cursor, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) RecolorCursorCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(recolorCursorRequest(c, Cursor, ForeRed, ForeGreen, ForeBlue, BackRed, BackGreen, BackBlue), cookie)
	return RecolorCursorCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook RecolorCursorCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for RecolorCursor
// recolorCursorRequest writes a RecolorCursor request to a byte slice.
func recolorCursorRequest(c *xgb.Conn, Cursor Cursor, ForeRed uint16, ForeGreen uint16, ForeBlue uint16, BackRed uint16, BackGreen uint16, BackBlue uint16) []byte {
	size := 20
	b := 0
	buf := make([]byte, size)

	buf[b] = 96 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cursor))
	b += 4

	xgb.Put16(buf[b:], ForeRed)
	b += 2

	xgb.Put16(buf[b:], ForeGreen)
	b += 2

	xgb.Put16(buf[b:], ForeBlue)
	b += 2

	xgb.Put16(buf[b:], BackRed)
	b += 2

	xgb.Put16(buf[b:], BackGreen)
	b += 2

	xgb.Put16(buf[b:], BackBlue)
	b += 2

	return buf
}

// ReparentWindowCookie is a cookie used only for ReparentWindow requests.
type ReparentWindowCookie struct {
	*xgb.Cookie
}

// ReparentWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func ReparentWindow(c *xgb.Conn, Window Window, Parent Window, X int16, Y int16) ReparentWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(reparentWindowRequest(c, Window, Parent, X, Y), cookie)
	return ReparentWindowCookie{cookie}
}

// ReparentWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using ReparentWindowCookie.Check()
func ReparentWindowChecked(c *xgb.Conn, Window Window, Parent Window, X int16, Y int16) ReparentWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(reparentWindowRequest(c, Window, Parent, X, Y), cookie)
	return ReparentWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook ReparentWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for ReparentWindow
// reparentWindowRequest writes a ReparentWindow request to a byte slice.
func reparentWindowRequest(c *xgb.Conn, Window Window, Parent Window, X int16, Y int16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 7 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put32(buf[b:], uint32(Parent))
	b += 4

	xgb.Put16(buf[b:], uint16(X))
	b += 2

	xgb.Put16(buf[b:], uint16(Y))
	b += 2

	return buf
}

// RotatePropertiesCookie is a cookie used only for RotateProperties requests.
type RotatePropertiesCookie struct {
	*xgb.Cookie
}

// RotateProperties sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func RotateProperties(c *xgb.Conn, Window Window, AtomsLen uint16, Delta int16, Atoms []Atom) RotatePropertiesCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(rotatePropertiesRequest(c, Window, AtomsLen, Delta, Atoms), cookie)
	return RotatePropertiesCookie{cookie}
}

// RotatePropertiesChecked sends a checked request.
// If an error occurs, it can be retrieved using RotatePropertiesCookie.Check()
func RotatePropertiesChecked(c *xgb.Conn, Window Window, AtomsLen uint16, Delta int16, Atoms []Atom) RotatePropertiesCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(rotatePropertiesRequest(c, Window, AtomsLen, Delta, Atoms), cookie)
	return RotatePropertiesCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook RotatePropertiesCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for RotateProperties
// rotatePropertiesRequest writes a RotateProperties request to a byte slice.
func rotatePropertiesRequest(c *xgb.Conn, Window Window, AtomsLen uint16, Delta int16, Atoms []Atom) []byte {
	size := xgb.Pad((12 + xgb.Pad((int(AtomsLen) * 4))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 114 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	xgb.Put16(buf[b:], AtomsLen)
	b += 2

	xgb.Put16(buf[b:], uint16(Delta))
	b += 2

	for i := 0; i < int(AtomsLen); i++ {
		xgb.Put32(buf[b:], uint32(Atoms[i]))
		b += 4
	}

	return buf
}

// SendEventCookie is a cookie used only for SendEvent requests.
type SendEventCookie struct {
	*xgb.Cookie
}

// SendEvent sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SendEvent(c *xgb.Conn, Propagate bool, Destination Window, EventMask uint32, Event string) SendEventCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(sendEventRequest(c, Propagate, Destination, EventMask, Event), cookie)
	return SendEventCookie{cookie}
}

// SendEventChecked sends a checked request.
// If an error occurs, it can be retrieved using SendEventCookie.Check()
func SendEventChecked(c *xgb.Conn, Propagate bool, Destination Window, EventMask uint32, Event string) SendEventCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(sendEventRequest(c, Propagate, Destination, EventMask, Event), cookie)
	return SendEventCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SendEventCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SendEvent
// sendEventRequest writes a SendEvent request to a byte slice.
func sendEventRequest(c *xgb.Conn, Propagate bool, Destination Window, EventMask uint32, Event string) []byte {
	size := 44
	b := 0
	buf := make([]byte, size)

	buf[b] = 25 // request opcode
	b += 1

	if Propagate {
		buf[b] = 1
	} else {
		buf[b] = 0
	}
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Destination))
	b += 4

	xgb.Put32(buf[b:], EventMask)
	b += 4

	copy(buf[b:], Event[:32])
	b += int(32)

	return buf
}

// SetAccessControlCookie is a cookie used only for SetAccessControl requests.
type SetAccessControlCookie struct {
	*xgb.Cookie
}

// SetAccessControl sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetAccessControl(c *xgb.Conn, Mode byte) SetAccessControlCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setAccessControlRequest(c, Mode), cookie)
	return SetAccessControlCookie{cookie}
}

// SetAccessControlChecked sends a checked request.
// If an error occurs, it can be retrieved using SetAccessControlCookie.Check()
func SetAccessControlChecked(c *xgb.Conn, Mode byte) SetAccessControlCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setAccessControlRequest(c, Mode), cookie)
	return SetAccessControlCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetAccessControlCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetAccessControl
// setAccessControlRequest writes a SetAccessControl request to a byte slice.
func setAccessControlRequest(c *xgb.Conn, Mode byte) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 111 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// SetClipRectanglesCookie is a cookie used only for SetClipRectangles requests.
type SetClipRectanglesCookie struct {
	*xgb.Cookie
}

// SetClipRectangles sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetClipRectangles(c *xgb.Conn, Ordering byte, Gc Gcontext, ClipXOrigin int16, ClipYOrigin int16, Rectangles []Rectangle) SetClipRectanglesCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setClipRectanglesRequest(c, Ordering, Gc, ClipXOrigin, ClipYOrigin, Rectangles), cookie)
	return SetClipRectanglesCookie{cookie}
}

// SetClipRectanglesChecked sends a checked request.
// If an error occurs, it can be retrieved using SetClipRectanglesCookie.Check()
func SetClipRectanglesChecked(c *xgb.Conn, Ordering byte, Gc Gcontext, ClipXOrigin int16, ClipYOrigin int16, Rectangles []Rectangle) SetClipRectanglesCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setClipRectanglesRequest(c, Ordering, Gc, ClipXOrigin, ClipYOrigin, Rectangles), cookie)
	return SetClipRectanglesCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetClipRectanglesCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetClipRectangles
// setClipRectanglesRequest writes a SetClipRectangles request to a byte slice.
func setClipRectanglesRequest(c *xgb.Conn, Ordering byte, Gc Gcontext, ClipXOrigin int16, ClipYOrigin int16, Rectangles []Rectangle) []byte {
	size := xgb.Pad((12 + xgb.Pad((len(Rectangles) * 8))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 59 // request opcode
	b += 1

	buf[b] = Ordering
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], uint16(ClipXOrigin))
	b += 2

	xgb.Put16(buf[b:], uint16(ClipYOrigin))
	b += 2

	b += RectangleListBytes(buf[b:], Rectangles)

	return buf
}

// SetCloseDownModeCookie is a cookie used only for SetCloseDownMode requests.
type SetCloseDownModeCookie struct {
	*xgb.Cookie
}

// SetCloseDownMode sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetCloseDownMode(c *xgb.Conn, Mode byte) SetCloseDownModeCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setCloseDownModeRequest(c, Mode), cookie)
	return SetCloseDownModeCookie{cookie}
}

// SetCloseDownModeChecked sends a checked request.
// If an error occurs, it can be retrieved using SetCloseDownModeCookie.Check()
func SetCloseDownModeChecked(c *xgb.Conn, Mode byte) SetCloseDownModeCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setCloseDownModeRequest(c, Mode), cookie)
	return SetCloseDownModeCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetCloseDownModeCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetCloseDownMode
// setCloseDownModeRequest writes a SetCloseDownMode request to a byte slice.
func setCloseDownModeRequest(c *xgb.Conn, Mode byte) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 112 // request opcode
	b += 1

	buf[b] = Mode
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// SetDashesCookie is a cookie used only for SetDashes requests.
type SetDashesCookie struct {
	*xgb.Cookie
}

// SetDashes sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetDashes(c *xgb.Conn, Gc Gcontext, DashOffset uint16, DashesLen uint16, Dashes []byte) SetDashesCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setDashesRequest(c, Gc, DashOffset, DashesLen, Dashes), cookie)
	return SetDashesCookie{cookie}
}

// SetDashesChecked sends a checked request.
// If an error occurs, it can be retrieved using SetDashesCookie.Check()
func SetDashesChecked(c *xgb.Conn, Gc Gcontext, DashOffset uint16, DashesLen uint16, Dashes []byte) SetDashesCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setDashesRequest(c, Gc, DashOffset, DashesLen, Dashes), cookie)
	return SetDashesCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetDashesCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetDashes
// setDashesRequest writes a SetDashes request to a byte slice.
func setDashesRequest(c *xgb.Conn, Gc Gcontext, DashOffset uint16, DashesLen uint16, Dashes []byte) []byte {
	size := xgb.Pad((12 + xgb.Pad((int(DashesLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 58 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Gc))
	b += 4

	xgb.Put16(buf[b:], DashOffset)
	b += 2

	xgb.Put16(buf[b:], DashesLen)
	b += 2

	copy(buf[b:], Dashes[:DashesLen])
	b += int(DashesLen)

	return buf
}

// SetFontPathCookie is a cookie used only for SetFontPath requests.
type SetFontPathCookie struct {
	*xgb.Cookie
}

// SetFontPath sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetFontPath(c *xgb.Conn, FontQty uint16, Font []Str) SetFontPathCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setFontPathRequest(c, FontQty, Font), cookie)
	return SetFontPathCookie{cookie}
}

// SetFontPathChecked sends a checked request.
// If an error occurs, it can be retrieved using SetFontPathCookie.Check()
func SetFontPathChecked(c *xgb.Conn, FontQty uint16, Font []Str) SetFontPathCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setFontPathRequest(c, FontQty, Font), cookie)
	return SetFontPathCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetFontPathCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetFontPath
// setFontPathRequest writes a SetFontPath request to a byte slice.
func setFontPathRequest(c *xgb.Conn, FontQty uint16, Font []Str) []byte {
	size := xgb.Pad((8 + StrListSize(Font)))
	b := 0
	buf := make([]byte, size)

	buf[b] = 51 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], FontQty)
	b += 2

	b += 2 // padding

	b += StrListBytes(buf[b:], Font)

	return buf
}

// SetInputFocusCookie is a cookie used only for SetInputFocus requests.
type SetInputFocusCookie struct {
	*xgb.Cookie
}

// SetInputFocus sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetInputFocus(c *xgb.Conn, RevertTo byte, Focus Window, Time Timestamp) SetInputFocusCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setInputFocusRequest(c, RevertTo, Focus, Time), cookie)
	return SetInputFocusCookie{cookie}
}

// SetInputFocusChecked sends a checked request.
// If an error occurs, it can be retrieved using SetInputFocusCookie.Check()
func SetInputFocusChecked(c *xgb.Conn, RevertTo byte, Focus Window, Time Timestamp) SetInputFocusCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setInputFocusRequest(c, RevertTo, Focus, Time), cookie)
	return SetInputFocusCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetInputFocusCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetInputFocus
// setInputFocusRequest writes a SetInputFocus request to a byte slice.
func setInputFocusRequest(c *xgb.Conn, RevertTo byte, Focus Window, Time Timestamp) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 42 // request opcode
	b += 1

	buf[b] = RevertTo
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Focus))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// SetModifierMappingCookie is a cookie used only for SetModifierMapping requests.
type SetModifierMappingCookie struct {
	*xgb.Cookie
}

// SetModifierMapping sends a checked request.
// If an error occurs, it will be returned with the reply by calling SetModifierMappingCookie.Reply()
func SetModifierMapping(c *xgb.Conn, KeycodesPerModifier byte, Keycodes []Keycode) SetModifierMappingCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(setModifierMappingRequest(c, KeycodesPerModifier, Keycodes), cookie)
	return SetModifierMappingCookie{cookie}
}

// SetModifierMappingUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetModifierMappingUnchecked(c *xgb.Conn, KeycodesPerModifier byte, Keycodes []Keycode) SetModifierMappingCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(setModifierMappingRequest(c, KeycodesPerModifier, Keycodes), cookie)
	return SetModifierMappingCookie{cookie}
}

// SetModifierMappingReply represents the data returned from a SetModifierMapping request.
type SetModifierMappingReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Status   byte
}

// Reply blocks and returns the reply data for a SetModifierMapping request.
func (cook SetModifierMappingCookie) Reply() (*SetModifierMappingReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return setModifierMappingReply(buf), nil
}

// setModifierMappingReply reads a byte slice into a SetModifierMappingReply value.
func setModifierMappingReply(buf []byte) *SetModifierMappingReply {
	v := new(SetModifierMappingReply)
	b := 1 // skip reply determinant

	v.Status = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	return v
}

// Write request to wire for SetModifierMapping
// setModifierMappingRequest writes a SetModifierMapping request to a byte slice.
func setModifierMappingRequest(c *xgb.Conn, KeycodesPerModifier byte, Keycodes []Keycode) []byte {
	size := xgb.Pad((4 + xgb.Pad(((int(KeycodesPerModifier) * 8) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 118 // request opcode
	b += 1

	buf[b] = KeycodesPerModifier
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	for i := 0; i < int((int(KeycodesPerModifier) * 8)); i++ {
		buf[b] = byte(Keycodes[i])
		b += 1
	}

	return buf
}

// SetPointerMappingCookie is a cookie used only for SetPointerMapping requests.
type SetPointerMappingCookie struct {
	*xgb.Cookie
}

// SetPointerMapping sends a checked request.
// If an error occurs, it will be returned with the reply by calling SetPointerMappingCookie.Reply()
func SetPointerMapping(c *xgb.Conn, MapLen byte, Map []byte) SetPointerMappingCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(setPointerMappingRequest(c, MapLen, Map), cookie)
	return SetPointerMappingCookie{cookie}
}

// SetPointerMappingUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetPointerMappingUnchecked(c *xgb.Conn, MapLen byte, Map []byte) SetPointerMappingCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(setPointerMappingRequest(c, MapLen, Map), cookie)
	return SetPointerMappingCookie{cookie}
}

// SetPointerMappingReply represents the data returned from a SetPointerMapping request.
type SetPointerMappingReply struct {
	Sequence uint16 // sequence number of the request for this reply
	Length   uint32 // number of bytes in this reply
	Status   byte
}

// Reply blocks and returns the reply data for a SetPointerMapping request.
func (cook SetPointerMappingCookie) Reply() (*SetPointerMappingReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return setPointerMappingReply(buf), nil
}

// setPointerMappingReply reads a byte slice into a SetPointerMappingReply value.
func setPointerMappingReply(buf []byte) *SetPointerMappingReply {
	v := new(SetPointerMappingReply)
	b := 1 // skip reply determinant

	v.Status = buf[b]
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	return v
}

// Write request to wire for SetPointerMapping
// setPointerMappingRequest writes a SetPointerMapping request to a byte slice.
func setPointerMappingRequest(c *xgb.Conn, MapLen byte, Map []byte) []byte {
	size := xgb.Pad((4 + xgb.Pad((int(MapLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 116 // request opcode
	b += 1

	buf[b] = MapLen
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	copy(buf[b:], Map[:MapLen])
	b += int(MapLen)

	return buf
}

// SetScreenSaverCookie is a cookie used only for SetScreenSaver requests.
type SetScreenSaverCookie struct {
	*xgb.Cookie
}

// SetScreenSaver sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetScreenSaver(c *xgb.Conn, Timeout int16, Interval int16, PreferBlanking byte, AllowExposures byte) SetScreenSaverCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setScreenSaverRequest(c, Timeout, Interval, PreferBlanking, AllowExposures), cookie)
	return SetScreenSaverCookie{cookie}
}

// SetScreenSaverChecked sends a checked request.
// If an error occurs, it can be retrieved using SetScreenSaverCookie.Check()
func SetScreenSaverChecked(c *xgb.Conn, Timeout int16, Interval int16, PreferBlanking byte, AllowExposures byte) SetScreenSaverCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setScreenSaverRequest(c, Timeout, Interval, PreferBlanking, AllowExposures), cookie)
	return SetScreenSaverCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetScreenSaverCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetScreenSaver
// setScreenSaverRequest writes a SetScreenSaver request to a byte slice.
func setScreenSaverRequest(c *xgb.Conn, Timeout int16, Interval int16, PreferBlanking byte, AllowExposures byte) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 107 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put16(buf[b:], uint16(Timeout))
	b += 2

	xgb.Put16(buf[b:], uint16(Interval))
	b += 2

	buf[b] = PreferBlanking
	b += 1

	buf[b] = AllowExposures
	b += 1

	return buf
}

// SetSelectionOwnerCookie is a cookie used only for SetSelectionOwner requests.
type SetSelectionOwnerCookie struct {
	*xgb.Cookie
}

// SetSelectionOwner sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func SetSelectionOwner(c *xgb.Conn, Owner Window, Selection Atom, Time Timestamp) SetSelectionOwnerCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(setSelectionOwnerRequest(c, Owner, Selection, Time), cookie)
	return SetSelectionOwnerCookie{cookie}
}

// SetSelectionOwnerChecked sends a checked request.
// If an error occurs, it can be retrieved using SetSelectionOwnerCookie.Check()
func SetSelectionOwnerChecked(c *xgb.Conn, Owner Window, Selection Atom, Time Timestamp) SetSelectionOwnerCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(setSelectionOwnerRequest(c, Owner, Selection, Time), cookie)
	return SetSelectionOwnerCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook SetSelectionOwnerCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for SetSelectionOwner
// setSelectionOwnerRequest writes a SetSelectionOwner request to a byte slice.
func setSelectionOwnerRequest(c *xgb.Conn, Owner Window, Selection Atom, Time Timestamp) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 22 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Owner))
	b += 4

	xgb.Put32(buf[b:], uint32(Selection))
	b += 4

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// StoreColorsCookie is a cookie used only for StoreColors requests.
type StoreColorsCookie struct {
	*xgb.Cookie
}

// StoreColors sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func StoreColors(c *xgb.Conn, Cmap Colormap, Items []Coloritem) StoreColorsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(storeColorsRequest(c, Cmap, Items), cookie)
	return StoreColorsCookie{cookie}
}

// StoreColorsChecked sends a checked request.
// If an error occurs, it can be retrieved using StoreColorsCookie.Check()
func StoreColorsChecked(c *xgb.Conn, Cmap Colormap, Items []Coloritem) StoreColorsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(storeColorsRequest(c, Cmap, Items), cookie)
	return StoreColorsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook StoreColorsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for StoreColors
// storeColorsRequest writes a StoreColors request to a byte slice.
func storeColorsRequest(c *xgb.Conn, Cmap Colormap, Items []Coloritem) []byte {
	size := xgb.Pad((8 + xgb.Pad((len(Items) * 12))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 89 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	b += ColoritemListBytes(buf[b:], Items)

	return buf
}

// StoreNamedColorCookie is a cookie used only for StoreNamedColor requests.
type StoreNamedColorCookie struct {
	*xgb.Cookie
}

// StoreNamedColor sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func StoreNamedColor(c *xgb.Conn, Flags byte, Cmap Colormap, Pixel uint32, NameLen uint16, Name string) StoreNamedColorCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(storeNamedColorRequest(c, Flags, Cmap, Pixel, NameLen, Name), cookie)
	return StoreNamedColorCookie{cookie}
}

// StoreNamedColorChecked sends a checked request.
// If an error occurs, it can be retrieved using StoreNamedColorCookie.Check()
func StoreNamedColorChecked(c *xgb.Conn, Flags byte, Cmap Colormap, Pixel uint32, NameLen uint16, Name string) StoreNamedColorCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(storeNamedColorRequest(c, Flags, Cmap, Pixel, NameLen, Name), cookie)
	return StoreNamedColorCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook StoreNamedColorCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for StoreNamedColor
// storeNamedColorRequest writes a StoreNamedColor request to a byte slice.
func storeNamedColorRequest(c *xgb.Conn, Flags byte, Cmap Colormap, Pixel uint32, NameLen uint16, Name string) []byte {
	size := xgb.Pad((16 + xgb.Pad((int(NameLen) * 1))))
	b := 0
	buf := make([]byte, size)

	buf[b] = 90 // request opcode
	b += 1

	buf[b] = Flags
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	xgb.Put32(buf[b:], Pixel)
	b += 4

	xgb.Put16(buf[b:], NameLen)
	b += 2

	b += 2 // padding

	copy(buf[b:], Name[:NameLen])
	b += int(NameLen)

	return buf
}

// TranslateCoordinatesCookie is a cookie used only for TranslateCoordinates requests.
type TranslateCoordinatesCookie struct {
	*xgb.Cookie
}

// TranslateCoordinates sends a checked request.
// If an error occurs, it will be returned with the reply by calling TranslateCoordinatesCookie.Reply()
func TranslateCoordinates(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16) TranslateCoordinatesCookie {
	cookie := c.NewCookie(true, true)
	c.NewRequest(translateCoordinatesRequest(c, SrcWindow, DstWindow, SrcX, SrcY), cookie)
	return TranslateCoordinatesCookie{cookie}
}

// TranslateCoordinatesUnchecked sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func TranslateCoordinatesUnchecked(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16) TranslateCoordinatesCookie {
	cookie := c.NewCookie(false, true)
	c.NewRequest(translateCoordinatesRequest(c, SrcWindow, DstWindow, SrcX, SrcY), cookie)
	return TranslateCoordinatesCookie{cookie}
}

// TranslateCoordinatesReply represents the data returned from a TranslateCoordinates request.
type TranslateCoordinatesReply struct {
	Sequence   uint16 // sequence number of the request for this reply
	Length     uint32 // number of bytes in this reply
	SameScreen bool
	Child      Window
	DstX       int16
	DstY       int16
}

// Reply blocks and returns the reply data for a TranslateCoordinates request.
func (cook TranslateCoordinatesCookie) Reply() (*TranslateCoordinatesReply, error) {
	buf, err := cook.Cookie.Reply()
	if err != nil {
		return nil, err
	}
	if buf == nil {
		return nil, nil
	}
	return translateCoordinatesReply(buf), nil
}

// translateCoordinatesReply reads a byte slice into a TranslateCoordinatesReply value.
func translateCoordinatesReply(buf []byte) *TranslateCoordinatesReply {
	v := new(TranslateCoordinatesReply)
	b := 1 // skip reply determinant

	if buf[b] == 1 {
		v.SameScreen = true
	} else {
		v.SameScreen = false
	}
	b += 1

	v.Sequence = xgb.Get16(buf[b:])
	b += 2

	v.Length = xgb.Get32(buf[b:]) // 4-byte units
	b += 4

	v.Child = Window(xgb.Get32(buf[b:]))
	b += 4

	v.DstX = int16(xgb.Get16(buf[b:]))
	b += 2

	v.DstY = int16(xgb.Get16(buf[b:]))
	b += 2

	return v
}

// Write request to wire for TranslateCoordinates
// translateCoordinatesRequest writes a TranslateCoordinates request to a byte slice.
func translateCoordinatesRequest(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16) []byte {
	size := 16
	b := 0
	buf := make([]byte, size)

	buf[b] = 40 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(SrcWindow))
	b += 4

	xgb.Put32(buf[b:], uint32(DstWindow))
	b += 4

	xgb.Put16(buf[b:], uint16(SrcX))
	b += 2

	xgb.Put16(buf[b:], uint16(SrcY))
	b += 2

	return buf
}

// UngrabButtonCookie is a cookie used only for UngrabButton requests.
type UngrabButtonCookie struct {
	*xgb.Cookie
}

// UngrabButton sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UngrabButton(c *xgb.Conn, Button byte, GrabWindow Window, Modifiers uint16) UngrabButtonCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(ungrabButtonRequest(c, Button, GrabWindow, Modifiers), cookie)
	return UngrabButtonCookie{cookie}
}

// UngrabButtonChecked sends a checked request.
// If an error occurs, it can be retrieved using UngrabButtonCookie.Check()
func UngrabButtonChecked(c *xgb.Conn, Button byte, GrabWindow Window, Modifiers uint16) UngrabButtonCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(ungrabButtonRequest(c, Button, GrabWindow, Modifiers), cookie)
	return UngrabButtonCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UngrabButtonCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UngrabButton
// ungrabButtonRequest writes a UngrabButton request to a byte slice.
func ungrabButtonRequest(c *xgb.Conn, Button byte, GrabWindow Window, Modifiers uint16) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 29 // request opcode
	b += 1

	buf[b] = Button
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put16(buf[b:], Modifiers)
	b += 2

	b += 2 // padding

	return buf
}

// UngrabKeyCookie is a cookie used only for UngrabKey requests.
type UngrabKeyCookie struct {
	*xgb.Cookie
}

// UngrabKey sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UngrabKey(c *xgb.Conn, Key Keycode, GrabWindow Window, Modifiers uint16) UngrabKeyCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(ungrabKeyRequest(c, Key, GrabWindow, Modifiers), cookie)
	return UngrabKeyCookie{cookie}
}

// UngrabKeyChecked sends a checked request.
// If an error occurs, it can be retrieved using UngrabKeyCookie.Check()
func UngrabKeyChecked(c *xgb.Conn, Key Keycode, GrabWindow Window, Modifiers uint16) UngrabKeyCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(ungrabKeyRequest(c, Key, GrabWindow, Modifiers), cookie)
	return UngrabKeyCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UngrabKeyCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UngrabKey
// ungrabKeyRequest writes a UngrabKey request to a byte slice.
func ungrabKeyRequest(c *xgb.Conn, Key Keycode, GrabWindow Window, Modifiers uint16) []byte {
	size := 12
	b := 0
	buf := make([]byte, size)

	buf[b] = 34 // request opcode
	b += 1

	buf[b] = byte(Key)
	b += 1

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(GrabWindow))
	b += 4

	xgb.Put16(buf[b:], Modifiers)
	b += 2

	b += 2 // padding

	return buf
}

// UngrabKeyboardCookie is a cookie used only for UngrabKeyboard requests.
type UngrabKeyboardCookie struct {
	*xgb.Cookie
}

// UngrabKeyboard sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UngrabKeyboard(c *xgb.Conn, Time Timestamp) UngrabKeyboardCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(ungrabKeyboardRequest(c, Time), cookie)
	return UngrabKeyboardCookie{cookie}
}

// UngrabKeyboardChecked sends a checked request.
// If an error occurs, it can be retrieved using UngrabKeyboardCookie.Check()
func UngrabKeyboardChecked(c *xgb.Conn, Time Timestamp) UngrabKeyboardCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(ungrabKeyboardRequest(c, Time), cookie)
	return UngrabKeyboardCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UngrabKeyboardCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UngrabKeyboard
// ungrabKeyboardRequest writes a UngrabKeyboard request to a byte slice.
func ungrabKeyboardRequest(c *xgb.Conn, Time Timestamp) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 32 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// UngrabPointerCookie is a cookie used only for UngrabPointer requests.
type UngrabPointerCookie struct {
	*xgb.Cookie
}

// UngrabPointer sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UngrabPointer(c *xgb.Conn, Time Timestamp) UngrabPointerCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(ungrabPointerRequest(c, Time), cookie)
	return UngrabPointerCookie{cookie}
}

// UngrabPointerChecked sends a checked request.
// If an error occurs, it can be retrieved using UngrabPointerCookie.Check()
func UngrabPointerChecked(c *xgb.Conn, Time Timestamp) UngrabPointerCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(ungrabPointerRequest(c, Time), cookie)
	return UngrabPointerCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UngrabPointerCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UngrabPointer
// ungrabPointerRequest writes a UngrabPointer request to a byte slice.
func ungrabPointerRequest(c *xgb.Conn, Time Timestamp) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 27 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Time))
	b += 4

	return buf
}

// UngrabServerCookie is a cookie used only for UngrabServer requests.
type UngrabServerCookie struct {
	*xgb.Cookie
}

// UngrabServer sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UngrabServer(c *xgb.Conn) UngrabServerCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(ungrabServerRequest(c), cookie)
	return UngrabServerCookie{cookie}
}

// UngrabServerChecked sends a checked request.
// If an error occurs, it can be retrieved using UngrabServerCookie.Check()
func UngrabServerChecked(c *xgb.Conn) UngrabServerCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(ungrabServerRequest(c), cookie)
	return UngrabServerCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UngrabServerCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UngrabServer
// ungrabServerRequest writes a UngrabServer request to a byte slice.
func ungrabServerRequest(c *xgb.Conn) []byte {
	size := 4
	b := 0
	buf := make([]byte, size)

	buf[b] = 37 // request opcode
	b += 1

	b += 1                             // padding
	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	return buf
}

// UninstallColormapCookie is a cookie used only for UninstallColormap requests.
type UninstallColormapCookie struct {
	*xgb.Cookie
}

// UninstallColormap sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UninstallColormap(c *xgb.Conn, Cmap Colormap) UninstallColormapCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(uninstallColormapRequest(c, Cmap), cookie)
	return UninstallColormapCookie{cookie}
}

// UninstallColormapChecked sends a checked request.
// If an error occurs, it can be retrieved using UninstallColormapCookie.Check()
func UninstallColormapChecked(c *xgb.Conn, Cmap Colormap) UninstallColormapCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(uninstallColormapRequest(c, Cmap), cookie)
	return UninstallColormapCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UninstallColormapCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UninstallColormap
// uninstallColormapRequest writes a UninstallColormap request to a byte slice.
func uninstallColormapRequest(c *xgb.Conn, Cmap Colormap) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 82 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Cmap))
	b += 4

	return buf
}

// UnmapSubwindowsCookie is a cookie used only for UnmapSubwindows requests.
type UnmapSubwindowsCookie struct {
	*xgb.Cookie
}

// UnmapSubwindows sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UnmapSubwindows(c *xgb.Conn, Window Window) UnmapSubwindowsCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(unmapSubwindowsRequest(c, Window), cookie)
	return UnmapSubwindowsCookie{cookie}
}

// UnmapSubwindowsChecked sends a checked request.
// If an error occurs, it can be retrieved using UnmapSubwindowsCookie.Check()
func UnmapSubwindowsChecked(c *xgb.Conn, Window Window) UnmapSubwindowsCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(unmapSubwindowsRequest(c, Window), cookie)
	return UnmapSubwindowsCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UnmapSubwindowsCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UnmapSubwindows
// unmapSubwindowsRequest writes a UnmapSubwindows request to a byte slice.
func unmapSubwindowsRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 11 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// UnmapWindowCookie is a cookie used only for UnmapWindow requests.
type UnmapWindowCookie struct {
	*xgb.Cookie
}

// UnmapWindow sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func UnmapWindow(c *xgb.Conn, Window Window) UnmapWindowCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(unmapWindowRequest(c, Window), cookie)
	return UnmapWindowCookie{cookie}
}

// UnmapWindowChecked sends a checked request.
// If an error occurs, it can be retrieved using UnmapWindowCookie.Check()
func UnmapWindowChecked(c *xgb.Conn, Window Window) UnmapWindowCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(unmapWindowRequest(c, Window), cookie)
	return UnmapWindowCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook UnmapWindowCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for UnmapWindow
// unmapWindowRequest writes a UnmapWindow request to a byte slice.
func unmapWindowRequest(c *xgb.Conn, Window Window) []byte {
	size := 8
	b := 0
	buf := make([]byte, size)

	buf[b] = 10 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(Window))
	b += 4

	return buf
}

// WarpPointerCookie is a cookie used only for WarpPointer requests.
type WarpPointerCookie struct {
	*xgb.Cookie
}

// WarpPointer sends an unchecked request.
// If an error occurs, it can only be retrieved using xgb.WaitForEvent or xgb.PollForEvent.
func WarpPointer(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16, SrcWidth uint16, SrcHeight uint16, DstX int16, DstY int16) WarpPointerCookie {
	cookie := c.NewCookie(false, false)
	c.NewRequest(warpPointerRequest(c, SrcWindow, DstWindow, SrcX, SrcY, SrcWidth, SrcHeight, DstX, DstY), cookie)
	return WarpPointerCookie{cookie}
}

// WarpPointerChecked sends a checked request.
// If an error occurs, it can be retrieved using WarpPointerCookie.Check()
func WarpPointerChecked(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16, SrcWidth uint16, SrcHeight uint16, DstX int16, DstY int16) WarpPointerCookie {
	cookie := c.NewCookie(true, false)
	c.NewRequest(warpPointerRequest(c, SrcWindow, DstWindow, SrcX, SrcY, SrcWidth, SrcHeight, DstX, DstY), cookie)
	return WarpPointerCookie{cookie}
}

// Check returns an error if one occurred for checked requests that are not expecting a reply.
// This cannot be called for requests expecting a reply, nor for unchecked requests.
func (cook WarpPointerCookie) Check() error {
	return cook.Cookie.Check()
}

// Write request to wire for WarpPointer
// warpPointerRequest writes a WarpPointer request to a byte slice.
func warpPointerRequest(c *xgb.Conn, SrcWindow Window, DstWindow Window, SrcX int16, SrcY int16, SrcWidth uint16, SrcHeight uint16, DstX int16, DstY int16) []byte {
	size := 24
	b := 0
	buf := make([]byte, size)

	buf[b] = 41 // request opcode
	b += 1

	b += 1 // padding

	xgb.Put16(buf[b:], uint16(size/4)) // write request size in 4-byte units
	b += 2

	xgb.Put32(buf[b:], uint32(SrcWindow))
	b += 4

	xgb.Put32(buf[b:], uint32(DstWindow))
	b += 4

	xgb.Put16(buf[b:], uint16(SrcX))
	b += 2

	xgb.Put16(buf[b:], uint16(SrcY))
	b += 2

	xgb.Put16(buf[b:], SrcWidth)
	b += 2

	xgb.Put16(buf[b:], SrcHeight)
	b += 2

	xgb.Put16(buf[b:], uint16(DstX))
	b += 2

	xgb.Put16(buf[b:], uint16(DstY))
	b += 2

	return buf
}
