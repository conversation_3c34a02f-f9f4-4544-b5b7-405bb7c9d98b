# 轮次命名修复完成报告

## 🎯 问题描述

用户反馈文件命名中的轮次显示不正确：
- 现在是第二轮截图的第一次B02模式
- 但文件命名中还是"R01"（第一轮）
- 期望文件名应该显示正确的轮次，如"R02"

## 🔍 问题分析

通过代码分析发现问题根源：

### 1. **硬编码轮次问题**
在 `app/services/screenshot_service.go` 的 `generateOptimizedFilename` 方法中：
```go
// 轮次（目前固定为R01，后续可以根据实际需求动态生成）
roundNumber := "R01"
```

### 2. **轮次管理缺失**
- `getCurrentRound` 方法固定返回 1
- 没有轮次状态管理机制
- 缺少轮次递增逻辑

## ✅ 修复方案

### 1. **添加轮次管理器**
在 `App` 结构体中添加轮次管理字段：
```go
type App struct {
    // ... 其他字段
    
    // 轮次管理器 - 用于跟踪每个用户的当前轮次
    roundManager map[string]int
}
```

### 2. **实现动态轮次计算**
修改 `getCurrentRound` 方法：
```go
func (a *App) getCurrentRound(userName string) int {
    if a.roundManager == nil {
        a.roundManager = make(map[string]int)
    }
    
    // 生成用户的唯一标识（用户名+日期）
    today := time.Now().Format("20060102")
    userKey := fmt.Sprintf("%s_%s", userName, today)
    
    // 如果是新用户或新的一天，从第1轮开始
    if currentRound, exists := a.roundManager[userKey]; exists {
        return currentRound
    } else {
        a.roundManager[userKey] = 1
        return 1
    }
}
```

### 3. **添加轮次递增方法**
```go
func (a *App) incrementRound(userName string) int {
    // 获取当前轮次并增加1
    currentRound := a.getCurrentRound(userName)
    newRound := currentRound + 1
    
    // 确保不超过最大轮次（10轮）
    if newRound > 10 {
        newRound = 10
    }
    
    today := time.Now().Format("20060102")
    userKey := fmt.Sprintf("%s_%s", userName, today)
    a.roundManager[userKey] = newRound
    return newRound
}
```

### 4. **修改文件命名逻辑**
更新 `generateOptimizedFilename` 方法：
```go
func (ss *ScreenshotService) generateOptimizedFilename(mode string, userName string, organName string, currentUser interface{}, currentRound int) string {
    // ... 其他逻辑
    
    // 轮次（使用传入的当前轮次）
    roundNumber := fmt.Sprintf("R%02d", currentRound)
    
    // ... 生成文件名
}
```

### 5. **集成到截图流程**
在 `ProcessScreenshotAndUpload` 方法中：
```go
// 1.2 获取当前轮次并使用识别的器官名称进行优化命名的截图
currentRound := a.getCurrentRound(userName)
fmt.Printf("[DEBUG] 当前轮次: R%02d\n", currentRound)

filePath, err := a.screenshotService.TakeScreenshotWithOptimizedNaming(mode, userName, organName, currentUser, currentRound)

// 1.3 截图成功后，增加轮次（为下次截图做准备）
nextRound := a.incrementRound(userName)
fmt.Printf("[DEBUG] 轮次已更新，下次截图将使用: R%02d\n", nextRound)
```

## 🧪 测试验证

创建了测试程序 `test_round_logic.go` 验证轮次逻辑：

### 测试结果
```
第1次截图:
  当前轮次: R01
  文件名: 医生或健康专家_心脏_RPT20250616001_R01_B02.png
  下次轮次: R02

第2次截图:
  当前轮次: R02
  文件名: 医生或健康专家_心脏_RPT20250616001_R02_B02.png
  下次轮次: R03

...

第10次截图:
  当前轮次: R10
  文件名: 医生或健康专家_心脏_RPT20250616001_R10_B02.png
  下次轮次: R10
```

## 📋 功能特性

### 1. **智能轮次管理**
- 每个用户独立的轮次计数
- 按日期重置轮次（新的一天从R01开始）
- 最大轮次限制（R10）

### 2. **用户隔离**
- 不同用户的轮次互不影响
- 支持多用户并发使用

### 3. **日期隔离**
- 每天的轮次从R01重新开始
- 用户标识格式：`用户名_YYYYMMDD`

### 4. **文件命名格式**
```
用户名_器官部位_检测报告号_轮次_列表类型.png
示例：医生或健康专家_心脏_RPT20250616001_R02_B02.png
```

## 🚀 部署说明

### 1. **重新构建应用**
```bash
# 构建包含修复的版本
wails build

# 或构建包含OCR功能的版本（需要先安装Tesseract-OCR）
wails build -tags ocr
```

### 2. **测试步骤**
1. 启动应用
2. 进行第一次截图（应显示R01）
3. 进行第二次截图（应显示R02）
4. 验证文件名中的轮次递增

## 📊 预期效果

修复后的文件命名示例：
```
第1轮: 医生或健康专家_心脏_RPT20250616001_R01_B02.png
第2轮: 医生或健康专家_心脏_RPT20250616001_R02_B02.png
第3轮: 医生或健康专家_心脏_RPT20250616001_R03_B02.png
...
第10轮: 医生或健康专家_心脏_RPT20250616001_R10_B02.png
```

## ✅ 修复完成

- [x] 轮次管理器实现
- [x] 动态轮次计算
- [x] 文件命名修复
- [x] 截图流程集成
- [x] 测试验证通过
- [x] 应用构建成功

现在文件命名将正确显示当前轮次，解决了用户反馈的问题。
