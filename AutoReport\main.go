package main

import (
	"bytes"           // Added for multipart body
	"encoding/base64" // 添加用于Basic认证编码
	"encoding/json"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"io"  // Added for multipart form
	"log" // Add this import
	"net"
	"net/http"
	"net/url" // 添加用于URL编码
	"os"
	"path"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"github.com/kbinani/screenshot"
	"github.com/mozillazg/go-pinyin"
)

var (
	user32               = syscall.NewLazyDLL("user32.dll")
	procGetAsyncKeyState = user32.NewProc("GetAsyncKeyState")
	VK_CONTROL           = 0x11
	VK_SHIFT             = 0x10
	VK_X                 = 0x58
	VK_U                 = 0x55
)

// 用户信息结构
type UserInfo struct {
	Name     string
	Birthday string // 格式: YYYY-MM-DD
	Age      int    // 计算得出的年龄
	Gender   string // 性别: 男/女
	IDNumber string // 身份证号
}

// 当前用户信息（临时使用）
var currentUser = UserInfo{
	Name:     "某用户",
	Birthday: "",
	Age:      0,
	Gender:   "",
	IDNumber: "",
}

// 模式信息结构
type ModeInfo struct {
	Code string
	Name string
}

// 模式配置
var modeConfig = map[string]ModeInfo{
	"A": {"A01", "器官问题来源分析"},
	"B": {"B02", "生化平衡分析"},
	"C": {"C03", "病理形态学分析"},
}

func loadConfig() error {
	// 读取config/app_config.json
	configPath := filepath.Join("config", "app_config.json")
	configFile, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析JSON
	if err := json.Unmarshal(configFile, &Config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}
	log.Printf("Config loaded. AppID: %s", Config.MpAppInfo.AppID)

	// 获取当前MAC地址
	currentMAC, err := getMACAddress()
	if err != nil {
		return fmt.Errorf("获取MAC地址失败: %v", err)
	}

	// 检查并更新MAC地址
	if Config.DeviceInfo.MACAddress == "" {
		Config.DeviceInfo.MACAddress = currentMAC
		if err := saveConfig(&Config); err != nil {
			return fmt.Errorf("保存配置失败: %v", err)
		}
	}
	return nil
}

func getAsyncKeyState(key int) bool {
	ret, _, _ := procGetAsyncKeyState.Call(uintptr(key))
	return ret&0x8000 != 0
}

// 图像预处理函数
func preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()
	enhancedImg := image.NewRGBA(bounds)

	// 1. 提高对比度和亮度
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 将颜色值从uint32转换为float64进行处理
			rf := float64(r >> 8)
			gf := float64(g >> 8)
			bf := float64(b >> 8)

			// 提高对比度（增加20%）
			contrast := 1.2
			rf = ((rf - 128.0) * contrast) + 128.0
			gf = ((gf - 128.0) * contrast) + 128.0
			bf = ((bf - 128.0) * contrast) + 128.0

			// 确保值在0-255范围内
			rf = maxf(0.0, minf(255.0, rf))
			gf = maxf(0.0, minf(255.0, gf))
			bf = maxf(0.0, minf(255.0, bf))

			enhancedImg.Set(x, y, color.RGBA{
				R: uint8(rf),
				G: uint8(gf),
				B: uint8(bf),
				A: uint8(a >> 8),
			})
		}
	}

	// 2. 轻微锐化处理
	sharpenKernel := []float64{
		0, -0.5, 0,
		-0.5, 3, -0.5,
		0, -0.5, 0,
	}

	sharpenedImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64

			// 应用锐化核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := enhancedImg.At(x+kx, y+ky).RGBA()
					weight := sharpenKernel[(ky+1)*3+(kx+1)]
					sumR += float64(r>>8) * weight
					sumG += float64(g>>8) * weight
					sumB += float64(b>>8) * weight
				}
			}

			// 确保值在0-255范围内
			r := uint8(maxf(0.0, minf(255.0, sumR)))
			g := uint8(maxf(0.0, minf(255.0, sumG)))
			b := uint8(maxf(0.0, minf(255.0, sumB)))

			sharpenedImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 3. 轻微平滑处理（使用简单的3x3均值滤波）
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64
			count := 0.0

			// 3x3邻域平均
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := sharpenedImg.At(x+kx, y+ky).RGBA()
					sumR += float64(r >> 8)
					sumG += float64(g >> 8)
					sumB += float64(b >> 8)
					count++
				}
			}

			// 计算平均值
			r := uint8(maxf(0.0, minf(255.0, sumR/count)))
			g := uint8(maxf(0.0, minf(255.0, sumG/count)))
			b := uint8(maxf(0.0, minf(255.0, sumB/count)))

			finalImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return finalImg
}

// 辅助函数 - 处理float64
// minf is used by preprocessImage to handle float64 min operations
func minf(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

func maxf(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func generateFilename(mode string) string {
	modeInfo := modeConfig[mode]
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s-%s-%s", currentUser.Name, modeInfo.Code, timestamp)
}

// Calculates cropping bounds based on percentages
func calculateCropBounds(imgBounds image.Rectangle, topPercent, bottomPercent, leftPercent, rightPercent float64) image.Rectangle {
	width := float64(imgBounds.Dx())
	height := float64(imgBounds.Dy())

	minX := int(width * leftPercent)
	maxX := int(width * (1.0 - rightPercent))
	minY := int(height * topPercent)
	maxY := int(height * (1.0 - bottomPercent))

	// Ensure bounds are within the original image
	minX = max(minX, imgBounds.Min.X)
	minY = max(minY, imgBounds.Min.Y)
	maxX = min(maxX, imgBounds.Max.X)
	maxY = min(maxY, imgBounds.Max.Y)

	return image.Rect(minX, minY, maxX, maxY)
}

// Performs the actual cropping based on calculated bounds
func performCrop(img image.Image, cropBounds image.Rectangle) image.Image {
	// Check if cropBounds is valid
	if cropBounds.Dx() <= 0 || cropBounds.Dy() <= 0 {
		fmt.Println("警告: 计算出的裁剪边界无效，返回原始图片副本。")
		bounds := img.Bounds()
		newImg := image.NewRGBA(bounds)
		for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
			for x := bounds.Min.X; x < bounds.Max.X; x++ {
				newImg.Set(x, y, img.At(x, y))
			}
		}
		return newImg
	}

	// Create a new RGBA image for the cropped area
	croppedImg := image.NewRGBA(image.Rect(0, 0, cropBounds.Dx(), cropBounds.Dy()))

	// Manual copy to be sure
	for y := 0; y < cropBounds.Dy(); y++ {
		for x := 0; x < cropBounds.Dx(); x++ {
			// Calculate corresponding pixel in the original image
			originalX := cropBounds.Min.X + x
			originalY := cropBounds.Min.Y + y
			// Get the color from the original image
			c := img.At(originalX, originalY)
			// Set the color in the new cropped image
			croppedImg.Set(x, y, c)
		}
	}

	return croppedImg
}

func saveImage(img image.Image, filepath string) error {
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	encoder := png.Encoder{
		CompressionLevel: png.BestCompression,
	}

	if err := encoder.Encode(file, img); err != nil {
		return fmt.Errorf("保存图片失败: %v", err)
	}
	return nil
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// 调用扣子load_screenshot工作流，推送截图信息
func callCozeWorkflow(picURL, picName, reportID string) error {
	// 删除原有的常量定义
	// 直接使用Config.APIKeys.Coze中的配置

	requestBody := map[string]interface{}{
		"space_id":          Config.APIKeys.Coze.SpaceID,
		"workflow_id":       Config.APIKeys.Coze.WorkflowIDPostPic, // 调用扣子load_screenshot工作流，推送截图信息
		"app_id":            Config.APIKeys.Coze.AppID,
		"is_async":          false,
		"stream":            false,
		"auto_save_history": true,
		"parameters": map[string]interface{}{
			"input_pic_url": picURL,
			"pic_name":      filepath.Base(picName),
			"report_id":     reportID,
			"check_time":    time.Now().Format("2006-01-02 15:04:05"),
			"site_id":       Config.SiteInfo.SiteID,   // 添加的网点信息
			"site_name":     Config.SiteInfo.SiteName, // 添加的网点信息
		},
	}

	_, err := callCozeAPIWithResponse(requestBody)
	return err

}

// 调用扣子getregistration工作流，推送受检人挂号信息
func callCozeRegistrationWorkflow(registrationNumber string) (string, error) {
	// 使用Config.APIKeys.Coze中的配置

	// 从完整挂号码中提取短挂号码
	parts := strings.Split(registrationNumber, "-")
	shortNumber := parts[len(parts)-1] // 最后一部分是短挂号码

	requestBody := map[string]interface{}{
		"space_id":          Config.APIKeys.Coze.SpaceID,
		"workflow_id":       Config.APIKeys.Coze.WorkflowIDPostRegistration, // 调用扣子getregistration工作流，推送受检人挂号信息
		"app_id":            Config.APIKeys.Coze.AppID,
		"is_async":          false,
		"stream":            false,
		"auto_save_history": true,
		"parameters": map[string]interface{}{
			"registration_number": registrationNumber,
			"short_number":        shortNumber,
			"registration_time":   time.Now().Format("2006-01-02 15:04:05"),
			"input_site_id":       Config.SiteInfo.SiteID,       // 添加的网点信息
			"input_device_mac":    Config.DeviceInfo.MACAddress, // 使用设备MAC地址
		},
	}

	respData, err := callCozeAPIWithResponse(requestBody)
	if err != nil {
		// 尝试解析响应数据获取更友好的错误信息
		if apiErr, ok := err.(*json.UnmarshalTypeError); ok {
			return "", fmt.Errorf("API响应格式错误: 期望类型 %v 但收到 %v", apiErr.Type, apiErr.Value)
		}
		return "", fmt.Errorf("调用API失败: %v", err)
	}

	// 检查响应数据是否包含错误信息
	if msg, ok := respData["msg"].(string); ok && msg != "" && msg != "Success" {
		return "", fmt.Errorf("API返回错误: %s", msg)
	}

	// 尝试从响应中提取用户姓名
	userName, err := parseAPIResponse(respData)
	if err != nil {
		// 如果无法解析用户名，返回一个默认值
		return "未知用户", nil
	}

	return userName, nil
}

// 解析API响应数据
func parseAPIResponse(respData map[string]interface{}) (string, error) {

	// 1. 检查是否有错误信息
	if msg, ok := respData["msg"].(string); ok && strings.Contains(strings.ToLower(msg), "error") {
		return "", fmt.Errorf("API返回错误: %s", msg)
	}

	// 2. 尝试从不同层级和字段获取用户名
	fieldsToCheck := []string{"user_name", "name", "username", "user", "full_name"}

	// 检查data字段
	if data, ok := respData["data"]; ok {
		switch v := data.(type) {
		case string:
			// data是直接的用户名字符串
			return v, nil
		case map[string]interface{}:
			// data是map，尝试从中获取用户名
			for _, field := range fieldsToCheck {
				if name, ok := v[field].(string); ok {
					return name, nil
				}
			}
		}
	}

	// 3. 尝试从顶层获取用户名
	for _, field := range fieldsToCheck {
		if name, ok := respData[field].(string); ok {
			return name, nil
		}
	}

	// 4. 尝试从result字段获取
	if result, ok := respData["result"]; ok {
		switch v := result.(type) {
		case string:
			return v, nil
		case map[string]interface{}:
			for _, field := range fieldsToCheck {
				if name, ok := v[field].(string); ok {
					return name, nil
				}
			}
		}
	}

	// 5. 如果所有尝试都失败，返回友好的错误信息
	return "", fmt.Errorf("无法从API响应中解析用户名，请检查响应格式: %v", respData)
}

// // 通用的扣子API调用函数，不返回响应数据
// func callCozeAPI(requestBody map[string]interface{}) error {
// 	_, err := callCozeAPIWithResponse(requestBody)
// 	return err
// }

// 通用的扣子API调用函数，返回响应数据
func callCozeAPIWithResponse(requestBody map[string]interface{}) (map[string]interface{}, error) {

	fmt.Println("请求体 parameters:", requestBody["parameters"])

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("JSON编码失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", "https://api.coze.cn/v1/workflow/run", bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+Config.APIKeys.Coze.Token)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 处理响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 尝试解析为JSON对象
	var jsonObj map[string]interface{}
	if err := json.Unmarshal(respBody, &jsonObj); err != nil {
		// 如果解析失败，尝试作为字符串处理
		return nil, fmt.Errorf("API响应格式错误: %s", string(respBody))
	}

	// 检查响应状态码
	if resp.StatusCode >= 400 {
		if msg, ok := jsonObj["msg"].(string); ok {
			return nil, fmt.Errorf("API调用失败: %s", msg)
		}
		return nil, fmt.Errorf("API调用失败: 状态码 %d", resp.StatusCode)
	}

	return jsonObj, nil
}

// 修改uploadFile函数，在成功上传后调用工作流
func uploadFile(filepath string) (string, error) {
	LogOperation("文件上传", currentUser.Name, Config.SiteInfo.SiteID)
	fmt.Println("检测网点ID和名称：", Config.SiteInfo.SiteID, Config.SiteInfo.SiteName)
	fmt.Println("开始上传文件到 DCloud:", filepath)

	// 打开文件
	file, err := os.Open(filepath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	// 读取文件内容
	fileInfo, err := file.Stat()
	if err != nil {
		return "", fmt.Errorf("获取文件信息失败: %v", err)
	}

	// 读取文件内容到内存
	fileData := make([]byte, fileInfo.Size())
	_, err = file.Read(fileData)
	if err != nil {
		LogError("文件上传", currentUser.Name, err)
		return "", err
	}

	// 将文件内容转换为base64编码
	base64Data := base64.StdEncoding.EncodeToString(fileData)
	// 添加base64编码头，符合云函数期望的格式
	imgBase64 := fmt.Sprintf("data:image/png;base64,%s", base64Data)

	// 获取文件名
	_, originalFilename := path.Split(filepath)

	// 构建URL，添加文件名和网点ID作为查询参数
	uploadURL := fmt.Sprintf("%s?fileName=%s&siteId=%s",
		Config.APIKeys.Dcloud.UploadURL, // 从配置中获取
		url.QueryEscape(originalFilename),
		url.QueryEscape(Config.SiteInfo.SiteID))

	// 创建POST请求，直接发送base64数据作为请求体
	req, err := http.NewRequest("POST", uploadURL, bytes.NewBufferString(imgBase64))
	if err != nil {
		return "", fmt.Errorf("创建DCloud上传请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "text/plain")

	// 如果云函数需要认证，保留这些头部
	// 删除这部分代码
	// if dcloudAccessKey != "" && dcloudSecretKey != "" {
	//    authValue := fmt.Sprintf("%s:%s", dcloudAccessKey, dcloudSecretKey)
	//    encodedAuth := base64.StdEncoding.EncodeToString([]byte(authValue))
	//    authHeader := fmt.Sprintf("Basic %s", encodedAuth)
	//    req.Header.Set("Authorization", authHeader)
	//    req.Header.Set("X-CloudBase-Application", dcloudSpaceId)
	//    req.Header.Set("x-basement-token", dcloudAccessKey)
	// }

	// 保留这部分代码，它已经使用了正确的配置变量
	if Config.APIKeys.Dcloud.AccessKey != "" && Config.APIKeys.Dcloud.SecretKey != "" {
		authValue := fmt.Sprintf("%s:%s", Config.APIKeys.Dcloud.AccessKey, Config.APIKeys.Dcloud.SecretKey)
		encodedAuth := base64.StdEncoding.EncodeToString([]byte(authValue))
		authHeader := fmt.Sprintf("Basic %s", encodedAuth)
		req.Header.Set("Authorization", authHeader)
		req.Header.Set("X-CloudBase-Application", Config.APIKeys.Dcloud.SpaceID)
		req.Header.Set("x-basement-token", Config.APIKeys.Dcloud.AccessKey)
	}

	fmt.Println("正在发送上传请求到 DCloud:", req.URL.String())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送 DCloud 上传请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取 DCloud 响应失败: %v", err)
	}

	fmt.Printf("DCloud 上传响应resp: %d\n", resp.StatusCode)

	// 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return "", fmt.Errorf("DCloud 上传失败 (状态码: %d): %s", resp.StatusCode, string(respBody))
	}

	// 修改后的客户端代码关键部分
	// 解析 DCloud 的响应
	var dcloudResp struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    map[string]interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &dcloudResp); err != nil {
		return "", fmt.Errorf("解析DCloud响应失败: %v", err)
	}

	// 检查响应状态
	if dcloudResp.Code != 0 {
		return "", fmt.Errorf("上传失败: %s", dcloudResp.Message)
	}

	// 直接从data中获取tempFileURL
	tempFileURL, ok := dcloudResp.Data["tempFileURL"].(string)
	if !ok {
		return "", fmt.Errorf("响应中未找到有效的tempFileURL")
	}

	fmt.Printf("文件 %s 上传到 DCloud 成功！\n", originalFilename)
	fmt.Printf("文件URL: %s\n", tempFileURL)

	// 生成报告ID
	userAge := fmt.Sprintf("%d", currentUser.Age)
	if currentUser.Age == 0 {
		userAge = "NA" // 如果年龄未知，使用NA标记
	}
	reportID := generateReportID(currentUser.Name, userAge)
	fmt.Printf("报告ID: %s\n", reportID)

	// 调用扣子工作流
	fmt.Println("准备调用扣子工作流...")
	err = callCozeWorkflow(tempFileURL, originalFilename, reportID)
	if err != nil {
		fmt.Printf("调用扣子工作流失败: %v\n", err)
		return tempFileURL, fmt.Errorf("工作流调用失败但文件已上传")
	}

	fmt.Println("扣子工作流调用成功！")
	return tempFileURL, nil
}

// 命令行模式
func runCommandLine() {
	fmt.Println("截图程序已启动（命令行模式）")
	fmt.Println("当前用户:", currentUser.Name)
	fmt.Println("可用模式：")
	// 模式A: A01 # 器官问题来源分析
	fmt.Println("Ctrl + Shift + A: 器官问题来源分析")

	// 模式B: B02 # 生化平衡分析
	fmt.Println("Ctrl + Shift + B: 生化平衡分析")

	// 模式C: C03 # 病理形态学分析
	fmt.Println("Ctrl + Shift + C: 病理形态学分析（仅保留数字行）")

	// 上传功能
	fmt.Println("Ctrl + Shift + U: 上传最新截图")

	fmt.Println("\n按 Ctrl + X 终止程序")

	// 添加冷却时间，防止重复触发
	lastScreenshot := time.Now().Add(-1 * time.Second)
	cooldown := 500 * time.Millisecond

	for {
		// 检查终止程序的快捷键
		if getAsyncKeyState(VK_CONTROL) && getAsyncKeyState(VK_X) {
			fmt.Println("\n检测到 Ctrl + X，程序即将退出...")
			time.Sleep(500 * time.Millisecond) // 给一点时间显示退出消息
			return
		}

		// 检查组合键是否按下
		ctrlPressed := getAsyncKeyState(VK_CONTROL)
		shiftPressed := getAsyncKeyState(VK_SHIFT)
		aPressed := getAsyncKeyState(0x41) // A键
		bPressed := getAsyncKeyState(0x42) // B键
		cPressed := getAsyncKeyState(0x43) // C键
		uPressed := getAsyncKeyState(VK_U) // U键

		// 检查截图快捷键和模式选择
		if ctrlPressed && shiftPressed {
			if uPressed {
				// 获取pic目录下最新的PNG文件
				files, err := filepath.Glob("pic/*.png")
				if err != nil {
					fmt.Printf("获取文件列表失败: %v\n", err)
					continue
				}

				if len(files) == 0 {
					fmt.Println("没有找到可上传的图片文件")
					continue
				}

				// 按修改时间排序，获取最新的文件
				latestFile := files[0]
				latestTime, err := os.Stat(latestFile)
				if err != nil {
					fmt.Printf("获取文件信息失败: %v\n", err)
					continue
				}

				for _, file := range files[1:] {
					fileInfo, err := os.Stat(file)
					if err != nil {
						continue
					}
					if fileInfo.ModTime().After(latestTime.ModTime()) {
						latestFile = file
						latestTime = fileInfo
					}
				}

				fmt.Printf("正在上传最新截图: %s\n", latestFile)
				fileURL, err := uploadFile(latestFile)
				if err != nil {
					fmt.Printf("上传失败: %v\n", err)
				} else {
					fmt.Printf("\n========================================\n")
					fmt.Printf("上传成功! 文件URL: \n%s\n", fileURL)
					fmt.Printf("========================================\n")
					// 这里可以添加代码来使用fileURL，例如复制到剪贴板或显示在界面上
				}
				continue
			}

			var mode string
			if aPressed {
				mode = "A"
			} else if bPressed {
				mode = "B"
			} else if cPressed {
				mode = "C"
			}

			if mode != "" {
				// 检查是否在冷却时间内
				if time.Since(lastScreenshot) < cooldown {
					time.Sleep(50 * time.Millisecond)
					continue
				}

				fmt.Printf("正在使用模式%s进行截图...\n", mode)

				// 获取屏幕数量
				n := screenshot.NumActiveDisplays()
				if n <= 0 {
					fmt.Println("未检测到显示器")
					continue
				}

				// 捕获主屏幕
				bounds := screenshot.GetDisplayBounds(0)
				fmt.Printf("正在捕获屏幕区域: %v\n", bounds)

				img, err := screenshot.CaptureRect(bounds)
				if err != nil {
					fmt.Printf("截图失败: %v\n", err)
					continue
				}

				// --- 二次裁剪：基于百分比 ---
				cropBounds := calculateCropBounds(img.Bounds(),
					Config.CropSettings.TopPercent,
					Config.CropSettings.BottomPercent,
					Config.CropSettings.LeftPercent,
					Config.CropSettings.RightPercent)
				fmt.Printf("应用百分比裁剪，新区域: %v\n", cropBounds)
				croppedImg := performCrop(img, cropBounds)

				// 生成文件名并保存图片
				filename := generateFilename(mode)
				filepath := filepath.Join("pic", filename+".png")

				// 保存裁剪后的图片
				if err := saveImage(croppedImg, filepath); err != nil {
					fmt.Printf("保存图片失败: %v\n", err)
					continue
				}

				// 获取文件大小
				fileInfo, err := os.Stat(filepath)
				if err != nil {
					fmt.Printf("获取文件信息失败: %v\n", err)
				} else {
					fmt.Printf("截图已保存: %s (大小: %d 字节)\n", filepath, fileInfo.Size())
				}

				lastScreenshot = time.Now()
			}
		}

		// 短暂延迟，避免CPU占用过高
		time.Sleep(50 * time.Millisecond)
	}
}

// 主函数
func main() {
	// 初始化日志
	if err := initLogger(); err != nil {
		log.Printf("警告: %v\n", err)
	}

	// 加载配置(会自动处理MAC地址)
	if err := loadConfig(); err != nil {
		fmt.Printf("警告: %v\n", err)
		fmt.Println("将使用默认裁剪设置")
		// 设置默认值
		Config.CropSettings.TopPercent = 0.153
		Config.CropSettings.BottomPercent = 0.051
		Config.CropSettings.LeftPercent = 0.0482
		Config.CropSettings.RightPercent = 0.30
	}

	// 创建pic目录（如果不存在）
	if err := os.MkdirAll("pic", 0755); err != nil {
		fmt.Printf("创建目录失败: %v\n", err)
		return
	}

	// 检查命令行参数
	useGUI := true
	for _, arg := range os.Args[1:] {
		if arg == "--cli" || arg == "-c" {
			useGUI = false
			break
		}
	}

	if useGUI {
		// 启动GUI模式
		fmt.Println("启动GUI模式...")
		gui := NewHealthAppGUI()
		gui.appConfig = Config                                               // 将加载的配置传递给GUI实例
		log.Printf("AppID passed to GUI: %s", gui.appConfig.MpAppInfo.AppID) // 添加日志打印传递给GUI的AppID
		gui.Run()
	} else {
		// 启动命令行模式
		runCommandLine()
	}
}

func getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
			mac := iface.HardwareAddr.String()
			return strings.ReplaceAll(mac, ":", ""), nil
		}
	}
	return "", fmt.Errorf("no active network interface found")
}

// 在generateFilename函数基础上修改
func generateReportID(userName, userAge string) string {
	// 机构信息（从配置获取）
	siteCode := Config.SiteInfo.SiteID

	// 用户标识（姓名拼音+年龄）
	userID := fmt.Sprintf("%s%s", convertToPinyin(userName), userAge)

	// 时间戳（精确到分钟）
	timestamp := time.Now().Format("200601021504")

	// 设备标识（取MAC地址后6位）
	deviceID := ""
	if len(Config.DeviceInfo.MACAddress) >= 8 {
		deviceID = strings.ReplaceAll(Config.DeviceInfo.MACAddress[len(Config.DeviceInfo.MACAddress)-8:], ":", "")
	}

	return fmt.Sprintf("%s_%s_%s_%s",
		siteCode, deviceID, userID, timestamp)
}

// 拼音转换辅助函数（使用专业库）
// 由于 pinyin 已被导入的包声明，修改函数名为 convertToPinyin
func convertToPinyin(name string) string {
	pinyinArgs := pinyin.NewArgs()
	pys := pinyin.Pinyin(name, pinyinArgs)
	var result string
	for _, py := range pys {
		if len(py) > 0 {
			result += py[0]
		}
	}
	return result
}
