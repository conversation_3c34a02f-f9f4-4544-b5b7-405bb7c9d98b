package main

import (
	"encoding/json"
	"fmt"
	"image"
	"image/png"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// 双OCR融合结果
type DualOCRResult struct {
	FinalText          string   `json:"final_text"`
	Confidence         float64  `json:"confidence"`
	SourceEngines      []string `json:"source_engines"`
	CorrectionsApplied []string `json:"corrections_applied"`
	Success            bool     `json:"success"`
}

// 双OCR融合服务
type DualOCRFusionService struct {
	pythonPath  string
	scriptPath  string
	tempDir     string
	cropEnabled bool
	cropRatio   float64 // 裁剪比例 (0.25 = 1/4)
}

// 创建双OCR融合服务
func NewDualOCRFusionService() *DualOCRFusionService {
	return &DualOCRFusionService{
		pythonPath:  "python",
		scriptPath:  "dual_ocr_fusion_system.py",
		tempDir:     "temp",
		cropEnabled: false, // 先测试全图识别
		cropRatio:   0.5,   // 如果启用裁剪，使用左上角1/2区域
	}
}

// 检查环境
func (d *DualOCRFusionService) CheckEnvironment() error {
	fmt.Println("=== 检查双OCR融合环境 ===")

	// 检查Python
	cmd := exec.Command(d.pythonPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("Python未安装: %v", err)
	}
	fmt.Printf("✓ Python: %s", string(output))

	// 检查融合脚本
	if _, err := os.Stat(d.scriptPath); os.IsNotExist(err) {
		return fmt.Errorf("融合脚本不存在: %s", d.scriptPath)
	}
	fmt.Printf("✓ 融合脚本: %s\n", d.scriptPath)

	// 测试脚本可用性
	cmd = exec.Command(d.pythonPath, d.scriptPath, "--help")
	err = cmd.Run()
	if err != nil {
		fmt.Printf("⚠ 脚本测试失败，但继续执行\n")
	} else {
		fmt.Printf("✓ 脚本测试通过\n")
	}

	return nil
}

// 裁剪图片到左上角指定比例区域
func (d *DualOCRFusionService) cropImage(imagePath string) (string, error) {
	if !d.cropEnabled {
		return imagePath, nil
	}

	// 打开原图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图片失败: %v", err)
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算裁剪区域
	cropWidth := int(float64(width) * d.cropRatio)
	cropHeight := int(float64(height) * d.cropRatio)

	fmt.Printf("原图尺寸: %dx%d\n", width, height)
	fmt.Printf("裁剪区域: %dx%d (左上角%.0f%%)\n", cropWidth, cropHeight, d.cropRatio*100)

	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, cropWidth, cropHeight))

	// 复制像素
	for y := 0; y < cropHeight; y++ {
		for x := 0; x < cropWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}

	// 保存裁剪后的图片
	os.MkdirAll(d.tempDir, 0755)

	// 生成临时文件名
	baseName := filepath.Base(imagePath)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)
	croppedPath := filepath.Join(d.tempDir, fmt.Sprintf("%s_cropped%s", nameWithoutExt, ext))

	outputFile, err := os.Create(croppedPath)
	if err != nil {
		return "", fmt.Errorf("创建裁剪文件失败: %v", err)
	}
	defer outputFile.Close()

	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", fmt.Errorf("保存裁剪图片失败: %v", err)
	}

	fmt.Printf("✓ 图片裁剪完成: %s\n", croppedPath)
	return croppedPath, nil
}

// 使用双OCR融合系统识别图片
func (d *DualOCRFusionService) RecognizeImage(imagePath string) (*DualOCRResult, error) {
	fmt.Printf("=== 双OCR融合识别 ===\n")
	fmt.Printf("原始图片: %s\n", imagePath)

	startTime := time.Now()

	// 裁剪图片
	processedImagePath, err := d.cropImage(imagePath)
	if err != nil {
		return nil, fmt.Errorf("图片裁剪失败: %v", err)
	}

	// 如果裁剪了图片，使用裁剪后的路径
	if processedImagePath != imagePath {
		defer os.Remove(processedImagePath) // 清理临时文件
	}

	// 调用Python融合脚本
	fmt.Printf("调用融合脚本...\n")
	cmd := exec.Command(d.pythonPath, d.scriptPath, processedImagePath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("融合脚本执行失败: %v", err)
	}

	// 解析输出，查找JSON结果
	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	var jsonLine string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "{") && strings.HasSuffix(line, "}") {
			jsonLine = line
			break
		}
	}

	if jsonLine == "" {
		return nil, fmt.Errorf("未找到JSON输出结果")
	}

	// 解析JSON结果
	var result DualOCRResult
	err = json.Unmarshal([]byte(jsonLine), &result)
	if err != nil {
		return nil, fmt.Errorf("解析JSON结果失败: %v\n原始输出: %s", err, outputStr)
	}

	duration := time.Since(startTime)
	fmt.Printf("✓ 识别完成，耗时: %v\n", duration)

	return &result, nil
}

// 分析识别结果
func (d *DualOCRFusionService) AnalyzeResult(result *DualOCRResult) {
	fmt.Printf("\n=== 识别结果分析 ===\n")
	fmt.Printf("最终文本: [%s]\n", result.FinalText)
	fmt.Printf("置信度: %.2f%%\n", result.Confidence*100)
	fmt.Printf("使用引擎: %v\n", result.SourceEngines)

	if len(result.CorrectionsApplied) > 0 {
		fmt.Printf("应用校正: %v\n", result.CorrectionsApplied)
	}

	// 验证是否是期望的格式
	if strings.Contains(result.FinalText, "0.000") && strings.Contains(result.FinalText, "腹部") {
		fmt.Printf("✓ 识别结果符合期望格式\n")
	} else if strings.Contains(result.FinalText, "0.000") {
		fmt.Printf("⚠ 识别到数字部分，但器官名称可能需要进一步处理\n")
	} else {
		fmt.Printf("❌ 未识别到期望的格式\n")
	}

	// 性能评估
	if result.Confidence > 0.9 {
		fmt.Printf("✓ 高置信度识别\n")
	} else if result.Confidence > 0.7 {
		fmt.Printf("⚠ 中等置信度识别\n")
	} else {
		fmt.Printf("❌ 低置信度识别，建议人工校验\n")
	}
}

// 完整的识别流程
func performDualOCRRecognition(imagePath string) {
	fmt.Println("=== 双OCR融合识别系统 ===")

	// 创建服务
	service := NewDualOCRFusionService()

	// 检查环境
	if err := service.CheckEnvironment(); err != nil {
		fmt.Printf("环境检查失败: %v\n", err)
		return
	}

	// 执行识别
	result, err := service.RecognizeImage(imagePath)
	if err != nil {
		fmt.Printf("识别失败: %v\n", err)
		return
	}

	// 分析结果
	service.AnalyzeResult(result)

	// 输出业务可用的结果
	fmt.Printf("\n=== 业务集成结果 ===\n")
	if result.Success && result.FinalText != "" {
		fmt.Printf("器官名称: %s\n", result.FinalText)
		fmt.Printf("可用于业务流程: ✓\n")
	} else {
		fmt.Printf("识别失败，需要降级处理\n")
	}
}

func main() {
	// 直接使用英文文件名的测试图片
	imagePath := "pic/test_image.png"

	// 检查文件
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 找不到测试图片: %s\n", imagePath)
		fmt.Printf("请确保图片文件存在\n")
		return
	}

	fmt.Printf("✓ 使用图片: %s\n", imagePath)

	// 执行双OCR融合识别
	performDualOCRRecognition(imagePath)
}
