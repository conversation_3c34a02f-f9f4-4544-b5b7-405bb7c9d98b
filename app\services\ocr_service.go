//go:build ocr
// +build ocr

package services

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/otiai10/gosseract/v2"
)

// OCRService OCR识别服务
type OCRService struct {
	client *gosseract.Client
}

// NewRealOCRService 创建新的真实OCR服务
func NewRealOCRService() *OCRService {
	// 尝试创建OCR客户端，如果失败则返回nil客户端的服务
	var client *gosseract.Client

	// 使用defer和recover来捕获可能的panic
	func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("OCR初始化失败: %v\n", r)
				client = nil
			}
		}()

		client = gosseract.NewClient()
		if client != nil {
			// 设置OCR语言为中文和英文
			client.SetLanguage("chi_sim", "eng")

			// 设置页面分割模式 - 自动检测
			client.SetPageSegMode(gosseract.PSM_AUTO)
		}
	}()

	return &OCRService{
		client: client,
	}
}

// Close 关闭OCR客户端
func (ocr *OCRService) Close() {
	if ocr.client != nil {
		ocr.client.Close()
	}
}

// ExtractTextFromImage 从图片中提取所有文字
func (ocr *OCRService) ExtractTextFromImage(imagePath string) (string, error) {
	if ocr.client == nil {
		return "", fmt.Errorf("OCR客户端未初始化")
	}

	// 设置图片路径
	err := ocr.client.SetImage(imagePath)
	if err != nil {
		return "", fmt.Errorf("设置图片失败: %v", err)
	}

	// 执行OCR识别
	text, err := ocr.client.Text()
	if err != nil {
		return "", fmt.Errorf("OCR识别失败: %v", err)
	}

	return strings.TrimSpace(text), nil
}

// ExtractOrganName 从截图中提取器官部位名称
func (ocr *OCRService) ExtractOrganName(imagePath string) (string, error) {
	// 首先提取所有文字
	fullText, err := ocr.ExtractTextFromImage(imagePath)
	if err != nil {
		return "", fmt.Errorf("提取文字失败: %v", err)
	}

	// 解析器官名称
	organName := ocr.parseOrganFromText(fullText)
	if organName == "" {
		return "未知器官", nil
	}

	return organName, nil
}

// parseOrganFromText 从OCR识别的文字中解析器官名称
func (ocr *OCRService) parseOrganFromText(text string) string {
	// 将文本按行分割
	lines := strings.Split(text, "\n")

	// 常见器官关键词列表
	organKeywords := []string{
		"心脏", "肝脏", "肺", "肾脏", "脾脏", "胃", "肠", "胆囊", "胰腺",
		"甲状腺", "前列腺", "子宫", "卵巢", "乳腺", "膀胱", "食管",
		"十二指肠", "小肠", "大肠", "结肠", "直肠", "阑尾",
		"心", "肝", "脾", "肺", "肾", "胆", "胰", "胃肠", "肠胃",
		"头部", "颈部", "胸部", "腹部", "盆腔", "四肢",
		"脑", "脑部", "头颅", "颅脑", "脊柱", "脊椎",
		"骨骼", "关节", "肌肉", "血管", "神经",
	}

	// 查找包含器官关键词的行
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查是否包含器官关键词
		for _, keyword := range organKeywords {
			if strings.Contains(line, keyword) {
				// 尝试提取更精确的器官名称
				extracted := ocr.extractOrganFromLine(line, keyword)
				if extracted != "" {
					return extracted
				}
			}
		}
	}

	// 如果没有找到明确的器官名称，尝试使用正则表达式匹配
	return ocr.extractOrganWithRegex(text)
}

// extractOrganFromLine 从包含器官关键词的行中提取器官名称
func (ocr *OCRService) extractOrganFromLine(line, keyword string) string {
	// 清理行内容
	line = strings.TrimSpace(line)

	// 如果行很短且主要是器官名称，直接返回关键词
	if len(line) <= 10 && strings.Contains(line, keyword) {
		return keyword
	}

	// 尝试提取器官名称的模式
	patterns := []string{
		// 匹配 "器官名称检查" 或 "器官名称分析" 等
		fmt.Sprintf(`(%s[检查分析报告结果]*?)`, keyword),
		// 匹配 "检查器官名称" 等
		fmt.Sprintf(`[检查分析报告]*?(%s)`, keyword),
		// 直接匹配器官名称
		fmt.Sprintf(`(%s)`, keyword),
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(line)
		if len(matches) > 1 {
			return strings.TrimSpace(matches[1])
		}
	}

	return keyword
}

// extractOrganWithRegex 使用正则表达式提取器官名称
func (ocr *OCRService) extractOrganWithRegex(text string) string {
	// 常用的器官名称正则模式
	patterns := []string{
		`([心肝脾肺肾胃肠胆胰]脏?)`,
		`(甲状腺|前列腺|乳腺)`,
		`([头颈胸腹]部)`,
		`(脑部?|头颅|颅脑)`,
		`(脊柱|脊椎)`,
		`(子宫|卵巢|膀胱|食管)`,
		`(十二指肠|小肠|大肠|结肠|直肠)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) > 1 {
			return strings.TrimSpace(matches[1])
		}
	}

	return ""
}

// GetOrganNameWithConfidence 获取器官名称及置信度
func (ocr *OCRService) GetOrganNameWithConfidence(imagePath string) (string, float64, error) {
	// 提取器官名称
	organName, err := ocr.ExtractOrganName(imagePath)
	if err != nil {
		return "", 0.0, err
	}

	// 简单的置信度计算（基于是否找到明确的器官名称）
	confidence := 0.5 // 默认置信度
	if organName != "未知器官" {
		confidence = 0.8 // 找到器官名称时的置信度
	}

	return organName, confidence, nil
}

// ValidateOCREnvironment 验证OCR环境是否正确配置
func (ocr *OCRService) ValidateOCREnvironment() error {
	if ocr.client == nil {
		return fmt.Errorf("OCR客户端未初始化，请确保已正确安装Tesseract-OCR及其依赖库")
	}

	// 尝试获取Tesseract版本信息
	version := ocr.client.Version()
	if version == "" {
		return fmt.Errorf("无法获取Tesseract版本信息，请检查Tesseract是否正确安装")
	}

	fmt.Printf("Tesseract版本: %s\n", version)
	return nil
}

// OCRResult OCR识别结果结构
type OCRResult struct {
	FullText    string        `json:"full_text"`    // 完整识别文字
	OrganName   string        `json:"organ_name"`   // 器官名称
	Confidence  float64       `json:"confidence"`   // 置信度
	ProcessTime time.Duration `json:"process_time"` // 处理时间
	ImagePath   string        `json:"image_path"`   // 图片路径
}

// ProcessImageWithDetails 处理图片并返回详细结果
func (ocr *OCRService) ProcessImageWithDetails(imagePath string) (*OCRResult, error) {
	startTime := time.Now()

	// 提取完整文字
	fullText, err := ocr.ExtractTextFromImage(imagePath)
	if err != nil {
		return nil, fmt.Errorf("提取文字失败: %v", err)
	}

	// 提取器官名称和置信度
	organName, confidence, err := ocr.GetOrganNameWithConfidence(imagePath)
	if err != nil {
		return nil, fmt.Errorf("提取器官名称失败: %v", err)
	}

	processTime := time.Since(startTime)

	result := &OCRResult{
		FullText:    fullText,
		OrganName:   organName,
		Confidence:  confidence,
		ProcessTime: processTime,
		ImagePath:   imagePath,
	}

	return result, nil
}

// tryCreateRealOCRService 尝试创建真实的OCR服务（覆盖接口中的默认实现）
func tryCreateRealOCRService() OCRInterface {
	service := NewRealOCRService()
	if service.client == nil {
		return nil
	}
	return service
}
