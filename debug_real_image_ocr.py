#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
调试真实医疗图像OCR识别
专门针对真实医疗截图进行调试和优化
"""

import os
import sys
import time
import json
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional

def debug_real_medical_image():
    """调试真实医疗图像"""
    image_path = r"F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png"
    
    print("=== 调试真实医疗图像OCR ===")
    print(f"图像路径: {image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    print("OK: 图像文件存在")
    
    # 读取图像
    try:
        image = cv2.imread(image_path)
        if image is None:
            print("❌ 无法读取图像文件")
            return
        
        height, width = image.shape[:2]
        print(f"✓ 图像尺寸: {width} x {height}")
        
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return
    
    # 检查EasyOCR
    try:
        import easyocr
        print("✓ EasyOCR可用")
        
        # 初始化EasyOCR
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False, verbose=False)
        print("✓ EasyOCR初始化成功")
        
    except ImportError:
        print("❌ EasyOCR不可用")
        return
    except Exception as e:
        print(f"❌ EasyOCR初始化失败: {e}")
        return
    
    # 图像预处理和识别
    print("\n=== 图像预处理和识别 ===")
    
    # 1. 原图直接识别
    print("1. 原图直接识别...")
    try:
        start_time = time.time()
        results_original = reader.readtext(image_path, detail=1)
        time_original = time.time() - start_time
        
        print(f"   耗时: {time_original:.3f}秒")
        print(f"   识别结果数量: {len(results_original)}")
        
        for i, (bbox, text, confidence) in enumerate(results_original):
            print(f"   结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
    except Exception as e:
        print(f"   ❌ 原图识别失败: {e}")
        results_original = []
    
    # 2. 灰度图识别
    print("\n2. 灰度图识别...")
    try:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_path = image_path.replace('.png', '_gray.png')
        cv2.imwrite(gray_path, gray)
        
        start_time = time.time()
        results_gray = reader.readtext(gray_path, detail=1)
        time_gray = time.time() - start_time
        
        print(f"   耗时: {time_gray:.3f}秒")
        print(f"   识别结果数量: {len(results_gray)}")
        
        for i, (bbox, text, confidence) in enumerate(results_gray):
            print(f"   结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
        # 清理临时文件
        if os.path.exists(gray_path):
            os.remove(gray_path)
            
    except Exception as e:
        print(f"   ❌ 灰度图识别失败: {e}")
        results_gray = []
    
    # 3. 增强处理后识别
    print("\n3. 增强处理后识别...")
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # CLAHE增强
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 高斯滤波去噪
        denoised = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        # 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(
            sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        enhanced_path = image_path.replace('.png', '_enhanced.png')
        cv2.imwrite(enhanced_path, binary)
        
        start_time = time.time()
        results_enhanced = reader.readtext(enhanced_path, detail=1)
        time_enhanced = time.time() - start_time
        
        print(f"   耗时: {time_enhanced:.3f}秒")
        print(f"   识别结果数量: {len(results_enhanced)}")
        
        for i, (bbox, text, confidence) in enumerate(results_enhanced):
            print(f"   结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
        # 保留增强图像用于检查
        print(f"   增强图像已保存: {enhanced_path}")
            
    except Exception as e:
        print(f"   ❌ 增强处理识别失败: {e}")
        results_enhanced = []
    
    # 4. 不同参数设置识别
    print("\n4. 不同参数设置识别...")
    try:
        # 使用不同的参数设置
        start_time = time.time()
        results_params = reader.readtext(
            image_path,
            detail=1,
            paragraph=False,
            width_ths=0.5,  # 降低宽度阈值
            height_ths=0.5,  # 降低高度阈值
            decoder='greedy',
            beamWidth=10,  # 增加束搜索宽度
            batch_size=1,
        )
        time_params = time.time() - start_time
        
        print(f"   耗时: {time_params:.3f}秒")
        print(f"   识别结果数量: {len(results_params)}")
        
        for i, (bbox, text, confidence) in enumerate(results_params):
            print(f"   结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
    except Exception as e:
        print(f"   ❌ 参数优化识别失败: {e}")
        results_params = []
    
    # 5. 图像分割识别
    print("\n5. 图像分割识别...")
    try:
        # 将图像分割为多个区域
        height, width = image.shape[:2]
        
        # 分割为4个区域
        regions = [
            image[0:height//2, 0:width//2],  # 左上
            image[0:height//2, width//2:width],  # 右上
            image[height//2:height, 0:width//2],  # 左下
            image[height//2:height, width//2:width],  # 右下
        ]
        
        region_names = ["左上", "右上", "左下", "右下"]
        
        for i, (region, name) in enumerate(zip(regions, region_names)):
            region_path = image_path.replace('.png', f'_region_{i}.png')
            cv2.imwrite(region_path, region)
            
            try:
                start_time = time.time()
                results_region = reader.readtext(region_path, detail=1)
                time_region = time.time() - start_time
                
                print(f"   {name}区域: {len(results_region)}个结果 ({time_region:.3f}秒)")
                
                for j, (bbox, text, confidence) in enumerate(results_region):
                    if confidence > 0.3:  # 只显示高置信度结果
                        print(f"     结果{j+1}: [{text}] 置信度: {confidence:.3f}")
                
                # 清理临时文件
                if os.path.exists(region_path):
                    os.remove(region_path)
                    
            except Exception as e:
                print(f"     ❌ {name}区域识别失败: {e}")
    
    except Exception as e:
        print(f"   ❌ 图像分割识别失败: {e}")
    
    # 汇总分析
    print("\n=== 汇总分析 ===")
    all_results = []
    
    if results_original:
        all_results.extend([(r, "原图") for r in results_original])
    if results_gray:
        all_results.extend([(r, "灰度") for r in results_gray])
    if results_enhanced:
        all_results.extend([(r, "增强") for r in results_enhanced])
    if results_params:
        all_results.extend([(r, "参数优化") for r in results_params])
    
    if all_results:
        print(f"总共识别到 {len(all_results)} 个文本结果")
        
        # 查找包含数字的结果
        number_results = []
        for (bbox, text, confidence), method in all_results:
            if any(char.isdigit() for char in text):
                number_results.append((text, confidence, method))
        
        if number_results:
            print(f"\n包含数字的结果 ({len(number_results)}个):")
            for text, confidence, method in number_results:
                print(f"  [{text}] 置信度: {confidence:.3f} 方法: {method}")
        
        # 查找可能的目标模式
        target_patterns = ["0.000", "0,000", "O.000", "0.00O", "o.000"]
        target_results = []
        for (bbox, text, confidence), method in all_results:
            for pattern in target_patterns:
                if pattern in text:
                    target_results.append((text, confidence, method, pattern))
        
        if target_results:
            print(f"\n找到目标模式 ({len(target_results)}个):")
            for text, confidence, method, pattern in target_results:
                print(f"  [{text}] 模式: {pattern} 置信度: {confidence:.3f} 方法: {method}")
        else:
            print("\n❌ 未找到目标数字模式")
            
        # 显示所有识别结果
        print(f"\n所有识别结果:")
        for (bbox, text, confidence), method in all_results:
            if confidence > 0.1:  # 显示置信度大于0.1的结果
                print(f"  [{text}] 置信度: {confidence:.3f} 方法: {method}")
    else:
        print("❌ 所有方法都未能识别到文本")
        
        # 提供调试建议
        print("\n=== 调试建议 ===")
        print("1. 检查图像是否包含清晰的文本")
        print("2. 图像可能需要更强的预处理")
        print("3. 考虑使用其他OCR引擎")
        print("4. 检查图像是否为表格格式，可能需要表格识别")

if __name__ == "__main__":
    debug_real_medical_image()
