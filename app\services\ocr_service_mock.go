package services

import (
	"fmt"
	"strings"
	"time"
)

// MockOCRService 模拟OCR服务（当未安装Tesseract时使用）
type MockOCRService struct {
	enabled bool
}

// NewMockOCRService 创建模拟OCR服务
func NewMockOCRService() *MockOCRService {
	return &MockOCRService{
		enabled: false,
	}
}

// Close 关闭模拟OCR服务
func (mock *MockOCRService) Close() {
	// 模拟服务无需关闭操作
}

// ExtractTextFromImage 模拟从图片中提取文字
func (mock *MockOCRService) ExtractTextFromImage(imagePath string) (string, error) {
	return "", fmt.Errorf("OCR功能未启用：请安装Tesseract-OCR")
}

// ExtractOrganName 模拟从截图中提取器官部位名称
func (mock *MockOCRService) ExtractOrganName(imagePath string) (string, error) {
	// 从文件名中尝试推断器官名称（临时方案）
	fileName := strings.ToLower(imagePath)

	// 简单的关键词匹配
	organKeywords := map[string]string{
		"心":      "心脏",
		"肝":      "肝脏",
		"肺":      "肺",
		"肾":      "肾脏",
		"脾":      "脾脏",
		"胃":      "胃",
		"肠":      "肠",
		"胆":      "胆囊",
		"胰":      "胰腺",
		"brain":  "脑部",
		"heart":  "心脏",
		"liver":  "肝脏",
		"lung":   "肺",
		"kidney": "肾脏",
	}

	for keyword, organName := range organKeywords {
		if strings.Contains(fileName, keyword) {
			return organName, nil
		}
	}

	return "未知器官", fmt.Errorf("OCR功能未启用，无法识别器官名称")
}

// GetOrganNameWithConfidence 模拟获取器官名称及置信度
func (mock *MockOCRService) GetOrganNameWithConfidence(imagePath string) (string, float64, error) {
	organName, err := mock.ExtractOrganName(imagePath)
	return organName, 0.1, err // 低置信度
}

// ValidateOCREnvironment 验证OCR环境
func (mock *MockOCRService) ValidateOCREnvironment() error {
	return fmt.Errorf("OCR功能未启用：请安装Tesseract-OCR和相关依赖库")
}

// ProcessImageWithDetails 模拟处理图片并返回详细结果
func (mock *MockOCRService) ProcessImageWithDetails(imagePath string) (*OCRResult, error) {
	startTime := time.Now()

	organName, confidence, err := mock.GetOrganNameWithConfidence(imagePath)
	if err != nil {
		return nil, err
	}

	result := &OCRResult{
		FullText:    "",
		OrganName:   organName,
		Confidence:  confidence,
		ProcessTime: time.Since(startTime),
		ImagePath:   imagePath,
	}

	return result, fmt.Errorf("OCR功能未启用：这是模拟结果")
}
