package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strings"
)

// PaddleOCR解决方案
type PaddleOCRService struct {
	pythonPath string
	tempDir    string
}

// 创建PaddleOCR服务
func NewPaddleOCRService() *PaddleOCRService {
	return &PaddleOCRService{
		pythonPath: "python", // 可以配置为具体的Python路径
		tempDir:    "temp",
	}
}

// 检查PaddleOCR环境
func (p *PaddleOCRService) CheckEnvironment() error {
	fmt.Println("=== 检查PaddleOCR环境 ===")
	
	// 检查Python
	cmd := exec.Command(p.pythonPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("Python未安装或不在PATH中: %v", err)
	}
	fmt.Printf("✅ Python版本: %s", string(output))
	
	// 检查PaddleOCR
	cmd = exec.Command(p.pythonPath, "-c", "import paddleocr; print('PaddleOCR版本:', paddleocr.__version__)")
	output, err = cmd.Output()
	if err != nil {
		fmt.Println("❌ PaddleOCR未安装")
		fmt.Println("请运行以下命令安装:")
		fmt.Println("pip install paddlepaddle paddleocr")
		return fmt.Errorf("PaddleOCR未安装: %v", err)
	}
	fmt.Printf("✅ %s", string(output))
	
	return nil
}

// 使用PaddleOCR识别图片
func (p *PaddleOCRService) RecognizeImage(imagePath string) ([]OCRResult, error) {
	fmt.Printf("=== 使用PaddleOCR识别图片 ===\n")
	fmt.Printf("图片路径: %s\n", imagePath)
	
	// 创建Python脚本
	script := `
import sys
import json
from paddleocr import PaddleOCR
import os

try:
    # 初始化PaddleOCR
    ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
    
    # 识别图片
    image_path = sys.argv[1]
    if not os.path.exists(image_path):
        print(json.dumps({"error": "图片文件不存在"}))
        sys.exit(1)
    
    result = ocr.ocr(image_path, cls=True)
    
    # 处理结果
    texts = []
    for idx in range(len(result)):
        res = result[idx]
        if res:
            for line in res:
                bbox = line[0]  # 边界框坐标
                text_info = line[1]  # (文本, 置信度)
                text = text_info[0]
                confidence = text_info[1]
                
                texts.append({
                    "text": text,
                    "confidence": confidence,
                    "bbox": bbox
                })
    
    print(json.dumps(texts, ensure_ascii=False))
    
except Exception as e:
    print(json.dumps({"error": str(e)}))
    sys.exit(1)
`
	
	// 创建临时目录
	os.MkdirAll(p.tempDir, 0755)
	
	// 写入临时Python文件
	tmpFile := fmt.Sprintf("%s/paddle_ocr_temp.py", p.tempDir)
	err := os.WriteFile(tmpFile, []byte(script), 0644)
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tmpFile)
	
	// 执行Python脚本
	fmt.Println("正在执行OCR识别...")
	cmd := exec.Command(p.pythonPath, tmpFile, imagePath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("PaddleOCR执行失败: %v", err)
	}
	
	// 解析JSON结果
	var results []OCRResult
	err = json.Unmarshal(output, &results)
	if err != nil {
		// 检查是否是错误信息
		var errorResult map[string]string
		if json.Unmarshal(output, &errorResult) == nil {
			if errorMsg, exists := errorResult["error"]; exists {
				return nil, fmt.Errorf("PaddleOCR错误: %s", errorMsg)
			}
		}
		return nil, fmt.Errorf("解析OCR结果失败: %v\n原始输出: %s", err, string(output))
	}
	
	fmt.Printf("✅ OCR识别完成，共识别到 %d 个文本块\n", len(results))
	return results, nil
}

// OCR识别结果
type OCRResult struct {
	Text       string      `json:"text"`
	Confidence float64     `json:"confidence"`
	BBox       [][]float64 `json:"bbox"`
}

// 分析OCR结果并提取器官名称
func (p *PaddleOCRService) AnalyzeResults(results []OCRResult) {
	fmt.Printf("\n=== OCR结果分析 ===\n")
	
	foundTarget := false
	
	for i, result := range results {
		fmt.Printf("文本块 %d:\n", i+1)
		fmt.Printf("  内容: [%s]\n", result.Text)
		fmt.Printf("  置信度: %.2f%%\n", result.Confidence*100)
		
		// 检查是否包含目标模式
		if containsTargetPattern(result.Text) {
			fmt.Printf("  🎯 发现目标模式!\n")
			foundTarget = true
			
			// 提取器官名称
			organName := extractOrganFromText(result.Text)
			fmt.Printf("  提取器官: [%s]\n", organName)
			
			// 应用校正
			correctedOrgan := applyOCRCorrection(organName)
			if correctedOrgan != organName {
				fmt.Printf("  校正结果: [%s]\n", correctedOrgan)
			}
			
			// 验证结果
			if correctedOrgan == "腹部第1腰椎水平截面" {
				fmt.Printf("  ✅ 识别结果正确!\n")
			} else if correctedOrgan != "未知器官" {
				fmt.Printf("  ⚠️  识别结果需要验证: %s\n", correctedOrgan)
			}
		}
		fmt.Println()
	}
	
	if !foundTarget {
		fmt.Printf("❌ 未找到包含 '0.000' 模式的文本\n")
		fmt.Printf("建议:\n")
		fmt.Printf("1. 检查图片是否包含期望的内容\n")
		fmt.Printf("2. 尝试裁剪不同的区域\n")
		fmt.Printf("3. 提高图片分辨率\n")
	}
}

// 检查是否包含目标模式
func containsTargetPattern(text string) bool {
	patterns := []string{
		"0.000",
		"O.000",
		"0.00O",
		"0.0O0",
	}
	
	for _, pattern := range patterns {
		if strings.Contains(text, pattern) {
			return true
		}
	}
	return false
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	if !containsTargetPattern(text) {
		return "未知器官"
	}
	
	text = strings.TrimSpace(text)
	
	// 修正常见的数字误识别
	text = strings.ReplaceAll(text, "O.000", "0.000")
	text = strings.ReplaceAll(text, "0.00O", "0.000")
	text = strings.ReplaceAll(text, "0.0O0", "0.000")
	
	// 使用正则表达式提取器官名称
	pattern := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	matches := pattern.FindStringSubmatch(text)
	
	if len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		return organName
	}
	
	return "未知器官"
}

// 应用OCR校正规则
func applyOCRCorrection(organName string) string {
	corrections := map[string]string{
		"胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"胆部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
		"服部第1腰椎水平截面": "腹部第1腰椎水平截面",
		"复部第1腰椎水平截面": "腹部第1腰椎水平截面",
	}
	
	if corrected, exists := corrections[organName]; exists {
		fmt.Printf("    🔧 校正应用: %s → %s\n", organName, corrected)
		return corrected
	}
	
	return organName
}

// 完整的OCR识别流程
func performCompleteOCR(imagePath string) {
	fmt.Println("=== PaddleOCR完整识别流程 ===")
	
	// 创建OCR服务
	ocrService := NewPaddleOCRService()
	
	// 检查环境
	if err := ocrService.CheckEnvironment(); err != nil {
		fmt.Printf("环境检查失败: %v\n", err)
		fmt.Println("\n=== 安装指南 ===")
		fmt.Println("1. 安装Python (如果未安装)")
		fmt.Println("2. 安装PaddleOCR:")
		fmt.Println("   pip install paddlepaddle paddleocr")
		fmt.Println("3. 重新运行程序")
		return
	}
	
	// 执行OCR识别
	results, err := ocrService.RecognizeImage(imagePath)
	if err != nil {
		fmt.Printf("OCR识别失败: %v\n", err)
		return
	}
	
	// 分析结果
	ocrService.AnalyzeResults(results)
	
	fmt.Println("\n=== 总结 ===")
	fmt.Println("✅ PaddleOCR识别完成")
	fmt.Println("✅ 结果分析完成")
	fmt.Println("✅ 校正机制已应用")
}

func main() {
	imagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`
	
	// 检查文件
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 图片文件不存在\n")
			return
		}
		imagePath = relativePath
	}
	
	fmt.Printf("✅ 找到图片: %s\n", imagePath)
	
	// 执行完整OCR流程
	performCompleteOCR(imagePath)
}
