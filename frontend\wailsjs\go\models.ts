export namespace models {
	
	export class CloudFunctionConfig {
	    registrations_url: string;
	    screenshot_records_url: string;
	    siteInfoByDeviceMAC_url: string;
	
	    static createFrom(source: any = {}) {
	        return new CloudFunctionConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.registrations_url = source["registrations_url"];
	        this.screenshot_records_url = source["screenshot_records_url"];
	        this.siteInfoByDeviceMAC_url = source["siteInfoByDeviceMAC_url"];
	    }
	}
	export class CozeConfig {
	    token: string;
	    workflow_id_post_pic: string;
	    workflow_id_post_registration: string;
	    workflow_id_user_info: string;
	    space_id: string;
	    app_id: string;
	
	    static createFrom(source: any = {}) {
	        return new CozeConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.token = source["token"];
	        this.workflow_id_post_pic = source["workflow_id_post_pic"];
	        this.workflow_id_post_registration = source["workflow_id_post_registration"];
	        this.workflow_id_user_info = source["workflow_id_user_info"];
	        this.space_id = source["space_id"];
	        this.app_id = source["app_id"];
	    }
	}
	export class DcloudConfig {
	    access_key: string;
	    secret_key: string;
	    upload_url: string;
	    space_id: string;
	
	    static createFrom(source: any = {}) {
	        return new DcloudConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.access_key = source["access_key"];
	        this.secret_key = source["secret_key"];
	        this.upload_url = source["upload_url"];
	        this.space_id = source["space_id"];
	    }
	}
	export class APIKeys {
	    dcloud: DcloudConfig;
	    coze: CozeConfig;
	    cloud_function: CloudFunctionConfig;
	
	    static createFrom(source: any = {}) {
	        return new APIKeys(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.dcloud = this.convertValues(source["dcloud"], DcloudConfig);
	        this.coze = this.convertValues(source["coze"], CozeConfig);
	        this.cloud_function = this.convertValues(source["cloud_function"], CloudFunctionConfig);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ConfigUserInfo {
	    name: string;
	    birth: string;
	    id_number: string;
	
	    static createFrom(source: any = {}) {
	        return new ConfigUserInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.birth = source["birth"];
	        this.id_number = source["id_number"];
	    }
	}
	export class DeviceInfo {
	    mac_address: string;
	    device_name: string;
	
	    static createFrom(source: any = {}) {
	        return new DeviceInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.mac_address = source["mac_address"];
	        this.device_name = source["device_name"];
	    }
	}
	export class CropSettings {
	    top_percent: number;
	    bottom_percent: number;
	    left_percent: number;
	    right_percent: number;
	
	    static createFrom(source: any = {}) {
	        return new CropSettings(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.top_percent = source["top_percent"];
	        this.bottom_percent = source["bottom_percent"];
	        this.left_percent = source["left_percent"];
	        this.right_percent = source["right_percent"];
	    }
	}
	export class Contact {
	    manager: string;
	    phone: string;
	
	    static createFrom(source: any = {}) {
	        return new Contact(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.manager = source["manager"];
	        this.phone = source["phone"];
	    }
	}
	export class Location {
	    province: string;
	    city: string;
	    district: string;
	    address: string;
	
	    static createFrom(source: any = {}) {
	        return new Location(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.province = source["province"];
	        this.city = source["city"];
	        this.district = source["district"];
	        this.address = source["address"];
	    }
	}
	export class SiteInfo {
	    site_id: string;
	    site_name: string;
	    site_type: string;
	    parent_org: string;
	    location: Location;
	    contact: Contact;
	
	    static createFrom(source: any = {}) {
	        return new SiteInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.site_id = source["site_id"];
	        this.site_name = source["site_name"];
	        this.site_type = source["site_type"];
	        this.parent_org = source["parent_org"];
	        this.location = this.convertValues(source["location"], Location);
	        this.contact = this.convertValues(source["contact"], Contact);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class MpAppInfo {
	    appid: string;
	    target_page: string;
	
	    static createFrom(source: any = {}) {
	        return new MpAppInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.appid = source["appid"];
	        this.target_page = source["target_page"];
	    }
	}
	export class AppConfig {
	    mp_app_info: MpAppInfo;
	    site_info: SiteInfo;
	    crop_settings: CropSettings;
	    api_keys: APIKeys;
	    device_info: DeviceInfo;
	    user_info: ConfigUserInfo;
	
	    static createFrom(source: any = {}) {
	        return new AppConfig(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.mp_app_info = this.convertValues(source["mp_app_info"], MpAppInfo);
	        this.site_info = this.convertValues(source["site_info"], SiteInfo);
	        this.crop_settings = this.convertValues(source["crop_settings"], CropSettings);
	        this.api_keys = this.convertValues(source["api_keys"], APIKeys);
	        this.device_info = this.convertValues(source["device_info"], DeviceInfo);
	        this.user_info = this.convertValues(source["user_info"], ConfigUserInfo);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	
	
	
	
	
	
	
	export class Patient {
	    name: string;
	    registration_code: string;
	    full_code: string;
	    register_time: string;
	
	    static createFrom(source: any = {}) {
	        return new Patient(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.registration_code = source["registration_code"];
	        this.full_code = source["full_code"];
	        this.register_time = source["register_time"];
	    }
	}
	export class UserInfo {
	    name: string;
	    birthday: string;
	    age: number;
	    gender: number;
	    id_number: string;
	
	    static createFrom(source: any = {}) {
	        return new UserInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.birthday = source["birthday"];
	        this.age = source["age"];
	        this.gender = source["gender"];
	        this.id_number = source["id_number"];
	    }
	}
	export class Registration {
	    _id: string;
	    user_id: string;
	    device_no: string;
	    site_id: string;
	    registration_number: string;
	    registration_time: number;
	    registration_date: string;
	    userInfo: UserInfo[];
	    name?: string;
	    number?: string;
	    register_time?: string;
	
	    static createFrom(source: any = {}) {
	        return new Registration(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this._id = source["_id"];
	        this.user_id = source["user_id"];
	        this.device_no = source["device_no"];
	        this.site_id = source["site_id"];
	        this.registration_number = source["registration_number"];
	        this.registration_time = source["registration_time"];
	        this.registration_date = source["registration_date"];
	        this.userInfo = this.convertValues(source["userInfo"], UserInfo);
	        this.name = source["name"];
	        this.number = source["number"];
	        this.register_time = source["register_time"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	

}

