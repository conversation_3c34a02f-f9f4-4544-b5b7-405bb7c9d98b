<template>
  <div class="log-panel">
    <div class="panel-header">
      <h3>系统日志</h3>
      <div class="header-actions">
        <button @click="refreshLogs" class="refresh-btn">
          🔄 刷新
        </button>
        <button @click="clearLogs" class="clear-btn">
          🗑️ 清空
        </button>
        <button @click="exportLogs" class="export-btn">
          📤 导出
        </button>
      </div>
    </div>
    
    <div class="panel-content">
      <!-- 日志过滤器 -->
      <div class="log-filters">
        <div class="filter-row">
          <div class="filter-group">
            <label>日志级别:</label>
            <select v-model="levelFilter" class="filter-select">
              <option value="">全部级别</option>
              <option value="debug">调试</option>
              <option value="info">信息</option>
              <option value="warning">警告</option>
              <option value="error">错误</option>
            </select>
          </div>
          
          <div class="filter-group">
            <label>日志类型:</label>
            <select v-model="typeFilter" class="filter-select">
              <option value="">全部类型</option>
              <option value="operation">操作日志</option>
              <option value="system">系统日志</option>
              <option value="api">API调用</option>
              <option value="screenshot">截图操作</option>
              <option value="patient">患者管理</option>
            </select>
          </div>
          
          <div class="filter-group">
            <label>时间范围:</label>
            <select v-model="timeFilter" class="filter-select">
              <option value="">全部时间</option>
              <option value="today">今天</option>
              <option value="yesterday">昨天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
            </select>
          </div>
        </div>
        
        <div class="search-row">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索日志内容..."
            class="search-input"
          />
          <button @click="searchLogs" class="search-btn">
            🔍 搜索
          </button>
        </div>
      </div>
      
      <!-- 日志统计 -->
      <div class="log-statistics">
        <div class="stat-item">
          <span class="stat-label">总日志数:</span>
          <span class="stat-value">{{ totalLogs }}</span>
        </div>
        <div class="stat-item error">
          <span class="stat-label">错误:</span>
          <span class="stat-value">{{ errorCount }}</span>
        </div>
        <div class="stat-item warning">
          <span class="stat-label">警告:</span>
          <span class="stat-value">{{ warningCount }}</span>
        </div>
        <div class="stat-item info">
          <span class="stat-label">信息:</span>
          <span class="stat-value">{{ infoCount }}</span>
        </div>
      </div>
      
      <!-- 日志列表 -->
      <div class="log-list">
        <div v-if="filteredLogs.length === 0" class="empty-state">
          <div class="empty-icon">📝</div>
          <p>暂无日志记录</p>
        </div>
        
        <div v-else class="log-items">
          <div 
            v-for="log in paginatedLogs" 
            :key="log.id"
            class="log-item"
            :class="log.level"
          >
            <div class="log-header">
              <div class="log-level">
                <span class="level-badge" :class="log.level">
                  {{ getLevelIcon(log.level) }} {{ getLevelText(log.level) }}
                </span>
              </div>
              
              <div class="log-time">
                {{ formatTime(log.timestamp) }}
              </div>
              
              <div class="log-type">
                <span class="type-badge" :class="log.type">
                  {{ getTypeText(log.type) }}
                </span>
              </div>
            </div>
            
            <div class="log-content">
              <div class="log-message">
                {{ log.message }}
              </div>
              
              <div v-if="log.details" class="log-details">
                <button 
                  @click="toggleDetails(log.id)" 
                  class="details-toggle"
                >
                  {{ expandedLogs.includes(log.id) ? '收起详情' : '展开详情' }}
                </button>
                
                <div v-if="expandedLogs.includes(log.id)" class="details-content">
                  <pre>{{ formatDetails(log.details) }}</pre>
                </div>
              </div>
              
              <div v-if="log.stackTrace" class="log-stack">
                <button 
                  @click="toggleStack(log.id)" 
                  class="stack-toggle"
                >
                  {{ expandedStacks.includes(log.id) ? '隐藏堆栈' : '显示堆栈' }}
                </button>
                
                <div v-if="expandedStacks.includes(log.id)" class="stack-content">
                  <pre>{{ log.stackTrace }}</pre>
                </div>
              </div>
            </div>
            
            <div class="log-actions">
              <button @click="copyLog(log)" class="action-btn copy">
                📋 复制
              </button>
              <button @click="deleteLog(log.id)" class="action-btn delete">
                🗑️ 删除
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页控制 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="currentPage = Math.max(1, currentPage - 1)"
          :disabled="currentPage === 1"
          class="page-btn"
        >
          ‹ 上一页
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in visiblePages" 
            :key="page"
            @click="currentPage = page"
            class="page-number"
            :class="{ active: page === currentPage }"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          下一页 ›
        </button>
        
        <div class="page-info">
          共 {{ filteredLogs.length }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
        </div>
      </div>
      
      <!-- 实时日志开关 -->
      <div class="realtime-controls">
        <label class="realtime-toggle">
          <input 
            type="checkbox" 
            v-model="realtimeEnabled"
            @change="toggleRealtime"
          />
          <span class="toggle-slider"></span>
          <span class="toggle-label">实时日志</span>
        </label>
        
        <div class="auto-scroll">
          <label>
            <input type="checkbox" v-model="autoScroll" />
            自动滚动到底部
          </label>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogPanel',
  data() {
    return {
      logs: [],
      levelFilter: '',
      typeFilter: '',
      timeFilter: '',
      searchQuery: '',
      currentPage: 1,
      pageSize: 50,
      expandedLogs: [],
      expandedStacks: [],
      realtimeEnabled: true,
      autoScroll: true,
      realtimeInterval: null
    }
  },
  computed: {
    filteredLogs() {
      let filtered = [...this.logs]
      
      // 级别过滤
      if (this.levelFilter) {
        filtered = filtered.filter(log => log.level === this.levelFilter)
      }
      
      // 类型过滤
      if (this.typeFilter) {
        filtered = filtered.filter(log => log.type === this.typeFilter)
      }
      
      // 时间过滤
      if (this.timeFilter) {
        const now = new Date()
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        
        filtered = filtered.filter(log => {
          const logDate = new Date(log.timestamp)
          
          switch (this.timeFilter) {
            case 'today':
              return logDate >= today
            case 'yesterday':
              const yesterday = new Date(today)
              yesterday.setDate(yesterday.getDate() - 1)
              return logDate >= yesterday && logDate < today
            case 'week':
              const weekStart = new Date(today)
              weekStart.setDate(weekStart.getDate() - weekStart.getDay())
              return logDate >= weekStart
            case 'month':
              const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
              return logDate >= monthStart
            default:
              return true
          }
        })
      }
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(log => 
          log.message.toLowerCase().includes(query) ||
          (log.details && JSON.stringify(log.details).toLowerCase().includes(query))
        )
      }
      
      // 按时间倒序排列
      return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    },
    
    paginatedLogs() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.filteredLogs.slice(start, start + this.pageSize)
    },
    
    totalPages() {
      return Math.ceil(this.filteredLogs.length / this.pageSize)
    },
    
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    },
    
    totalLogs() {
      return this.logs.length
    },
    
    errorCount() {
      return this.logs.filter(log => log.level === 'error').length
    },
    
    warningCount() {
      return this.logs.filter(log => log.level === 'warning').length
    },
    
    infoCount() {
      return this.logs.filter(log => log.level === 'info').length
    }
  },
  mounted() {
    this.loadLogs()
    this.startRealtime()
  },
  beforeUnmount() {
    this.stopRealtime()
  },
  methods: {
    async loadLogs() {
      try {
        // 模拟加载日志数据
        this.logs = [
          {
            id: 1,
            level: 'info',
            type: 'operation',
            message: '应用程序启动成功',
            timestamp: new Date(),
            details: {
              version: '1.0.0',
              startTime: new Date().toISOString()
            }
          },
          {
            id: 2,
            level: 'info',
            type: 'screenshot',
            message: '截图操作完成',
            timestamp: new Date(Date.now() - 60000),
            details: {
              filePath: 'pic/screenshot_20231201_143022.png',
              fileSize: '2.1MB',
              resolution: '1920x1080'
            }
          },
          {
            id: 3,
            level: 'warning',
            type: 'api',
            message: 'API调用响应时间较长',
            timestamp: new Date(Date.now() - 120000),
            details: {
              endpoint: '/api/upload',
              responseTime: '5.2s',
              statusCode: 200
            }
          },
          {
            id: 4,
            level: 'error',
            type: 'system',
            message: '网络连接失败',
            timestamp: new Date(Date.now() - 180000),
            details: {
              error: 'Connection timeout',
              retryCount: 3
            },
            stackTrace: 'Error: Connection timeout\n    at fetch (http.go:123)\n    at uploadImage (api.go:45)'
          }
        ]
      } catch (error) {
        console.error('加载日志失败:', error)
      }
    },
    
    refreshLogs() {
      this.loadLogs()
      this.showMessage('日志已刷新', 'success')
    },
    
    async clearLogs() {
      if (confirm('确定要清空所有日志吗？此操作不可恢复！')) {
        try {
          this.logs = []
          this.expandedLogs = []
          this.expandedStacks = []
          this.currentPage = 1
          this.showMessage('日志已清空', 'success')
        } catch (error) {
          this.showMessage('清空日志失败', 'error')
        }
      }
    },
    
    async exportLogs() {
      try {
        const logsToExport = this.filteredLogs.length > 0 ? this.filteredLogs : this.logs
        const csvContent = this.generateLogCSV(logsToExport)
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `系统日志_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`
        link.click()
        
        this.showMessage('日志已导出', 'success')
      } catch (error) {
        this.showMessage('导出日志失败', 'error')
      }
    },
    
    generateLogCSV(logs) {
      const headers = ['时间', '级别', '类型', '消息', '详情']
      const rows = logs.map(log => [
        this.formatTime(log.timestamp),
        this.getLevelText(log.level),
        this.getTypeText(log.type),
        log.message,
        log.details ? JSON.stringify(log.details) : ''
      ])
      
      return [headers, ...rows].map(row => 
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
      ).join('\n')
    },
    
    searchLogs() {
      this.currentPage = 1
    },
    
    toggleDetails(logId) {
      const index = this.expandedLogs.indexOf(logId)
      if (index > -1) {
        this.expandedLogs.splice(index, 1)
      } else {
        this.expandedLogs.push(logId)
      }
    },
    
    toggleStack(logId) {
      const index = this.expandedStacks.indexOf(logId)
      if (index > -1) {
        this.expandedStacks.splice(index, 1)
      } else {
        this.expandedStacks.push(logId)
      }
    },
    
    copyLog(log) {
      const logText = `[${this.formatTime(log.timestamp)}] [${this.getLevelText(log.level)}] [${this.getTypeText(log.type)}] ${log.message}`
      
      navigator.clipboard.writeText(logText).then(() => {
        this.showMessage('日志已复制到剪贴板', 'success')
      }).catch(() => {
        this.showMessage('复制失败', 'error')
      })
    },
    
    deleteLog(logId) {
      if (confirm('确定要删除这条日志吗？')) {
        this.logs = this.logs.filter(log => log.id !== logId)
        this.expandedLogs = this.expandedLogs.filter(id => id !== logId)
        this.expandedStacks = this.expandedStacks.filter(id => id !== logId)
        this.showMessage('日志已删除', 'success')
      }
    },
    
    toggleRealtime() {
      if (this.realtimeEnabled) {
        this.startRealtime()
      } else {
        this.stopRealtime()
      }
    },
    
    startRealtime() {
      if (this.realtimeInterval) return
      
      this.realtimeInterval = setInterval(() => {
        // 模拟新日志
        if (Math.random() < 0.3) {
          this.addNewLog()
        }
      }, 5000)
    },
    
    stopRealtime() {
      if (this.realtimeInterval) {
        clearInterval(this.realtimeInterval)
        this.realtimeInterval = null
      }
    },
    
    addNewLog() {
      const levels = ['info', 'warning', 'error', 'debug']
      const types = ['operation', 'system', 'api', 'screenshot', 'patient']
      const messages = [
        '用户操作记录',
        '系统状态检查',
        'API调用完成',
        '截图保存成功',
        '患者信息更新'
      ]
      
      const newLog = {
        id: Date.now(),
        level: levels[Math.floor(Math.random() * levels.length)],
        type: types[Math.floor(Math.random() * types.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        timestamp: new Date(),
        details: {
          randomData: Math.random().toString(36).substring(7)
        }
      }
      
      this.logs.unshift(newLog)
      
      // 限制日志数量
      if (this.logs.length > 1000) {
        this.logs = this.logs.slice(0, 1000)
      }
      
      // 自动滚动
      if (this.autoScroll && this.currentPage === 1) {
        this.$nextTick(() => {
          const logList = this.$el.querySelector('.log-list')
          if (logList) {
            logList.scrollTop = 0
          }
        })
      }
    },
    
    getLevelIcon(level) {
      const icons = {
        debug: '🐛',
        info: 'ℹ️',
        warning: '⚠️',
        error: '❌'
      }
      return icons[level] || 'ℹ️'
    },
    
    getLevelText(level) {
      const texts = {
        debug: '调试',
        info: '信息',
        warning: '警告',
        error: '错误'
      }
      return texts[level] || level
    },
    
    getTypeText(type) {
      const texts = {
        operation: '操作',
        system: '系统',
        api: 'API',
        screenshot: '截图',
        patient: '患者'
      }
      return texts[type] || type
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString()
    },
    
    formatDetails(details) {
      return JSON.stringify(details, null, 2)
    },
    
    showMessage(message, type = 'info') {
      console.log(`[${type}] ${message}`)
    }
  }
}
</script>

<style scoped>
.log-panel {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  background: #f8f9fa;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.refresh-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.export-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.log-filters {
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  min-width: 100px;
}

.search-row {
  display: flex;
  gap: 8px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
}

.search-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.log-statistics {
  display: flex;
  gap: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-shrink: 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.stat-item.error .stat-value {
  color: #dc3545;
}

.stat-item.warning .stat-value {
  color: #ffc107;
}

.stat-item.info .stat-value {
  color: #17a2b8;
}

.log-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.log-items {
  display: flex;
  flex-direction: column;
}

.log-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.log-item:hover {
  background: #f8f9fa;
}

.log-item.error {
  border-left: 4px solid #dc3545;
}

.log-item.warning {
  border-left: 4px solid #ffc107;
}

.log-item.info {
  border-left: 4px solid #17a2b8;
}

.log-item.debug {
  border-left: 4px solid #6c757d;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.level-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.level-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.level-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.level-badge.info {
  background: #d1ecf1;
  color: #0c5460;
}

.level-badge.debug {
  background: #e2e3e5;
  color: #383d41;
}

.log-time {
  font-size: 11px;
  color: #666;
  font-family: monospace;
}

.type-badge {
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  background: #e9ecef;
  color: #495057;
}

.log-content {
  margin-bottom: 8px;
}

.log-message {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
}

.details-toggle,
.stack-toggle {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 11px;
  text-decoration: underline;
  padding: 0;
  margin-right: 8px;
}

.details-content,
.stack-content {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #3498db;
}

.details-content pre,
.stack-content pre {
  margin: 0;
  font-size: 11px;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e9ecef;
}

.action-btn.copy:hover {
  background: #d1ecf1;
  border-color: #17a2b8;
}

.action-btn.delete:hover {
  background: #f8d7da;
  border-color: #dc3545;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.page-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  min-width: 32px;
  text-align: center;
}

.page-number.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.page-info {
  font-size: 11px;
  color: #666;
  margin-left: 12px;
}

.realtime-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.realtime-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 12px;
}

.realtime-toggle input {
  display: none;
}

.toggle-slider {
  width: 40px;
  height: 20px;
  background: #ccc;
  border-radius: 10px;
  position: relative;
  transition: background-color 0.2s;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

.realtime-toggle input:checked + .toggle-slider {
  background: #28a745;
}

.realtime-toggle input:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.auto-scroll {
  font-size: 12px;
  color: #666;
}

.auto-scroll input {
  margin-right: 4px;
}
</style>