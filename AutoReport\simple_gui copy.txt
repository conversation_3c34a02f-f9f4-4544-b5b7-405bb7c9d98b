package main

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func initGUI() {
	a := app.New()
	w := a.<PERSON>indow("健康检测系统")

	// 获取屏幕尺寸
	screen := a.Driver().AllWindows()[0].Canvas().Size()
	defaultWidth := float32(float64(screen.Width) * 0.049)
	expandedWidth := float32(float64(screen.Width) * 0.3)
	height := float32(float64(screen.Height) * 0.5)

	// 初始窗口大小
	w.Resize(fyne.NewSize(defaultWidth, height))

	// 创建切换按钮
	var toggleBtn *widget.Button
	toggleBtn = widget.NewButton(">", func() {
		currentSize := w.Content().Size()
		if currentSize.Width == defaultWidth {
			w.Resize(fyne.NewSize(expandedWidth, height))
			toggleBtn.SetText("<")
		} else {
			w.Resize(fyne.NewSize(defaultWidth, height))
			toggleBtn.SetText(">")
		}
	})

	siteIDEntry := widget.NewEntry()
	siteNameEntry := widget.NewEntry()
	siteTypeSelect := widget.NewSelect([]string{"类型1", "类型2", "类型3"}, func(selected string) {})

	siteForm := container.NewVBox(
		widget.NewLabel("站点ID"),
		siteIDEntry,
		widget.NewLabel("站点名称"),
		siteNameEntry,
		widget.NewLabel("站点类型"),
		siteTypeSelect,
		widget.NewButton("保存", func() {}),
	)

	userNameEntry := widget.NewEntry()
	userBirthdayEntry := widget.NewDateEntry()
	userGenderSelect := widget.NewRadioGroup([]string{"男", "女"}, func(selected string) {})

	userForm := container.NewVBox(
		widget.NewLabel("姓名"),
		userNameEntry,
		widget.NewLabel("生日"),
		userBirthdayEntry,
		widget.NewLabel("性别"),
		userGenderSelect,
		widget.NewButton("保存", func() {}),
	)

	modeSelect := widget.NewSelect([]string{"A", "B", "C"}, func(selected string) {})
	screenshotButton := widget.NewButton("截图", func() {})
	preview := widget.NewLabel("截图预览区域")

	screenshotTab := container.NewVBox(
		widget.NewLabel("截图模式"),
		modeSelect,
		screenshotButton,
		widget.NewLabel("最近一次截图预览"),
		preview,
	)

	cropSetting := widget.NewCheck("启用裁剪", func(b bool) {})
	apiKeyEntry := widget.NewEntry()
	apiKeyEntry.SetPlaceHolder("请输入API密钥")
	settingTab := container.NewVBox(
		widget.NewLabel("裁剪设置"),
		cropSetting,
		widget.NewLabel("API密钥"),
		apiKeyEntry,
	)

	tabs := container.NewAppTabs(
		container.NewTabItem("站点信息管理", siteForm),
		container.NewTabItem("用户信息管理", userForm),
		container.NewTabItem("截图与分析控制", screenshotTab),
		container.NewTabItem("设置", settingTab),
	)

	// 将切换按钮和标签页组合
	content := container.NewBorder(nil, nil, toggleBtn, nil, tabs)
	w.SetContent(content)

	w.ShowAndRun()
}

// 修改后的代码已包含窗口大小切换功能
