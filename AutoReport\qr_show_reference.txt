桌面 GUI 端显示二维码
在桌面 GUI 端（Go 语言，使用 fyne 库），显示生成的二维码：
package main

import (
	"image/png"
	"bytes"
	"io/ioutil"
	"fmt"
	"time"
	"encoding/base64"
	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// 配置信息
type Config struct {
	SiteID    string
	MACAddress string
}

// 生成挂号码
func GenerateFullNumber(config Config) string {
	siteID := config.SiteID
	if siteID == "" {
		siteID = "UNKNOWN"
	}

	macAddress := config.MACAddress
	if macAddress == "" {
		macAddress = "UNKNOWN-MAC"
	}

	timestamp := time.Now().Format("20060102150405")
	fullNumber := fmt.Sprintf("%s-%s-%s", siteID, macAddress, timestamp)
	return fullNumber
}

// 生成健康检测码（二维码）
func GenerateHealthQRCode(fullNumber string) ([]byte, error) {
	qrCode, err := qr.Encode(fullNumber, qr.EcLevelL)
	if err != nil {
		return nil, err
	}

	img, err := barcode.Image(qrCode, barcode.FgBlack, barcode.BgWhite, 20)
	if err != nil {
		return nil, err
	}

	buf := new(bytes.Buffer)
	if err := png.Encode(buf, img); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func main() {
	config := Config{
		SiteID: "SITE123",
		MACAddress: "00:1A:2B:3C:4D:5E",
	}

	fullNumber := GenerateFullNumber(config)
	qrBytes, err := GenerateHealthQRCode(fullNumber)
	if err != nil {
		fmt.Println("生成二维码失败:", err)
		return
	}

	// 创建应用程序窗口
	a := app.New()
	w := a.NewWindow("健康检测码生成器")

	// 将二维码图片显示在 GUI 界面上
	img := canvas.NewImageFromReader(bytes.NewReader(qrBytes))
	img.FillMode = canvas.ImageFillOriginal

	// 创建布局
	content := container.NewVBox(
		widget.NewLabel("健康检测码"),
		img,
		widget.NewLabel("检测网点ID: " + config.SiteID),
		widget.NewLabel("设备MAC: " + config.MACAddress),
		widget.NewLabel("生成时间: " + time.Now().Format("2006-01-02 15:04:05")),
	)

	w.SetContent(content)
	w.Resize(fyne.NewSize(400, 400))
	w.ShowAndRun()
}