package main

import (
	"fmt"
	"image"
	"image/png"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/kbinani/screenshot"
	"github.com/otiai10/gosseract/v2"
)

// 模拟OCR服务结构
type TestOCRService struct {
	client *gosseract.Client
}

// 创建测试OCR服务
func NewTestOCRService() *TestOCRService {
	client := gosseract.NewClient()
	// 设置中文和英文语言
	client.SetLanguage("chi_sim+eng")
	// 设置OCR引擎模式
	client.SetPageSegMode(gosseract.PSM_AUTO)

	return &TestOCRService{
		client: client,
	}
}

// 关闭OCR服务
func (tos *TestOCRService) Close() {
	if tos.client != nil {
		tos.client.Close()
	}
}

// 截取左上角1/4区域
func (tos *TestOCRService) captureTopLeftQuarter(imagePath string) (string, error) {
	// 如果提供了图片路径，直接处理该图片
	if imagePath != "" {
		return tos.cropTopLeftQuarter(imagePath)
	}

	// 否则截取屏幕的左上角1/4
	bounds := screenshot.GetDisplayBounds(0)

	// 计算左上角1/4的区域
	quarterWidth := bounds.Dx() / 4
	quarterHeight := bounds.Dy() / 4

	cropBounds := image.Rect(0, 0, quarterWidth, quarterHeight)

	// 截取指定区域
	img, err := screenshot.CaptureRect(cropBounds)
	if err != nil {
		return "", fmt.Errorf("截取屏幕失败: %v", err)
	}

	// 保存截图
	fileName := "test_ocr_quarter.png"
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	os.MkdirAll("pic", 0755)

	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	err = png.Encode(file, img)
	if err != nil {
		return "", fmt.Errorf("保存图片失败: %v", err)
	}

	return filePath, nil
}

// 裁剪现有图片的左上角1/4区域
func (tos *TestOCRService) cropTopLeftQuarter(imagePath string) (string, error) {
	// 打开原图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图片失败: %v", err)
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算左上角1/4区域
	quarterWidth := width / 4
	quarterHeight := height / 4

	// 创建裁剪后的图片
	croppedImg := image.NewRGBA(image.Rect(0, 0, quarterWidth, quarterHeight))

	// 复制像素
	for y := 0; y < quarterHeight; y++ {
		for x := 0; x < quarterWidth; x++ {
			croppedImg.Set(x, y, img.At(x, y))
		}
	}

	// 保存裁剪后的图片
	fileName := "test_ocr_quarter_cropped.png"
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	os.MkdirAll("pic", 0755)

	outputFile, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	err = png.Encode(outputFile, croppedImg)
	if err != nil {
		return "", fmt.Errorf("保存裁剪图片失败: %v", err)
	}

	return filePath, nil
}

// 从图片中提取文本
func (tos *TestOCRService) extractTextFromImage(imagePath string) (string, error) {
	err := tos.client.SetImage(imagePath)
	if err != nil {
		return "", fmt.Errorf("设置图片失败: %v", err)
	}

	text, err := tos.client.Text()
	if err != nil {
		return "", fmt.Errorf("OCR识别失败: %v", err)
	}

	return text, nil
}

// 从OCR文本中提取器官名称
func (tos *TestOCRService) extractOrganFromText(text string) string {
	fmt.Printf("=== OCR识别的完整文本 ===\n%s\n", text)
	fmt.Printf("=== 文本长度: %d 字符 ===\n", len(text))

	// 按行分割文本
	lines := strings.Split(text, "\n")
	fmt.Printf("=== 共 %d 行文本 ===\n", len(lines))

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fmt.Printf("第%d行: [%s]\n", i+1, line)

		// 查找包含 "0.000" 的行
		if strings.Contains(line, "0.000") {
			fmt.Printf("*** 找到包含 0.000 的行: [%s] ***\n", line)

			// 尝试多种模式提取器官名称
			organName := tos.extractOrganFromLine(line)
			if organName != "" {
				fmt.Printf("*** 提取到器官名称: [%s] ***\n", organName)
				return organName
			}
		}

		// 也检查包含类似数字的行（OCR可能识别错误）
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, line); matched {
			fmt.Printf("*** 找到包含数字模式的行: [%s] ***\n", line)

			organName := tos.extractOrganFromLine(line)
			if organName != "" {
				fmt.Printf("*** 从数字模式行提取到器官名称: [%s] ***\n", organName)
				return organName
			}
		}
	}

	fmt.Printf("*** 未找到器官名称，返回默认值 ***\n")
	return "未知器官"
}

// 从单行文本中提取器官名称
func (tos *TestOCRService) extractOrganFromLine(line string) string {
	// 移除多余的空格
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	fmt.Printf("处理行: [%s]\n", line)

	// 模式1: 数字后直接跟器官名称
	// 例如: "0.000 胆囊第1腰椎水平截面"
	pattern1 := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		fmt.Printf("模式1匹配: [%s]\n", organName)
		return organName
	}

	// 模式2: 表格格式，数字在前，器官名称在后
	// 例如: "| 0.000 | 胆囊第1腰椎水平截面 |"
	pattern2 := regexp.MustCompile(`\|\s*[0-9]\.[0-9]{3}\s*\|\s*(.+?)\s*\|`)
	if matches := pattern2.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		fmt.Printf("模式2匹配: [%s]\n", organName)
		return organName
	}

	// 模式3: 简单分割，取数字后的部分
	parts := strings.Fields(line)
	for i, part := range parts {
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, part); matched {
			if i+1 < len(parts) {
				// 取数字后面的所有部分作为器官名称
				organParts := parts[i+1:]
				organName := strings.Join(organParts, " ")
				fmt.Printf("模式3匹配: [%s]\n", organName)
				return organName
			}
		}
	}

	fmt.Printf("所有模式都未匹配\n")
	return ""
}

// 测试器官提取功能
func (tos *TestOCRService) testOrganExtraction(imagePath string) {
	fmt.Printf("=== 开始测试器官名称提取 ===\n")
	fmt.Printf("输入图片: %s\n", imagePath)

	// 1. 裁剪左上角1/4区域
	fmt.Printf("\n步骤1: 裁剪左上角1/4区域...\n")
	croppedPath, err := tos.captureTopLeftQuarter(imagePath)
	if err != nil {
		fmt.Printf("裁剪失败: %v\n", err)
		return
	}
	fmt.Printf("裁剪完成，保存到: %s\n", croppedPath)

	// 2. OCR识别文本
	fmt.Printf("\n步骤2: OCR识别文本...\n")
	text, err := tos.extractTextFromImage(croppedPath)
	if err != nil {
		fmt.Printf("OCR识别失败: %v\n", err)
		return
	}

	// 3. 提取器官名称
	fmt.Printf("\n步骤3: 提取器官名称...\n")
	organName := tos.extractOrganFromText(text)

	fmt.Printf("\n=== 测试结果 ===\n")
	fmt.Printf("识别到的器官名称: [%s]\n", organName)
	fmt.Printf("裁剪后的图片保存在: %s\n", croppedPath)
	fmt.Printf("=== 测试完成 ===\n")
}

func main() {
	fmt.Println("=== OCR器官名称提取测试程序 ===")

	// 创建OCR服务
	ocrService := NewTestOCRService()
	defer ocrService.Close()

	// 测试指定的图片文件
	testImagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`

	// 检查测试图片是否存在
	if _, err := os.Stat(testImagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 测试图片不存在: %s\n", testImagePath)

		// 尝试相对路径
		relativePath := `pic\医生或健康专家-B02-OCR-20250616_221124.png`
		if _, err := os.Stat(relativePath); os.IsNotExist(err) {
			fmt.Printf("❌ 相对路径也不存在: %s\n", relativePath)
			fmt.Printf("请确认图片文件存在\n")
			return
		} else {
			fmt.Printf("✅ 找到图片文件（相对路径）: %s\n", relativePath)
			testImagePath = relativePath
		}
	} else {
		fmt.Printf("✅ 找到图片文件: %s\n", testImagePath)
	}

	// 显示图片信息
	if fileInfo, err := os.Stat(testImagePath); err == nil {
		fmt.Printf("图片大小: %.2f KB\n", float64(fileInfo.Size())/1024)
		fmt.Printf("修改时间: %s\n", fileInfo.ModTime().Format("2006-01-02 15:04:05"))
	}

	// 开始测试
	fmt.Printf("\n开始测试OCR识别...\n")
	ocrService.testOrganExtraction(testImagePath)
}
