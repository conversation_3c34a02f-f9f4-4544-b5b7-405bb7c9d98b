package main

import (
	"fmt"
	"regexp"
	"strings"
)

// OCR识别准确性分析
func analyzeOCRAccuracy() {
	fmt.Println("=== OCR识别准确性分析 ===")
	fmt.Println("分析为什么 '腹部' 被误识别为 '胆囊'")

	// 实际图片内容 vs 我们测试时使用的内容
	fmt.Println("\n=== 实际内容对比 ===")
	actualText := "0.000 腹部第1腰椎水平截面"
	testText := "0.000 胆囊第1腰椎水平截面"

	fmt.Printf("实际图片内容: [%s]\n", actualText)
	fmt.Printf("测试使用内容: [%s]\n", testText)

	// 分析字符差异
	fmt.Println("\n=== 字符差异分析 ===")
	analyzeCharacterDifferences("腹部", "胆囊")

	// 模拟可能的OCR识别错误
	fmt.Println("\n=== 模拟OCR识别错误 ===")
	simulateOCRErrors(actualText)

	// 测试我们的提取算法对实际内容的处理
	fmt.Println("\n=== 算法测试（实际内容） ===")
	result := extractOrganFromText(actualText)
	fmt.Printf("提取结果: [%s]\n", result)

	// 分析OCR可能的识别问题
	fmt.Println("\n=== OCR识别问题分析 ===")
	analyzeOCRIssues()

	// 提供改进建议
	fmt.Println("\n=== 改进建议 ===")
	provideSuggestions()
}

// 分析字符差异
func analyzeCharacterDifferences(actual, recognized string) {
	fmt.Printf("实际字符: [%s]\n", actual)
	fmt.Printf("识别字符: [%s]\n", recognized)

	actualRunes := []rune(actual)
	recognizedRunes := []rune(recognized)

	fmt.Println("\n字符对比:")
	fmt.Printf("实际: ")
	for i, r := range actualRunes {
		fmt.Printf("[%d]%c ", i, r)
	}
	fmt.Printf("\n识别: ")
	for i, r := range recognizedRunes {
		fmt.Printf("[%d]%c ", i, r)
	}
	fmt.Println()

	// 分析相似性
	fmt.Println("\n相似性分析:")
	fmt.Println("'腹' vs '胆':")
	fmt.Println("  - 都是左右结构的汉字")
	fmt.Println("  - 左边都有'月'字旁（肉月旁）")
	fmt.Println("  - 在低分辨率或模糊情况下容易混淆")

	fmt.Println("'部' vs '囊':")
	fmt.Println("  - '部'：左右结构，右边是'阝'")
	fmt.Println("  - '囊'：上下结构，比较复杂")
	fmt.Println("  - 形状差异较大，但在OCR中可能被误识别")
}

// 模拟OCR识别错误
func simulateOCRErrors(actualText string) {
	// 常见的OCR识别错误模式
	errorPatterns := []struct {
		description string
		pattern     string
		replacement string
	}{
		{"腹部 → 胆囊", "腹部", "胆囊"},
		{"腹部 → 腹囊", "腹部", "腹囊"},
		{"腹部 → 胆部", "腹部", "胆部"},
		{"腹部 → 服部", "腹部", "服部"},
		{"腹部 → 复部", "腹部", "复部"},
		{"数字识别错误", "0.000", "0.00O"},
		{"数字识别错误", "0.000", "O.000"},
		{"空格丢失", "0.000 腹部", "0.000腹部"},
		{"多余字符", "腹部", "腹部位"},
	}

	fmt.Println("可能的OCR识别错误:")
	for i, pattern := range errorPatterns {
		errorText := strings.ReplaceAll(actualText, pattern.pattern, pattern.replacement)
		fmt.Printf("%d. %s: [%s]\n", i+1, pattern.description, errorText)

		// 测试我们的算法是否能处理这些错误
		result := extractOrganFromText(errorText)
		if result != "未知器官" && result != "" {
			fmt.Printf("   ✅ 算法仍能提取: [%s]\n", result)
		} else {
			fmt.Printf("   ❌ 算法无法处理此错误\n")
		}
	}
}

// 分析OCR识别问题
func analyzeOCRIssues() {
	fmt.Println("可能的OCR识别问题原因:")

	issues := []struct {
		category string
		problems []string
	}{
		{
			"图像质量问题",
			[]string{
				"分辨率不足导致字符模糊",
				"截图压缩损失细节",
				"屏幕显示质量影响",
				"字体大小过小",
			},
		},
		{
			"OCR引擎限制",
			[]string{
				"Tesseract对中文识别准确率有限",
				"训练数据中医疗术语较少",
				"字符相似性导致混淆",
				"上下文理解能力不足",
			},
		},
		{
			"预处理不足",
			[]string{
				"未进行图像增强",
				"未调整对比度",
				"未进行去噪处理",
				"未优化字符分割",
			},
		},
		{
			"配置问题",
			[]string{
				"OCR引擎参数未优化",
				"语言模型配置不当",
				"页面分割模式不合适",
				"置信度阈值设置问题",
			},
		},
	}

	for _, issue := range issues {
		fmt.Printf("\n%s:\n", issue.category)
		for _, problem := range issue.problems {
			fmt.Printf("  - %s\n", problem)
		}
	}
}

// 提供改进建议
func provideSuggestions() {
	suggestions := []struct {
		category string
		methods  []string
	}{
		{
			"图像预处理优化",
			[]string{
				"增加图像分辨率（放大2-3倍）",
				"应用锐化滤镜增强字符边缘",
				"调整对比度和亮度",
				"使用去噪算法",
				"二值化处理突出文字",
			},
		},
		{
			"OCR引擎优化",
			[]string{
				"尝试不同的OCR引擎（PaddleOCR、EasyOCR）",
				"使用专门的中文OCR模型",
				"调整页面分割模式（PSM_SINGLE_LINE）",
				"设置合适的DPI值",
				"使用白名单限制识别字符",
			},
		},
		{
			"多引擎融合",
			[]string{
				"同时使用多个OCR引擎",
				"对比识别结果，选择最可能的",
				"使用投票机制决定最终结果",
				"建立医疗术语词典进行校正",
			},
		},
		{
			"后处理校正",
			[]string{
				"建立常见误识别字符对照表",
				"使用编辑距离算法匹配已知器官名称",
				"基于医疗知识库进行语义校正",
				"使用正则表达式修正常见错误",
			},
		},
		{
			"质量监控",
			[]string{
				"记录OCR识别的置信度",
				"对低置信度结果进行人工校验",
				"建立识别错误反馈机制",
				"持续优化识别准确率",
			},
		},
	}

	for _, suggestion := range suggestions {
		fmt.Printf("\n%s:\n", suggestion.category)
		for _, method := range suggestion.methods {
			fmt.Printf("  ✓ %s\n", method)
		}
	}
}

// 从文本中提取器官名称（复用之前的函数）
func extractOrganFromText(text string) string {
	text = strings.TrimSpace(text)

	if strings.Contains(text, "0.000") {
		return extractOrganFromLine(text)
	}

	lines := strings.Split(text, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.Contains(line, "0.000") {
			return extractOrganFromLine(line)
		}

		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, line); matched {
			result := extractOrganFromLine(line)
			if result != "" {
				return result
			}
		}
	}

	return "未知器官"
}

// 从单行中提取器官名称（复用之前的函数）
func extractOrganFromLine(line string) string {
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	// 模式1: 数字后直接跟器官名称（有空格）
	pattern1 := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		if organName != "" {
			return organName
		}
	}

	// 模式1B: 数字后直接跟器官名称（无空格）
	pattern1B := regexp.MustCompile(`[0-9]\.[0-9]{3}([^\s\d].*)`)
	if matches := pattern1B.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		if organName != "" {
			return organName
		}
	}

	return ""
}

// 清理器官名称（复用之前的函数）
func cleanOrganName(organName string) string {
	organName = regexp.MustCompile(`\s*[A-Z][A-Z\s\-]+$`).ReplaceAllString(organName, "")
	organName = strings.Trim(organName, "|")
	organName = strings.TrimSpace(organName)

	if matched, _ := regexp.MatchString(`^[A-Za-z0-9\s\-]+$`, organName); matched {
		return ""
	}

	return organName
}

func main() {
	analyzeOCRAccuracy()
}
