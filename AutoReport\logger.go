package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

const logFileName = "operation.log"

var logger *log.Logger

func initLogger() error {
	// 创建日志目录
	logDir := filepath.Join("logs")
	if err := os.<PERSON>dir<PERSON>ll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开日志文件
	logFile, err := os.OpenFile(filepath.Join(logDir, logFileName),
		os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 初始化logger
	logger = log.New(logFile, "", log.LstdFlags)
	return nil
}

// LogOperation 记录操作日志
func LogOperation(operation, user, siteID string) {
	if logger == nil {
		if err := initLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [操作:%s] [用户:%s] [网点:%s]\n",
		timestamp, operation, user, siteID)
}

func LogError(operation, user string, err error) {
	if logger == nil {
		if err := initLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [操作:%s] [用户:%s] [错误:%v]\n",
		timestamp, operation, user, err)
}
