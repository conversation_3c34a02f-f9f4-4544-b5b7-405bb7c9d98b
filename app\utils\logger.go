package utils

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"
)

const logFileName = "operation.log"

var logger *log.Logger

// InitLogger 初始化日志
func InitLogger() error {
	// 创建日志目录
	logDir := filepath.Join("logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开日志文件
	logFile, err := os.OpenFile(filepath.Join(logDir, logFileName),
		os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 同时输出到文件和控制台
	mw := io.MultiWriter(logFile, os.Stdout)

	// 初始化logger
	logger = log.New(mw, "", log.LstdFlags)
	return nil
}

// LogOperation 记录操作日志
func LogOperation(operation, user, siteID string) {
	if logger == nil {
		if err := InitLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [操作:%s] [用户:%s] [网点:%s]\n",
		timestamp, operation, user, siteID)
}

// LogError 记录错误日志
func LogError(operation, user string, err error) {
	if logger == nil {
		if initErr := InitLogger(); initErr != nil {
			log.Printf("初始化日志失败: %v", initErr)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [操作:%s] [用户:%s] [错误:%v]\n",
		timestamp, operation, user, err)
}

// LogInfo 记录信息日志
func LogInfo(message string) {
	if logger == nil {
		if err := InitLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] [信息:%s]", timestamp, message)
	// 同时输出到文件和控制台
	logger.Println(logMessage)
	fmt.Println(logMessage)
}

// LogWarning 记录警告日志
func LogWarning(message string) {
	if logger == nil {
		if err := InitLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [警告:%s]\n", timestamp, message)
}

// LogDebug 记录调试日志
func LogDebug(message string) {
	if logger == nil {
		if err := InitLogger(); err != nil {
			log.Printf("初始化日志失败: %v", err)
			return
		}
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logger.Printf("[%s] [调试:%s]\n", timestamp, message)
}
