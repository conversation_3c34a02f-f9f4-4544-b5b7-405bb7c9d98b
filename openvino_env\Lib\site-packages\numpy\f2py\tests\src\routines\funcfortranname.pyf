python module funcfortranname ! in
    interface  ! in :funcfortranname
        function funcfortranname_default(a,b) ! in :funcfortranname:funcfortranname.f
            fortranname funcfortranname
            real*8 :: a
            real*8 :: b
            real*8 :: funcfortranname_default
            real*8, intent(out) :: funcfortranname
        end function funcfortranname_default
    end interface
end python module funcfortranname
