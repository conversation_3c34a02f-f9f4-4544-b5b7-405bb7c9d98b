package main

import (
	"fmt"
	"regexp"
	"strings"
)

// OCR器官名称提取测试（模拟版本）
func testOCROrganExtraction() {
	fmt.Println("=== OCR器官名称提取测试 ===")
	fmt.Println("基于您提供的图片内容进行测试")
	fmt.Println("图片描述：标准图谱分析窗口，左上角1/4区域包含相似度递减列表")

	// 模拟您提供的图片中左上角1/4区域可能包含的文本内容
	fmt.Println("\n=== 模拟OCR识别的文本内容 ===")

	// 根据您的图片，左上角1/4区域主要包含：
	// 1. 标题部分："标准图谱分析窗口"
	// 2. 表格开始部分，包含"0.000"行
	sampleTexts := []string{
		// 完整的表格行（最重要的测试用例）
		"0.000 胆囊第1腰椎水平截面",

		// 可能的OCR识别变体
		"0.000胆囊第1腰椎水平截面",
		"0.000  胆囊第1腰椎水平截面",
		"| 0.000 | 胆囊第1腰椎水平截面 |",

		// 其他可能的行
		"4.479 优化配置",
		"0.054 C反应蛋白C-REACTIVE PROTEIN",
		"0.072 血尿酸SERUM URIC ACID",

		// 多行文本测试
		`按照标准图谱相似度递减列表:
0.000 胆囊第1腰椎水平截面
4.479 优化配置`,

		// 表格格式测试
		`| 数值 | 器官名称 |
| 0.000 | 胆囊第1腰椎水平截面 |
| 4.479 | 优化配置 |`,

		// 实际可能的OCR输出（带噪声）
		"0.000  胆囊第1腰椎水平截面  ",
		"0 . 0 0 0 胆囊第1腰椎水平截面",
		"0.000\t胆囊第1腰椎水平截面",
	}

	fmt.Printf("共 %d 个测试用例\n", len(sampleTexts))

	successCount := 0
	for i, text := range sampleTexts {
		fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
		fmt.Printf("测试用例 %d:\n", i+1)
		fmt.Printf("原文本: [%s]\n", text)

		organName := extractOrganFromText(text)
		fmt.Printf("提取结果: [%s]\n", organName)

		if organName != "未知器官" && organName != "" {
			fmt.Printf("✅ 成功提取器官名称\n")
			successCount++
		} else {
			fmt.Printf("❌ 未能提取器官名称\n")
		}
	}

	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("测试完成！成功率: %d/%d (%.1f%%)\n", successCount, len(sampleTexts), float64(successCount)/float64(len(sampleTexts))*100)

	// 展示算法逻辑
	fmt.Println("\n=== 提取算法说明 ===")
	fmt.Println("1. 查找包含 '0.000' 的行（第一行，相似度最高）")
	fmt.Println("2. 使用正则表达式提取数字后的器官名称")
	fmt.Println("3. 清理英文描述，保留中文器官名称")
	fmt.Println("4. 支持多种格式：普通文本、表格格式、带噪声文本")
}

// 从文本中提取器官名称
func extractOrganFromText(text string) string {
	// 清理文本
	text = strings.TrimSpace(text)

	fmt.Printf("  → 处理文本: [%s]\n", text)

	// 查找包含 "0.000" 的部分
	if strings.Contains(text, "0.000") {
		result := extractOrganFromLine(text)
		if result != "" {
			return result
		}
	}

	// 按行处理
	lines := strings.Split(text, "\n")
	for lineNum, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fmt.Printf("  → 检查第%d行: [%s]\n", lineNum+1, line)

		// 查找包含 "0.000" 的行
		if strings.Contains(line, "0.000") {
			result := extractOrganFromLine(line)
			if result != "" {
				return result
			}
		}

		// 也检查包含类似数字的行
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, line); matched {
			result := extractOrganFromLine(line)
			if result != "" {
				return result
			}
		}
	}

	fmt.Printf("  → 未找到匹配的器官名称\n")
	return "未知器官"
}

// 从单行中提取器官名称
func extractOrganFromLine(line string) string {
	// 移除多余空格和特殊字符
	line = regexp.MustCompile(`\s+`).ReplaceAllString(line, " ")
	line = strings.TrimSpace(line)

	fmt.Printf("    → 分析行: [%s]\n", line)

	// 模式1: 数字后直接跟器官名称（有空格）
	// 例如: "0.000 胆囊第1腰椎水平截面"
	pattern1 := regexp.MustCompile(`[0-9]\.[0-9]{3}\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		fmt.Printf("    → 模式1匹配: [%s]\n", organName)
		if organName != "" {
			return organName
		}
	}

	// 模式1B: 数字后直接跟器官名称（无空格）
	// 例如: "0.000胆囊第1腰椎水平截面"
	pattern1B := regexp.MustCompile(`[0-9]\.[0-9]{3}([^\s\d].*)`)
	if matches := pattern1B.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		fmt.Printf("    → 模式1B匹配（无空格）: [%s]\n", organName)
		if organName != "" {
			return organName
		}
	}

	// 模式2: 表格格式
	// 例如: "| 0.000 | 胆囊第1腰椎水平截面 |"
	pattern2 := regexp.MustCompile(`\|\s*[0-9]\.[0-9]{3}\s*\|\s*(.+?)\s*\|`)
	if matches := pattern2.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		fmt.Printf("    → 模式2匹配: [%s]\n", organName)
		if organName != "" {
			return organName
		}
	}

	// 模式3: 简单分割（处理空格分隔的情况）
	parts := strings.Fields(line)
	for i, part := range parts {
		if matched, _ := regexp.MatchString(`[0-9]\.[0-9]{3}`, part); matched {
			if i+1 < len(parts) {
				organParts := parts[i+1:]
				organName := strings.Join(organParts, " ")
				organName = cleanOrganName(organName)
				fmt.Printf("    → 模式3匹配: [%s]\n", organName)
				if organName != "" {
					return organName
				}
			}
		}
	}

	// 模式4: 处理带噪声的数字（如 "0 . 0 0 0"）
	pattern4 := regexp.MustCompile(`[0-9]\s*\.\s*[0-9]\s*[0-9]\s*[0-9]\s+(.+)`)
	if matches := pattern4.FindStringSubmatch(line); len(matches) > 1 {
		organName := strings.TrimSpace(matches[1])
		organName = cleanOrganName(organName)
		fmt.Printf("    → 模式4匹配（带噪声）: [%s]\n", organName)
		if organName != "" {
			return organName
		}
	}

	// 模式5: 处理更复杂的噪声数字（如 "0 . 0 0 0"）
	// 先清理噪声，再匹配
	cleanedLine := regexp.MustCompile(`([0-9])\s*\.\s*([0-9])\s*([0-9])\s*([0-9])`).ReplaceAllString(line, "$1.$2$3$4")
	if cleanedLine != line {
		fmt.Printf("    → 清理噪声后: [%s]\n", cleanedLine)
		return extractOrganFromLine(cleanedLine) // 递归调用
	}

	// 模式6: 直接查找中文字符（作为最后的备选方案）
	// 如果前面的模式都失败，尝试提取行中的中文内容
	chinesePattern := regexp.MustCompile(`[\p{Han}]+[^\s]*`)
	if matches := chinesePattern.FindAllString(line, -1); len(matches) > 0 {
		// 取最长的中文匹配作为器官名称
		longestMatch := ""
		for _, match := range matches {
			if len(match) > len(longestMatch) {
				longestMatch = match
			}
		}
		if len(longestMatch) > 2 { // 至少3个字符
			fmt.Printf("    → 模式6匹配（中文提取）: [%s]\n", longestMatch)
			return longestMatch
		}
	}

	fmt.Printf("    → 所有模式都未匹配\n")
	return ""
}

// 清理器官名称
func cleanOrganName(organName string) string {
	// 移除常见的英文描述（通常在中文后面）
	organName = regexp.MustCompile(`\s*[A-Z][A-Z\s\-]+$`).ReplaceAllString(organName, "")

	// 移除表格分隔符
	organName = strings.Trim(organName, "|")

	// 移除多余的空格
	organName = strings.TrimSpace(organName)

	// 如果只剩下英文或数字，返回空
	if matched, _ := regexp.MatchString(`^[A-Za-z0-9\s\-]+$`, organName); matched {
		return ""
	}

	return organName
}

func main() {
	testOCROrganExtraction()
}
