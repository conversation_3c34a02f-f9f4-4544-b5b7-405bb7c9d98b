package main

import (
	"syscall"
	"unsafe"

	"fyne.io/fyne/v2"
)

// Windows API常量
const (
	SWP_NOSIZE     = 0x0001
	SWP_NOZORDER   = 0x0004
	SWP_SHOWWINDOW = 0x0040
)

// Windows API函数
var (
	// 使用main.go中已经声明的user32
	procSetWindowPos        = user32.NewProc("SetWindowPos")
	procFindWindowW         = user32.NewProc("FindWindowW")
	procGetForegroundWindow = user32.NewProc("GetForegroundWindow")
)

// 设置窗口位置到屏幕左上角(0,0)
func SetWindowPosition(w fyne.Window, x, y int) {
	// 使用Windows API
	// 注意：这只在Windows平台上有效
	title := w.Title()
	handle := findWindow(title)
	if handle != 0 {
		setWindowPos(handle, x, y)
	}
}

// 查找窗口句柄
func findWindow(title string) uintptr {
	// 先尝试获取前台窗口
	hwnd, _, _ := procGetForegroundWindow.Call()
	if hwnd != 0 {
		return hwnd
	}

	// 如果获取前台窗口失败，尝试通过标题查找
	hwnd, _, _ = procFindWindowW.Call(0, uintptr(unsafe.Pointer(syscall.StringToUTF16Ptr(title))))
	return hwnd
}

// 设置窗口位置
func setWindowPos(hwnd uintptr, x, y int) {
	procSetWindowPos.Call(
		hwnd,
		0, // hWndInsertAfter
		uintptr(x),
		uintptr(y),
		0, // cx
		0, // cy
		SWP_NOSIZE|SWP_NOZORDER|SWP_SHOWWINDOW,
	)
}
