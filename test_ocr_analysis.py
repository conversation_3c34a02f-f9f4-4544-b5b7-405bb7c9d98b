#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import re

def analyze_ocr_results():
    print("=== OCR Results Analysis ===")
    
    try:
        import easyocr
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        image_path = "pic/test_image.png"
        print(f"Analyzing image: {image_path}")
        
        result = reader.readtext(image_path)
        print(f"Total text blocks found: {len(result)}")
        print()
        
        # Analyze all results
        target_patterns = []
        high_confidence_texts = []
        number_patterns = []
        
        for i, (bbox, text, confidence) in enumerate(result):
            print(f"Block {i+1}: [{text}] (conf: {confidence:.2f})")
            
            # Look for number patterns
            if re.search(r'[0-9]', text):
                number_patterns.append((text, confidence))
                print(f"  -> NUMBER PATTERN FOUND")
            
            # Look for target patterns (0.000 or similar)
            if re.search(r'[0-9][.,][0-9]{3}', text):
                target_patterns.append((text, confidence))
                print(f"  -> TARGET PATTERN FOUND!")
            
            # High confidence texts
            if confidence > 0.7:
                high_confidence_texts.append((text, confidence))
                print(f"  -> HIGH CONFIDENCE")
            
            print()
        
        print("=== Analysis Summary ===")
        print(f"Number patterns found: {len(number_patterns)}")
        for text, conf in number_patterns:
            print(f"  - [{text}] (conf: {conf:.2f})")
        
        print(f"\nTarget patterns found: {len(target_patterns)}")
        for text, conf in target_patterns:
            print(f"  - [{text}] (conf: {conf:.2f})")
            
            # Try to extract organ name from nearby text
            print(f"    Attempting to find related organ text...")
            
        print(f"\nHigh confidence texts: {len(high_confidence_texts)}")
        for text, conf in high_confidence_texts:
            print(f"  - [{text}] (conf: {conf:.2f})")
        
        # Try to reconstruct the target line
        print("\n=== Reconstruction Attempt ===")
        if target_patterns:
            target_text = target_patterns[0][0]
            print(f"Found target number: {target_text}")
            
            # Normalize the number format
            normalized = target_text.replace(',', '.')
            print(f"Normalized: {normalized}")
            
            # Look for nearby Chinese text that might be the organ name
            print("Looking for nearby organ text...")
            # This would require more sophisticated spatial analysis
            
        else:
            print("No target pattern found. Possible reasons:")
            print("1. The target text is not in the expected format")
            print("2. OCR accuracy is insufficient")
            print("3. The target area needs to be cropped more precisely")
        
        return result
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_ocr_results()
