package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"MagneticOperator/app/services"
)

func main() {
	fmt.Println("=== 截图+OCR功能测试 ===")

	// 创建配置服务
	ctx := context.Background()
	configService := services.NewConfigService(ctx)

	// 创建截图服务
	screenshotService := services.NewScreenshotService(configService)

	fmt.Println("1. 测试原有截图功能（保持不变）...")

	// 测试原有截图功能
	filePath, err := screenshotService.TakeScreenshot("生化平衡分析", "测试用户")
	if err != nil {
		log.Printf("原有截图功能测试失败: %v", err)
	} else {
		fmt.Printf("✓ 原有截图功能正常 - 文件: %s\n", filePath)
	}

	fmt.Println("\n2. 测试新的OCR区域截图功能...")

	// 测试OCR区域截图功能
	ocrFilePath, organName, err := screenshotService.TakeOCRRegionScreenshot("生化平衡分析", "测试用户")
	if err != nil {
		log.Printf("OCR区域截图测试失败: %v", err)
	} else {
		fmt.Printf("✓ OCR区域截图功能正常\n")
		fmt.Printf("  OCR文件: %s\n", ocrFilePath)
		fmt.Printf("  识别器官: %s\n", organName)
	}

	fmt.Println("\n3. 测试异步OCR功能...")

	// 测试异步OCR功能
	asyncFilePath, err := screenshotService.TakeScreenshotWithOCRAsync("病理形态学分析", "测试用户")
	if err != nil {
		log.Printf("异步OCR功能测试失败: %v", err)
	} else {
		fmt.Printf("✓ 异步OCR功能启动成功 - 主截图文件: %s\n", asyncFilePath)
		fmt.Println("  (OCR识别正在后台进行...)")

		// 等待一下让异步OCR完成
		time.Sleep(3 * time.Second)
	}

	fmt.Println("\n=== 功能测试完成 ===")
	fmt.Println("\n📋 功能说明:")
	fmt.Println("1. 原有截图功能 (TakeScreenshot) - 保持完全不变")
	fmt.Println("2. OCR区域截图 (TakeOCRRegionScreenshot) - 只截取左上角1/4区域并识别器官")
	fmt.Println("3. 异步OCR (TakeScreenshotWithOCRAsync) - 执行原有截图后异步启动OCR")
	fmt.Println("\n🎯 集成方式:")
	fmt.Println("- 快捷键功能继续使用原有的 ProcessScreenshotAndUpload")
	fmt.Println("- ProcessScreenshotAndUpload 内部已添加异步OCR调用")
	fmt.Println("- 两个功能并行工作，互不干扰")
}
