# -*- coding: utf-8 -*-
# Copyright (C) 2018-2025 Intel Corporation
# SPDX-License-Identifier: Apache-2.0

from openvino.utils.types import TensorShape
from openvino.utils.types import NumericData
from openvino.utils.types import NumericType
from openvino.utils.types import ScalarData
from openvino.utils.types import NodeInput

from openvino.utils.types import openvino_to_numpy_types_map
from openvino.utils.types import openvino_to_numpy_types_str_map
from openvino.utils.types import get_element_type
from openvino.utils.types import get_element_type_str
from openvino.utils.types import get_dtype
from openvino.utils.types import get_numpy_ctype
from openvino.utils.types import get_ndarray
from openvino.utils.types import get_shape
from openvino.utils.types import make_constant_node
from openvino.utils.types import as_node
from openvino.utils.types import as_nodes
