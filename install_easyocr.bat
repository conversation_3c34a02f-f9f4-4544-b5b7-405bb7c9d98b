@echo off
echo ===== EasyOCR 安装脚本 (兼容32位Python) =====
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo EasyOCR对32位Python兼容性更好，正在安装...
echo.

echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装依赖包...
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

echo.
echo 安装EasyOCR...
pip install easyocr

echo.
echo 验证安装...
python -c "import easyocr; print('EasyOCR安装成功!')"

if %errorlevel% equ 0 (
    echo.
    echo ===== 安装完成 =====
    echo 现在可以运行: go run easyocr_solution.go
) else (
    echo.
    echo ===== 安装失败 =====
    echo 建议升级到64位Python后安装PaddleOCR
)

echo.
pause
