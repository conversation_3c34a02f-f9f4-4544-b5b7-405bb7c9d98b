#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time

def test_real_image():
    """测试真实医疗图像"""
    image_path = r"F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png"
    
    print("=== 简单OCR测试 ===")
    print(f"图像路径: {image_path}")
    
    # 检查文件
    if not os.path.exists(image_path):
        print("ERROR: 图像文件不存在")
        return
    
    print("OK: 图像文件存在")
    
    # 测试EasyOCR
    try:
        import easyocr
        print("OK: EasyOCR可用")
        
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False, verbose=False)
        print("OK: EasyOCR初始化成功")
        
        # 识别
        print("开始识别...")
        start_time = time.time()
        results = reader.readtext(image_path)
        end_time = time.time()
        
        print(f"识别完成，耗时: {end_time - start_time:.2f}秒")
        print(f"识别结果数量: {len(results)}")
        
        # 显示所有结果
        for i, (bbox, text, confidence) in enumerate(results):
            print(f"结果{i+1}: [{text}] 置信度: {confidence:.3f}")
        
        # 查找目标模式
        found_target = False
        for bbox, text, confidence in results:
            if "0.000" in text or "0,000" in text or "O.000" in text:
                print(f"找到目标: [{text}] 置信度: {confidence:.3f}")
                found_target = True
        
        if not found_target:
            print("未找到目标数字模式")
            
    except Exception as e:
        print(f"EasyOCR测试失败: {e}")

if __name__ == "__main__":
    test_real_image()
