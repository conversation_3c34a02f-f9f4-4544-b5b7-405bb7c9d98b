package services

import (
	"time"
)

// OCRResult OCR识别结果结构
type OCRResult struct {
	FullText    string        `json:"full_text"`    // 完整识别文字
	OrganName   string        `json:"organ_name"`   // 器官名称
	Confidence  float64       `json:"confidence"`   // 置信度
	ProcessTime time.Duration `json:"process_time"` // 处理时间
	ImagePath   string        `json:"image_path"`   // 图片路径
}

// OCRInterface OCR服务接口
type OCRInterface interface {
	// Close 关闭OCR服务
	Close()
	
	// ExtractTextFromImage 从图片中提取所有文字
	ExtractTextFromImage(imagePath string) (string, error)
	
	// ExtractOrganName 从截图中提取器官部位名称
	ExtractOrganName(imagePath string) (string, error)
	
	// GetOrganNameWithConfidence 获取器官名称及置信度
	GetOrganNameWithConfidence(imagePath string) (string, float64, error)
	
	// ValidateOCREnvironment 验证OCR环境是否正确配置
	ValidateOCREnvironment() error
	
	// ProcessImageWithDetails 处理图片并返回详细结果
	ProcessImageWithDetails(imagePath string) (*OCRResult, error)
}

// NewOCRService 创建OCR服务（工厂函数）
func NewOCRService() OCRInterface {
	// 尝试创建真实的OCR服务
	if service := tryCreateRealOCRService(); service != nil {
		return service
	}
	
	// 如果失败，返回模拟服务
	return NewMockOCRService()
}

// tryCreateRealOCRService 尝试创建真实的OCR服务
func tryCreateRealOCRService() OCRInterface {
	// 这个函数会在有OCR构建标签时被真实实现覆盖
	return nil
}
