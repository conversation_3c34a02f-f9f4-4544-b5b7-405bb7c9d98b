package models

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// MpAppInfo 小程序应用信息
type MpAppInfo struct {
	AppID      string `json:"appid"`
	TargetPage string `json:"target_page"`
}

// Location 位置信息
type Location struct {
	Province string `json:"province"`
	City     string `json:"city"`
	District string `json:"district"`
	Address  string `json:"address"`
}

// Contact 联系人信息
type Contact struct {
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
}

// SiteInfo 站点信息
type SiteInfo struct {
	SiteID    string   `json:"site_id"`
	SiteName  string   `json:"site_name"`
	SiteType  string   `json:"site_type"`
	ParentOrg string   `json:"parent_org"`
	Location  Location `json:"location"`
	Contact   Contact  `json:"contact"`
}

// CropSettings 裁剪设置
type CropSettings struct {
	TopPercent    float64 `json:"top_percent"`
	BottomPercent float64 `json:"bottom_percent"`
	LeftPercent   float64 `json:"left_percent"`
	RightPercent  float64 `json:"right_percent"`
}

// DcloudConfig DCloud配置
type DcloudConfig struct {
	AccessKey string `json:"access_key"`
	SecretKey string `json:"secret_key"`
	UploadURL string `json:"upload_url"`
	SpaceID   string `json:"space_id"`
}

// CozeConfig Coze配置
type CozeConfig struct {
	Token                      string `json:"token"`
	WorkflowIDPostPic          string `json:"workflow_id_post_pic"`
	WorkflowIDPostRegistration string `json:"workflow_id_post_registration"`
	WorkflowIDCheckUserInfo    string `json:"workflow_id_user_info"`
	SpaceID                    string `json:"space_id"`
	AppID                      string `json:"app_id"`
}

// CloudFunctionConfig 云函数配置
type CloudFunctionConfig struct {
	RegistrationsURL       string `json:"registrations_url"`
	ScreenshotRecordsURL   string `json:"screenshot_records_url"`
	SiteInfoByDeviceMACURL string `json:"siteInfoByDeviceMAC_url"`
}

// APIKeys API密钥配置
type APIKeys struct {
	Dcloud        DcloudConfig        `json:"dcloud"`
	Coze          CozeConfig          `json:"coze"`
	CloudFunction CloudFunctionConfig `json:"cloud_function"`
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	MACAddress string `json:"mac_address"`
	DeviceName string `json:"device_name"`
}

// ConfigUserInfo 配置中的用户信息
type ConfigUserInfo struct {
	Name string `json:"name"`
	// Gender   string `json:"gender"` // Temporarily commented out as per user request and to fix unmarshal error
	Birth    string `json:"birth"`
	IDNumber string `json:"id_number"`
}

// AppConfig 应用程序配置结构体
type AppConfig struct {
	MpAppInfo    MpAppInfo      `json:"mp_app_info"`
	SiteInfo     SiteInfo       `json:"site_info"`
	CropSettings CropSettings   `json:"crop_settings"`
	APIKeys      APIKeys        `json:"api_keys"`
	DeviceInfo   DeviceInfo     `json:"device_info"`
	UserInfo     ConfigUserInfo `json:"user_info"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	Name     string `json:"name"`
	Birthday string `json:"birthday"`  // 格式: YYYY-MM-DD
	Age      int    `json:"age"`       // 计算得出的年龄
	Gender   int    `json:"gender"`    // 性别: 1 表示男性, 2 表示女性 (或其他数字编码)
	IDNumber string `json:"id_number"` // 身份证号
}

// ModeInfo 模式信息结构
type ModeInfo struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// ScreenshotRecordParams 截图记录参数结构
type ScreenshotRecordParams struct {
	UserName       string `json:"user_name"`       // 用户姓名
	UserID         string `json:"user_id"`         // 用户ID
	RegistrationID string `json:"registration_id"` // 挂号记录ID
	Round          int    `json:"round"`           // 轮次 1-10
	AnalysisType   string `json:"analysis_type"`   // 分析类型 B02/C03
	DetectedOrgan  string `json:"detected_organ"`  // 识别的器官名称
	Filename       string `json:"filename"`        // 文件名
	CloudURL       string `json:"cloud_url"`       // 云存储URL
	OperatorName   string `json:"operator_name"`   // 操作员姓名
	OCRSuccess     bool   `json:"ocr_success"`     // OCR是否成功
}

// Patient 患者信息结构
type Patient struct {
	Name             string `json:"name"`              // 姓名
	RegistrationCode string `json:"registration_code"` // 挂号码
	FullCode         string `json:"full_code"`         // 完整挂号码
	RegisterTime     string `json:"register_time"`     // 挂号时间
}

// Registration 候检者报到信息结构
type Registration struct {
	ID                 string     `json:"_id"`                 // 记录ID
	UserID             string     `json:"user_id"`             // 用户ID
	DeviceNo           string     `json:"device_no"`           // 设备号
	SiteID             string     `json:"site_id"`             // 站点ID
	RegistrationNumber string     `json:"registration_number"` // 报到号
	RegistrationTime   int64      `json:"registration_time"`   // 报到时间戳
	RegistrationDate   string     `json:"registration_date"`   // 报到日期
	UserInfo           []UserInfo `json:"userInfo"`            // 用户信息
	// 为了兼容前端显示，添加计算属性
	Name         string `json:"name,omitempty"`          // 姓名（从UserInfo提取）
	Number       string `json:"number,omitempty"`        // 报到号（别名）
	RegisterTime string `json:"register_time,omitempty"` // 格式化的报到时间
}

// SaveConfig 保存配置到文件
func (c *AppConfig) SaveConfig() error {
	configPath := filepath.Join("config", "app_config.json")
	os.MkdirAll(filepath.Dir(configPath), 0755)

	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}
