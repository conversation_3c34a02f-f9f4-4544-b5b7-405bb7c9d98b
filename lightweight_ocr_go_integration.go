package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// 轻量化OCR结果
type LightweightOCRResult struct {
	FinalText          string   `json:"final_text"`
	Confidence         float64  `json:"confidence"`
	ProcessingTime     float64  `json:"processing_time"`
	Engine             string   `json:"engine"`
	Success            bool     `json:"success"`
	CorrectionsApplied []string `json:"corrections_applied"`
}

// 轻量化OCR服务
type LightweightOCRService struct {
	pythonPath  string
	scriptPath  string
	tempDir     string
	initialized bool
	mu          sync.Mutex
}

// 创建轻量化OCR服务
func NewLightweightOCRService() *LightweightOCRService {
	return &LightweightOCRService{
		pythonPath:  "python",
		scriptPath:  "lightweight_ocr_solution.py",
		tempDir:     "temp",
		initialized: false,
	}
}

// 检查环境
func (l *LightweightOCRService) CheckEnvironment() error {
	fmt.Println("=== 检查轻量化OCR环境 ===")

	// 检查Python
	cmd := exec.Command(l.pythonPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("Python未安装: %v", err)
	}
	fmt.Printf("✓ Python: %s", string(output))

	// 检查脚本
	if _, err := os.Stat(l.scriptPath); os.IsNotExist(err) {
		return fmt.Errorf("OCR脚本不存在: %s", l.scriptPath)
	}
	fmt.Printf("✓ OCR脚本: %s\n", l.scriptPath)

	// 测试脚本可用性
	cmd = exec.Command(l.pythonPath, "-c", "import easyocr; print('EasyOCR OK')")
	output, err = cmd.Output()
	if err != nil {
		fmt.Printf("⚠ EasyOCR测试失败，但继续执行\n")
	} else {
		fmt.Printf("✓ %s", string(output))
	}

	l.initialized = true
	return nil
}

// 识别单张图片
func (l *LightweightOCRService) RecognizeImage(imagePath string) (*LightweightOCRResult, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if !l.initialized {
		return nil, fmt.Errorf("服务未初始化")
	}

	fmt.Printf("识别图片: %s\n", imagePath)
	startTime := time.Now()

	// 调用Python OCR脚本
	cmd := exec.Command(l.pythonPath, l.scriptPath)

	// 临时修改脚本以处理特定图片
	tempScript := l.createTempScript(imagePath)
	defer os.Remove(tempScript)

	cmd = exec.Command(l.pythonPath, tempScript)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("OCR脚本执行失败: %v", err)
	}

	// 解析JSON输出
	result, err := l.parseOCROutput(string(output))
	if err != nil {
		return nil, fmt.Errorf("解析OCR结果失败: %v", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("✓ 识别完成，耗时: %v\n", duration)

	return result, nil
}

// 创建临时脚本处理特定图片
func (l *LightweightOCRService) createTempScript(imagePath string) string {
	tempScript := filepath.Join(l.tempDir, "temp_ocr_script.py")
	os.MkdirAll(l.tempDir, 0755)

	scriptContent := fmt.Sprintf(`
import sys
sys.path.append('.')
from lightweight_ocr_solution import LightweightOCRService
import json

# 创建服务并处理指定图片
service = LightweightOCRService()
result = service.process_image_optimized('%s')

# 输出JSON结果
output = {
    "final_text": result.final_text,
    "confidence": result.confidence,
    "processing_time": result.processing_time,
    "engine": result.engine,
    "success": result.success,
    "corrections_applied": result.corrections_applied
}

print("JSON_START")
print(json.dumps(output, ensure_ascii=False))
print("JSON_END")
`, strings.ReplaceAll(imagePath, "\\", "/"))

	os.WriteFile(tempScript, []byte(scriptContent), 0644)
	return tempScript
}

// 解析OCR输出
func (l *LightweightOCRService) parseOCROutput(output string) (*LightweightOCRResult, error) {
	lines := strings.Split(output, "\n")

	var jsonContent string
	inJSON := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "JSON_START" {
			inJSON = true
			continue
		}
		if line == "JSON_END" {
			break
		}
		if inJSON {
			jsonContent += line
		}
	}

	if jsonContent == "" {
		return nil, fmt.Errorf("未找到JSON输出")
	}

	var result LightweightOCRResult
	err := json.Unmarshal([]byte(jsonContent), &result)
	if err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v\n原始输出: %s", err, output)
	}

	return &result, nil
}

// 批量处理图片（10轮次优化）
func (l *LightweightOCRService) ProcessBatch(imagePaths []string) ([]*LightweightOCRResult, error) {
	fmt.Printf("=== 批量处理 %d 张图片 ===\n", len(imagePaths))

	results := make([]*LightweightOCRResult, len(imagePaths))
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 限制并发数量，避免资源竞争
	semaphore := make(chan struct{}, 3) // 最多3个并发

	startTime := time.Now()

	for i, imagePath := range imagePaths {
		wg.Add(1)
		go func(index int, path string) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result, err := l.RecognizeImage(path)

			mu.Lock()
			if err != nil {
				fmt.Printf("图片 %d 识别失败: %v\n", index+1, err)
				results[index] = &LightweightOCRResult{
					Success: false,
					Engine:  "Error",
				}
			} else {
				results[index] = result
				fmt.Printf("图片 %d 识别成功: [%s]\n", index+1, result.FinalText)
			}
			mu.Unlock()
		}(i, imagePath)
	}

	wg.Wait()

	totalTime := time.Since(startTime)
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	fmt.Printf("✓ 批量处理完成: %d/%d 成功，总耗时: %v\n",
		successCount, len(imagePaths), totalTime)

	return results, nil
}

// 分析识别结果
func (l *LightweightOCRService) AnalyzeResult(result *LightweightOCRResult) {
	fmt.Printf("\n=== 识别结果分析 ===\n")
	fmt.Printf("识别文本: [%s]\n", result.FinalText)
	fmt.Printf("置信度: %.2f%%\n", result.Confidence*100)
	fmt.Printf("处理时间: %.3f秒\n", result.ProcessingTime)
	fmt.Printf("使用引擎: %s\n", result.Engine)
	fmt.Printf("识别成功: %t\n", result.Success)

	if len(result.CorrectionsApplied) > 0 {
		fmt.Printf("应用校正: %v\n", result.CorrectionsApplied)
	}

	// 业务逻辑验证
	if strings.Contains(result.FinalText, "0.000") {
		fmt.Printf("✓ 包含目标数字模式\n")

		if strings.Contains(result.FinalText, "腹部") {
			fmt.Printf("✓ 包含期望的器官名称\n")
		} else {
			fmt.Printf("⚠ 器官名称可能需要进一步处理\n")
		}
	} else {
		fmt.Printf("❌ 未找到目标数字模式\n")
	}

	// 性能评估
	if result.ProcessingTime < 1.0 {
		fmt.Printf("✓ 处理速度优秀 (<%1秒)\n")
	} else if result.ProcessingTime < 3.0 {
		fmt.Printf("⚠ 处理速度良好 (<3秒)\n")
	} else {
		fmt.Printf("❌ 处理速度需要优化 (>3秒)\n")
	}
}

// 模拟10轮次业务流程
func simulate10RoundProcess() {
	fmt.Println("=== 模拟10轮次业务流程 ===")

	// 创建服务
	service := NewLightweightOCRService()

	// 检查环境
	if err := service.CheckEnvironment(); err != nil {
		fmt.Printf("环境检查失败: %v\n", err)
		return
	}

	// 模拟10轮次，每轮2张图片（B02和C03模式）
	imagePaths := []string{}
	testImage := "pic/test_image.png"

	// 检查测试图片是否存在
	if _, err := os.Stat(testImage); os.IsNotExist(err) {
		fmt.Printf("❌ 测试图片不存在: %s\n", testImage)
		return
	}

	// 生成10轮次的图片路径（实际应用中这些是真实的截图）
	for round := 1; round <= 10; round++ {
		imagePaths = append(imagePaths, testImage) // B02模式
		imagePaths = append(imagePaths, testImage) // C03模式
	}

	fmt.Printf("开始处理10轮次（共%d张图片）...\n", len(imagePaths))

	// 批量处理
	results, err := service.ProcessBatch(imagePaths)
	if err != nil {
		fmt.Printf("批量处理失败: %v\n", err)
		return
	}

	// 分析结果
	fmt.Printf("\n=== 10轮次处理结果汇总 ===\n")
	successCount := 0
	totalTime := 0.0

	for i, result := range results {
		round := (i / 2) + 1
		mode := "B02"
		if i%2 == 1 {
			mode = "C03"
		}

		if result.Success {
			successCount++
			totalTime += result.ProcessingTime
			fmt.Printf("轮次%d-%s: ✓ [%s] (%.3fs)\n",
				round, mode, result.FinalText, result.ProcessingTime)
		} else {
			fmt.Printf("轮次%d-%s: ❌ 识别失败\n", round, mode)
		}
	}

	avgTime := totalTime / float64(len(results))
	fmt.Printf("\n=== 性能统计 ===\n")
	fmt.Printf("成功率: %d/%d (%.1f%%)\n", successCount, len(results),
		float64(successCount)/float64(len(results))*100)
	fmt.Printf("平均处理时间: %.3f秒\n", avgTime)
	fmt.Printf("总处理时间: %.3f秒\n", totalTime)

	// 与原方案对比
	fmt.Printf("\n=== 性能对比 ===\n")
	fmt.Printf("原扣子API方案: 10-30秒/张 × 20张 = 200-600秒\n")
	fmt.Printf("轻量化OCR方案: %.3f秒/张 × 20张 = %.3f秒\n", avgTime, totalTime)
	fmt.Printf("性能提升: %.1f倍\n", 400.0/totalTime) // 使用中位数400秒计算
}

func main() {
	fmt.Println("=== 轻量化OCR Go集成测试 ===")

	// 测试真实医疗截图
	realImagePath := `F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png`

	// 检查文件是否存在
	if _, err := os.Stat(realImagePath); os.IsNotExist(err) {
		fmt.Printf("❌ 真实医疗截图不存在: %s\n", realImagePath)
		fmt.Println("运行10轮次模拟测试...")
		simulate10RoundProcess()
		return
	}

	fmt.Printf("✓ 找到真实医疗截图: %s\n", realImagePath)

	// 创建服务
	service := NewLightweightOCRService()

	// 检查环境
	if err := service.CheckEnvironment(); err != nil {
		fmt.Printf("环境检查失败: %v\n", err)
		return
	}

	// 识别真实医疗截图
	fmt.Println("\n=== 识别真实医疗截图 ===")
	result, err := service.RecognizeImage(realImagePath)
	if err != nil {
		fmt.Printf("识别失败: %v\n", err)
		return
	}

	// 分析结果
	service.AnalyzeResult(result)

	// 输出详细结果
	fmt.Printf("\n=== 真实医疗截图识别结果 ===\n")
	fmt.Printf("文件名: %s\n", realImagePath)
	fmt.Printf("识别文本: [%s]\n", result.FinalText)
	fmt.Printf("置信度: %.2f%%\n", result.Confidence*100)
	fmt.Printf("处理时间: %.3f秒\n", result.ProcessingTime)
	fmt.Printf("使用引擎: %s\n", result.Engine)
	fmt.Printf("识别成功: %t\n", result.Success)

	if len(result.CorrectionsApplied) > 0 {
		fmt.Printf("应用校正: %v\n", result.CorrectionsApplied)
	}

	// 业务逻辑验证
	fmt.Printf("\n=== 业务逻辑验证 ===\n")
	if result.Success && result.FinalText != "" {
		if strings.Contains(result.FinalText, "0.000") {
			fmt.Printf("✓ 识别到目标数字模式 '0.000'\n")

			// 提取器官名称
			parts := strings.Split(result.FinalText, "0.000")
			if len(parts) > 1 {
				organName := strings.TrimSpace(parts[1])
				if organName != "" {
					fmt.Printf("✓ 提取器官名称: [%s]\n", organName)

					// 验证是否为已知的医疗术语
					knownTerms := []string{"腹部第1腰椎水平截面", "腹部第2腰椎水平截面", "腹部第3腰椎水平截面", "心脏", "肝脏", "肾脏"}
					for _, term := range knownTerms {
						if strings.Contains(organName, term) {
							fmt.Printf("✓ 匹配已知医疗术语: %s\n", term)
							break
						}
					}
				}
			}
		} else {
			fmt.Printf("⚠ 未识别到目标数字模式 '0.000'\n")
		}

		fmt.Printf("✓ 可用于业务流程\n")
	} else {
		fmt.Printf("❌ 识别失败，需要人工处理\n")
	}
}
