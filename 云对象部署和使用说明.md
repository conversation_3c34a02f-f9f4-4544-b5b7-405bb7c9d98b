# 云对象部署和使用说明

## 🎯 云对象方案优势

相比云函数，云对象具有以下优势：
- **更简洁的调用方式** - 直接调用对象方法
- **更好的代码组织** - 多个相关方法在一个对象中
- **更强的类型支持** - 支持参数验证和文档
- **更灵活的部署** - 支持URL化访问

## 📁 文件结构

### 已创建的文件：
```
├── hc-detection-screenshot-records.schema.json  # 数据库表结构
├── 云对象示例代码-hc-screenshot-records.obj.js   # 云对象代码
└── 云对象部署和使用说明.md                      # 本文档
```

### 本地代码修改：
```
app/services/api_service.go  # 已修改为调用云对象
app/models/config.go        # 已添加ScreenshotRecordParams结构
app.go                      # 已集成截图记录功能
```

## 🚀 部署步骤

### 1. 在DCloud中创建数据库表
1. 登录 [DCloud开发者中心](https://dev.dcloud.net.cn/)
2. 进入您的项目 → 云数据库 → 数据库表
3. 创建新表：`hc-detection-screenshot-records`
4. 复制 `hc-detection-screenshot-records.schema.json` 的内容到表结构中

### 2. 创建云对象
1. 在DCloud项目中 → 云函数/云对象 → 新建云对象
2. 云对象名称：`hc-screenshot-records`
3. 复制 `云对象示例代码-hc-screenshot-records.obj.js` 的内容
4. 上传并部署云对象

### 3. 配置云对象URL化（可选）
如果需要HTTP访问，可以配置URL化：
```
https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-screenshot-records/createOrUpdateScreenshotRecord
```

## 🔧 云对象方法说明

### 1. createOrUpdateScreenshotRecord
**功能**: 创建或更新截图记录

**参数**:
```javascript
{
  "user_name": "张三",           // 用户姓名 (必需)
  "user_id": "user123",         // 用户ID
  "site_id": "site001",         // 网点ID
  "device_no": "AABBCCDDEEFF",  // 设备MAC地址
  "registration_id": "reg001",  // 挂号记录ID
  "round": 1,                   // 轮次 1-10
  "analysis_type": "B02",       // 分析类型 A01/B02/C03 (必需)
  "detected_organ": "心脏",      // 识别的器官名称
  "filename": "张三_心脏_RPT20240616001_R01_B02.png", // 文件名 (必需)
  "cloud_url": "https://...",   // 云存储URL
  "operator_name": "操作员",     // 操作员姓名
  "ocr_success": true           // OCR是否成功
}
```

**返回值**:
```javascript
{
  "errCode": 0,
  "errMsg": "截图记录创建成功",
  "data": {
    "action": "created",        // created/updated
    "session_id": "SES_张三_20240616",
    "record_id": "record_id",
    "current_round": 1
  }
}
```

### 2. getSessionsByUser
**功能**: 查询用户检测记录

**参数**:
```javascript
{
  "user_name": "张三",          // 用户姓名
  "site_id": "site001",        // 网点ID
  "date": "2024-06-16",        // 日期 (可选)
  "page": 1,                   // 页码
  "pageSize": 20               // 每页数量
}
```

## 📊 数据库记录示例

### 创建新记录时：
```json
{
  "session_id": "SES_张三_20240616",
  "user_name": "张三",
  "user_id": "user123",
  "site_id": "site001",
  "current_round": 1,
  "total_rounds": 10,
  "screenshots": [
    {
      "round": 1,
      "analysis_type1": "B02",
      "detected_organ": "心脏",
      "B02_filename": "张三_心脏_RPT20240616001_R01_B02.png",
      "B02_screenshot_cloud_url": "https://...",
      "screenshot_time": "2024-06-16T10:30:00.000Z",
      "ocr_success": true,
      "coze_workflow_called": false
    }
  ],
  "session_status": "CREATED"
}
```

### 更新记录时（同一轮次的C03截图）：
```json
{
  "screenshots": [
    {
      "round": 1,
      "analysis_type1": "B02",
      "analysis_type2": "C03",
      "detected_organ": "心脏",
      "B02_filename": "张三_心脏_RPT20240616001_R01_B02.png",
      "C03_filename": "张三_心脏_RPT20240616001_R01_C03.png",
      "B02_screenshot_cloud_url": "https://...",
      "C03_screenshot_cloud_url": "https://...",
      "screenshot_time": "2024-06-16T10:30:00.000Z",
      "ocr_success": true,
      "coze_workflow_called": false
    }
  ],
  "session_status": "IN_PROGRESS"
}
```

## 🔄 状态管理逻辑

### 会话状态 (session_status)：
- **CREATED**: 第一轮第一次B02截图时创建
- **IN_PROGRESS**: 第1-9轮截图过程中
- **COMPLETED**: 第10轮C03截图完成时
- **CANCELLED**: 用户取消检测时

### 轮次管理：
- 每轮包含B02和C03两次截图
- 同一轮次的截图会更新到同一个screenshots数组元素中
- 不同轮次会创建新的screenshots数组元素

## 🧪 测试方法

### 1. 本地测试
运行应用程序，按快捷键进行截图：
- `Ctrl+Shift+B` - 生化平衡分析 (B02)
- `Ctrl+Shift+C` - 病理形态学分析 (C03)

### 2. 观察控制台输出
```
[DEBUG] 步骤2.5: 更新截图记录到云数据库...
Screenshot Record Request JSON: {"user_name":"张三",...}
[DEBUG] 步骤2.5成功: 截图记录已更新到云数据库
```

### 3. 检查数据库记录
在DCloud控制台查看 `hc-detection-screenshot-records` 表中的记录。

## ⚠️ 注意事项

### 1. 权限设置
云对象中使用了 `admin` 角色跳过权限验证：
```javascript
await dbJQL.setUser({
    role: ['admin']
});
```

### 2. 错误处理
- 数据库更新失败不会影响主截图流程
- 会在控制台显示警告信息
- 支持网络重试机制

### 3. 性能优化
- 使用异步更新，不阻塞主流程
- 支持批量更新操作
- 合理的索引设计

## 🎯 下一步优化

1. **轮次自动管理** - 根据数据库记录自动计算当前轮次
2. **状态同步** - 实现前端状态显示
3. **数据分析** - 添加统计和报表功能
4. **离线支持** - 网络断开时的本地缓存

## 📞 技术支持

如果在部署过程中遇到问题：
1. 检查云对象是否正确部署
2. 确认数据库表结构是否正确
3. 查看云对象运行日志
4. 检查网络连接和权限配置

现在您可以按照这个说明在DCloud中部署云对象，然后测试整个截图记录功能！
