@echo off
echo 正在构建 MagneticOperator...

REM 使用 wails build 构建应用，包含OCR支持
echo 构建包含OCR功能的版本...
wails build -tags ocr

REM 检查构建是否成功
if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建成功，正在复制配置文件...

REM 确保目录存在
if not exist "build\bin\config" mkdir "build\bin\config"
if not exist "build\bin\logs" mkdir "build\bin\logs"
if not exist "build\bin\pic" mkdir "build\bin\pic"

REM 复制配置文件
copy "config\app_config.json" "build\bin\config\app_config.json" >nul
if %ERRORLEVEL% neq 0 (
    echo 警告：复制配置文件失败！
) else (
    echo 配置文件复制成功
)

echo 构建完成！可执行文件位于：build\bin\MagneticOperator.exe
pause