#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher

@dataclass
class OCRResult:
    text: str
    confidence: float
    engine: str
    bbox: Optional[List] = None

@dataclass
class FusionResult:
    final_text: str
    confidence: float
    source_engines: List[str]
    corrections_applied: List[str]
    raw_results: List[OCRResult]

class MedicalTerminologyDatabase:
    """医疗术语数据库"""
    
    def __init__(self):
        # 器官部位标准术语库
        self.organ_terms = {
            "腹部第1腰椎水平截面": ["腹部第1腰椎水平截面", "腹部第一腰椎水平截面"],
            "腹部第2腰椎水平截面": ["腹部第2腰椎水平截面", "腹部第二腰椎水平截面"],
            "腹部第3腰椎水平截面": ["腹部第3腰椎水平截面", "腹部第三腰椎水平截面"],
            "心脏": ["心脏", "心臟"],
            "肝脏": ["肝脏", "肝臟"],
            "肾脏": ["肾脏", "腎臟"],
            "脾脏": ["脾脏", "脾臟"],
            "胆囊": ["胆囊", "膽囊"],
            "胰腺": ["胰腺", "胰臟"],
            "胃": ["胃"],
            "十二指肠": ["十二指肠", "十二指腸"],
        }
        
        # 常见OCR误识别映射
        self.ocr_corrections = {
            "胆囊第1腰椎水平截面": "腹部第1腰椎水平截面",
            "胆部第1腰椎水平截面": "腹部第1腰椎水平截面", 
            "腹囊第1腰椎水平截面": "腹部第1腰椎水平截面",
            "服部第1腰椎水平截面": "腹部第1腰椎水平截面",
            "复部第1腰椎水平截面": "腹部第1腰椎水平截面",
            "0,000": "0.000",
            "O.000": "0.000",
            "0.00O": "0.000",
            "0.0O0": "0.000",
        }
        
        # 数字模式
        self.number_patterns = [
            r'[0-9][.,][0-9]{3}',  # 0.000 或 0,000
            r'[O0][.,][O0]{3}',    # O.000 等变体
        ]

    def find_best_match(self, text: str) -> Tuple[str, float]:
        """找到最佳匹配的标准术语"""
        # 直接映射校正
        if text in self.ocr_corrections:
            return self.ocr_corrections[text], 1.0
        
        # 模糊匹配
        best_match = ""
        best_score = 0.0
        
        for standard_term, variants in self.organ_terms.items():
            for variant in variants:
                score = SequenceMatcher(None, text, variant).ratio()
                if score > best_score and score > 0.7:  # 70%相似度阈值
                    best_match = standard_term
                    best_score = score
        
        return best_match if best_match else text, best_score

    def extract_target_pattern(self, text: str) -> Optional[str]:
        """提取目标模式 (0.000 + 器官名称)"""
        # 标准化数字格式
        normalized_text = text
        for wrong, correct in self.ocr_corrections.items():
            normalized_text = normalized_text.replace(wrong, correct)
        
        # 查找数字模式
        for pattern in self.number_patterns:
            match = re.search(pattern, normalized_text)
            if match:
                # 尝试提取后面的器官名称
                start_pos = match.end()
                remaining_text = normalized_text[start_pos:].strip()
                
                if remaining_text:
                    # 查找最佳匹配的器官术语
                    organ_match, confidence = self.find_best_match(remaining_text)
                    if confidence > 0.5:
                        return f"0.000 {organ_match}"
        
        return None

class DualOCRFusionSystem:
    """双OCR引擎融合系统"""
    
    def __init__(self):
        self.terminology_db = MedicalTerminologyDatabase()
        self.engines_available = self._check_available_engines()
    
    def _check_available_engines(self) -> Dict[str, bool]:
        """检查可用的OCR引擎"""
        engines = {
            "easyocr": False,
            "paddleocr": False,
            "tesseract": False
        }
        
        try:
            import easyocr
            engines["easyocr"] = True
            print("OK: EasyOCR available")
        except ImportError:
            print("NO: EasyOCR not available")

        try:
            import paddleocr
            engines["paddleocr"] = True
            print("OK: PaddleOCR available")
        except ImportError:
            print("NO: PaddleOCR not available")

        try:
            import subprocess
            subprocess.run(["tesseract", "--version"], capture_output=True, check=True)
            engines["tesseract"] = True
            print("OK: Tesseract available")
        except:
            print("NO: Tesseract not available")
        
        return engines
    
    def run_easyocr(self, image_path: str) -> List[OCRResult]:
        """运行EasyOCR"""
        if not self.engines_available["easyocr"]:
            return []
        
        try:
            import easyocr
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            results = reader.readtext(image_path)
            
            ocr_results = []
            for bbox, text, confidence in results:
                if confidence > 0.3:  # 置信度阈值
                    ocr_results.append(OCRResult(
                        text=text,
                        confidence=confidence,
                        engine="EasyOCR",
                        bbox=bbox
                    ))
            
            return ocr_results
        except Exception as e:
            print(f"EasyOCR error: {e}")
            return []
    
    def run_paddleocr(self, image_path: str) -> List[OCRResult]:
        """运行PaddleOCR"""
        if not self.engines_available["paddleocr"]:
            return []
        
        try:
            from paddleocr import PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
            results = ocr.ocr(image_path, cls=True)
            
            ocr_results = []
            for idx in range(len(results)):
                res = results[idx]
                if res:
                    for line in res:
                        bbox = line[0]
                        text_info = line[1]
                        text = text_info[0]
                        confidence = text_info[1]
                        
                        if confidence > 0.3:
                            ocr_results.append(OCRResult(
                                text=text,
                                confidence=confidence,
                                engine="PaddleOCR",
                                bbox=bbox
                            ))
            
            return ocr_results
        except Exception as e:
            print(f"PaddleOCR error: {e}")
            return []
    
    def run_tesseract(self, image_path: str) -> List[OCRResult]:
        """运行Tesseract"""
        if not self.engines_available["tesseract"]:
            return []
        
        try:
            import subprocess
            cmd = ["tesseract", image_path, "stdout", "-l", "chi_sim+eng"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            lines = result.stdout.strip().split('\n')
            ocr_results = []
            
            for line in lines:
                line = line.strip()
                if line:
                    ocr_results.append(OCRResult(
                        text=line,
                        confidence=0.8,  # Tesseract不提供置信度，使用默认值
                        engine="Tesseract"
                    ))
            
            return ocr_results
        except Exception as e:
            print(f"Tesseract error: {e}")
            return []
    
    def fuse_results(self, all_results: List[List[OCRResult]]) -> FusionResult:
        """融合多个OCR引擎的结果"""
        # 收集所有结果
        all_texts = []
        for engine_results in all_results:
            for result in engine_results:
                all_texts.append(result)
        
        if not all_texts:
            return FusionResult(
                final_text="",
                confidence=0.0,
                source_engines=[],
                corrections_applied=[],
                raw_results=[]
            )
        
        # 查找目标模式
        target_candidates = []
        corrections_applied = []
        
        for result in all_texts:
            # 尝试提取目标模式
            target = self.terminology_db.extract_target_pattern(result.text)
            if target:
                target_candidates.append((target, result.confidence, result.engine))
        
        if target_candidates:
            # 选择置信度最高的结果
            best_candidate = max(target_candidates, key=lambda x: x[1])
            final_text = best_candidate[0]
            confidence = best_candidate[1]
            source_engines = [best_candidate[2]]
            
            # 检查是否应用了校正
            for result in all_texts:
                if result.text != final_text and final_text in self.terminology_db.ocr_corrections.values():
                    corrections_applied.append(f"{result.text} → {final_text}")
        else:
            # 没有找到目标模式，返回置信度最高的结果
            best_result = max(all_texts, key=lambda x: x.confidence)
            final_text = best_result.text
            confidence = best_result.confidence
            source_engines = [best_result.engine]
        
        return FusionResult(
            final_text=final_text,
            confidence=confidence,
            source_engines=source_engines,
            corrections_applied=corrections_applied,
            raw_results=all_texts
        )
    
    def process_image(self, image_path: str) -> FusionResult:
        """处理图片，返回融合结果"""
        print(f"=== Dual OCR Fusion System ===")
        print(f"Processing image: {image_path}")
        print(f"Available engines: {[k for k, v in self.engines_available.items() if v]}")
        
        # 运行所有可用的OCR引擎
        all_results = []
        
        if self.engines_available["easyocr"]:
            print("\nRunning EasyOCR...")
            easy_results = self.run_easyocr(image_path)
            all_results.append(easy_results)
            print(f"EasyOCR found {len(easy_results)} text blocks")
        
        if self.engines_available["paddleocr"]:
            print("\nRunning PaddleOCR...")
            paddle_results = self.run_paddleocr(image_path)
            all_results.append(paddle_results)
            print(f"PaddleOCR found {len(paddle_results)} text blocks")
        
        if self.engines_available["tesseract"]:
            print("\nRunning Tesseract...")
            tesseract_results = self.run_tesseract(image_path)
            all_results.append(tesseract_results)
            print(f"Tesseract found {len(tesseract_results)} text blocks")
        
        # 融合结果
        print("\nFusing results...")
        fusion_result = self.fuse_results(all_results)
        
        # 显示结果
        print(f"\n=== Fusion Results ===")
        print(f"Final text: [{fusion_result.final_text}]")
        print(f"Confidence: {fusion_result.confidence:.2f}")
        print(f"Source engines: {fusion_result.source_engines}")
        if fusion_result.corrections_applied:
            print(f"Corrections applied: {fusion_result.corrections_applied}")
        
        print(f"\n=== Raw Results ===")
        for result in fusion_result.raw_results:
            print(f"{result.engine}: [{result.text}] (conf: {result.confidence:.2f})")
        
        return fusion_result

def main():
    if len(sys.argv) < 2:
        print("Usage: python dual_ocr_fusion_system.py <image_path>")
        return
    
    image_path = sys.argv[1]
    if not os.path.exists(image_path):
        print(f"Error: Image file not found: {image_path}")
        return
    
    fusion_system = DualOCRFusionSystem()
    result = fusion_system.process_image(image_path)
    
    # 输出JSON格式结果供Go程序使用
    output = {
        "final_text": result.final_text,
        "confidence": result.confidence,
        "source_engines": result.source_engines,
        "corrections_applied": result.corrections_applied,
        "success": bool(result.final_text)
    }
    
    print(f"\n=== JSON Output ===")
    print(json.dumps(output, ensure_ascii=False))

if __name__ == "__main__":
    main()
