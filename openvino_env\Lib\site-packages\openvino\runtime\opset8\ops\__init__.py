# -*- coding: utf-8 -*-
# Copyright (C) 2018-2025 Intel Corporation
# SPDX-License-Identifier: Apache-2.0

from openvino.opset8.ops import adaptive_avg_pool
from openvino.opset8.ops import adaptive_max_pool
from openvino.opset8.ops import deformable_convolution
from openvino.opset8.ops import detection_output
from openvino.opset8.ops import gather
from openvino.opset8.ops import gather_nd
from openvino.opset8.ops import if_op
from openvino.opset8.ops import i420_to_bgr
from openvino.opset8.ops import i420_to_rgb
from openvino.opset8.ops import matrix_nms
from openvino.opset8.ops import max_pool
from openvino.opset8.ops import multiclass_nms
from openvino.opset8.ops import nv12_to_bgr
from openvino.opset8.ops import nv12_to_rgb
from openvino.opset8.ops import prior_box
from openvino.opset8.ops import random_uniform
from openvino.opset8.ops import slice
from openvino.opset8.ops import softmax
