<template>
  <div id="app" :class="{ 'expanded': windowState.isExpanded, 'compact': !windowState.isExpanded }">
    <!-- 紧凑模式头部 -->
    <div class="compact-header" v-if="!windowState.isExpanded">
      <div class="title-bar">
        <span class="app-title">磁感分析操作台</span>
        <div class="window-controls">
          <button @click="toggleWindowSize" class="expand-btn" title="展开 (F11)">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M2 2v5h2V4h3V2H2zM9 2v2h3v3h2V2H9zM4 9H2v5h5v-2H4V9zM14 9h-2v3H9v2h5V9z" fill="currentColor"/>
            </svg>
          </button>
          <button @click="minimizeWindow" class="minimize-btn" title="最小化 (F9)">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M3 8h10v1H3V8z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 紧凑模式状态栏 -->
      <div class="compact-status">
        <div class="status-item">
          <span class="label">检测站点:</span>
          <span class="value">{{ config?.SiteInfo?.SiteName || '加载中...' }}</span>
        </div>
        <div class="status-item">
          <span class="label">站点编号:</span>
          <span class="value">{{ config?.SiteInfo?.SiteID || '加载中...' }}</span>
        </div>
        <div class="status-item">
          <span class="label">设备编号:</span>
          <span class="value">{{ config?.device_info?.device_no || '未设置' }}</span>
        </div>
        <div class="status-item">
          <span class="label">今日报到:</span>
          <span class="value">{{ todayPatientCount }}人</span>
        </div>
        <div class="status-item">
          <span class="label">今日检测:</span>
          <span class="value">{{ currentRegistrationNumber }}人</span>
        </div>
      </div>
    </div>

    <!-- 紧凑模式主要内容 -->
    <div class="compact-content" v-if="!windowState.isExpanded">
      <!-- 报到二维码 -->
        <div class="registration-qrcode-container">
          <img v-if="registrationQRCodeUrl" :src="registrationQRCodeUrl" alt="报到二维码" class="registration-qrcode-image" />
          <p v-else>正在生成报到二维码...</p>
        </div>

      <!-- 截图、患者二维码、展开按钮 -->
      <div class="quick-actions">
        <button @click="handleQuickScreenshot" class="action-btn primary">
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path d="M3 4h14v12H3V4zm1 1v10h12V5H4z" fill="currentColor"/>
            <circle cx="10" cy="10" r="2" fill="currentColor"/>
          </svg>
          <span>截图</span>
        </button>
        
        <button @click="generateQRCode" class="action-btn">
          <svg width="20" height="20" viewBox="0 0 20 20">
            <rect x="2" y="2" width="6" height="6" fill="currentColor"/>
            <rect x="12" y="2" width="6" height="6" fill="currentColor"/>
            <rect x="2" y="12" width="6" height="6" fill="currentColor"/>
          </svg>
          <span>二维码</span>
        </button>
        
        <button @click="toggleWindowSize" class="action-btn expand-main">
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path d="M3 3v6h2V5h4V3H3zM11 3v2h4v4h2V3h-6zM5 11H3v6h6v-2H5v-4zM17 11h-2v4h-4v2h6v-6z" fill="currentColor"/>
          </svg>
          <span>展开</span>
        </button>
      </div>

      <!-- 当前候检者卡片 -->
      <div class="current-patient-section">
        <div class="section-header">
          <span>当前候检者</span>
        </div>
        <div class="current-patient-card" v-if="currentPatient">
          <div class="current-patient-info">
            <span class="current-patient-name">{{ currentPatient.name }}</span>
            <span class="current-patient-number">{{ currentPatient.number }}</span>
          </div>
        </div>
        <div class="current-patient-card empty" v-else>
          <div class="current-patient-info">
            <span class="empty-text">暂无候检者</span>
          </div>
        </div>
      </div>

      <!-- 候检者列表（紧凑版） -->
        <div class="patient-section">
          <div class="section-header">
            <span>候检者列表 ({{ registrations.length }})</span>
            <button @click="refreshRegistrations" class="refresh-btn" :disabled="isRefreshing">🔄</button>
          </div>
          <div class="patient-list">
          <div v-for="(registration, index) in displayedRegistrations" :key="index" 
               class="patient-item" 
               :class="{ 'selected': selectedPatientIndex === index }"
               @click="selectPatient(index)">
            <input type="radio" 
                   :name="'patient-radio'" 
                   :value="index" 
                   v-model="selectedPatientIndex" 
                   class="patient-radio">
            <span class="patient-name">{{ registration.name }}</span>
            <span class="patient-age">{{ calculateAge(registration.userInfo[0].birth_date) }}</span>
            <span class="patient-gender">{{ formatGender(registration.userInfo[0].gender) }}</span>
            <span class="patient-number">{{ registration.number }}</span>
            <span class="patient-time">{{ formatTime(registration.register_time) }}</span>
          </div>
          <div v-if="registrations.length > displayLimit" class="more-patients" @click="loadMoreRegistrations">
            点击加载更多 (还有 {{ registrations.length - displayLimit }} 位候检者)
          </div>
        </div>
      </div>

      <!-- 最新二维码显示 -->
      <div class="compact-qrcode" v-if="latestQRCode">
        <div class="section-header">
          <span>当前二维码</span>
        </div>
        <div class="qrcode-display">
          <img :src="latestQRCode" alt="患者二维码" />
        </div>
      </div>
    </div>

    <!-- 展开模式 -->
    <div class="expanded-content" v-if="windowState.isExpanded">
      <!-- 展开模式头部 -->
      <div class="expanded-header">
        <h1>磁感分析操作台</h1>
        <div class="header-controls">
          <div class="status-info">
            <span class="site-info">{{ siteInfo }}</span>
            <span class="time">{{ currentTime }}</span>
          </div>
          <div class="window-controls">
            <button @click="toggleWindowPosition" class="position-btn" :title="'移到' + (windowState.position === 'left' ? '右侧' : '左侧') + ' (F10)'">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M2 3h12v10H2V3zm1 1v8h10V4H3z" fill="currentColor"/>
                <path :d="windowState.position === 'left' ? 'M11 6v4h2V6h-2z' : 'M3 6v4h2V6H3z'" fill="currentColor"/>
              </svg>
            </button>
            <button @click="toggleWindowSize" class="collapse-btn" title="收缩 (F11)">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M6 6h4v4H6V6z" fill="currentColor"/>
              </svg>
            </button>
            <button @click="minimizeWindow" class="minimize-btn" title="最小化 (F9)">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M3 8h10v1H3V8z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 展开模式主要内容 -->
      <div class="expanded-main">
        <!-- 左侧面板 -->
        <div class="left-panel">
          <SiteInfoPanel 
            :config="config" 
            @update-site="updateSiteInfo"
          />
          <CropSettingsPanel 
            :config="config" 
            @update-crop="updateCropSettings"
          />
          <DeviceInfoPanel :config="config" />
        </div>

        <!-- 中间面板 -->
        <div class="center-panel">
          <ScreenshotPanel 
            :modes="modeConfig"
            @take-screenshot="handleScreenshot"
            @process-complete="handleProcessComplete"
          />
          <QRCodePanel 
            @generate-qrcode="generateQRCode"
          />
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel">
          <PatientPanel 
            :patients="patients"
            @add-patient="addPatient"
            @remove-patient="removePatient"
            @clear-patients="clearPatients"
          />
          <LogPanel :logs="logs" />
        </div>
      </div>

      <!-- 展开模式状态栏 -->
      <div class="expanded-footer">
        <div class="stats">
          <span>今日患者: {{ todayPatientCount }}</span>
          <span>当前挂号: {{ currentRegistrationNumber }}</span>
          <span>系统状态: {{ systemStatus }}</span>
        </div>
      </div>
    </div>

    <!-- 添加患者对话框 -->
    <div v-if="showAddPatientDialog" class="dialog-overlay" @click="showAddPatientDialog = false">
      <div class="dialog" @click.stop>
        <h3>添加患者</h3>
        <form @submit.prevent="handleAddPatient">
          <input v-model="newPatient.name" placeholder="患者姓名" required />
          <input v-model="newPatient.registrationNumber" placeholder="挂号号码" required />
          <div class="dialog-buttons">
            <button type="button" @click="showAddPatientDialog = false">取消</button>
            <button type="submit">添加</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
    </div>

    <!-- 通知消息 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
  </div>
</template>

<script>
import { GetConfig, UpdateSiteInfo, UpdateCropSettings, ProcessScreenshotAndUpload, 
         GenerateQRCode, AddPatient, GetPatientList, RemovePatient, ClearPatientList,
         GetCurrentRegistrationNumber, GetTodayPatientCount, GetModeConfig,
         ToggleWindowSize, SetWindowPosition, GetWindowState, MinimizeWindow,
         HandleKeyboardShortcut, GenerateRegistrationQRCode, GetRegistrations, GetSiteInfo } from '../wailsjs/go/main/App'

import SiteInfoPanel from './components/SiteInfoPanel.vue'
import CropSettingsPanel from './components/CropSettingsPanel.vue'
import DeviceInfoPanel from './components/DeviceInfoPanel.vue'
import ScreenshotPanel from './components/ScreenshotPanel.vue'
import QRCodePanel from './components/QRCodePanel.vue'
import PatientPanel from './components/PatientPanel.vue'
import LogPanel from './components/LogPanel.vue'

export default {
  name: 'App',
  components: {
    SiteInfoPanel,
    CropSettingsPanel,
    DeviceInfoPanel,
    ScreenshotPanel,
    QRCodePanel,
    PatientPanel,
    LogPanel
  },
  data() {
    return {
      config: {
        SiteInfo: {
          SiteName: '',
          SiteID: ''
        },
        device_info: {
          mac_address: '',
          device_no: ''
        }
      },
      modeConfig: {},
      patients: [],
      logs: [],
      currentTime: '',
      loading: false,
      loadingMessage: '',
      notification: {
        show: false,
        type: 'info',
        message: ''
      },
      systemStatus: '正常',
      timeInterval: null,
      // 窗体状态
      windowState: {
        isExpanded: false,
        position: 'left'
      },
      // 新增字段
      latestQRCode: null,
      registrationQRCodeUrl: '', // 用于存储报到二维码URL
      showAddPatientDialog: false,
      newPatient: {
        name: '',
        registrationNumber: ''
      },
      // 候检者相关
      registrations: [], // 候检者列表
      isRefreshing: false, // 刷新状态
      pollTimer: null, // 轮询定时器
      displayLimit: 5, // 显示的候检者数量限制
      selectedPatientIndex: 0, // 当前选中的候检者索引
      // 自动收缩定时器
      inactivityTimer: null,
      lastActivityTime: Date.now(),
      // 配置监听器定时器
      configWatcherTimer: null
    }
  },
  computed: {
    siteInfo() {
      if (!this.config || !this.config.SiteInfo || !this.config.SiteInfo.SiteName) return '加载中...'
      return `${this.config.SiteInfo.SiteName} (${this.config.SiteInfo.SiteID})`
    },
    todayPatientCount() {
      return this.registrations.length
    },
    currentRegistrationNumber() {
      return this.patients.length + 1
    },
    displayedRegistrations() {
      return this.registrations.slice(0, this.displayLimit)
    },
    currentPatient() {
      if (this.registrations.length > 0 && this.selectedPatientIndex >= 0 && this.selectedPatientIndex < this.registrations.length) {
        return this.registrations[this.selectedPatientIndex]
      }
      return null
    }
  },
  async mounted() {
    await this.initializeApp()
    await this.initializeWindowState()
    this.startTimeUpdate()
    this.setupKeyboardShortcuts()



    this.resetInactivityTimer()
    await this.generateRegistrationQRCode() // 生成报到二维码
    await this.loadRegistrations() // 加载候检者列表
    
    // 监听鼠标和键盘活动
    document.addEventListener('mousedown', this.updateActivity)
    document.addEventListener('keydown', this.updateActivity)
  },
  beforeUnmount() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer)
    }
    // if (this.configWatcherTimer) {
    //   clearInterval(this.configWatcherTimer)
    // }
    if (this.pollTimer) {
      clearInterval(this.pollTimer)
    }
    // 移除事件监听器
    document.removeEventListener('mousedown', this.updateActivity)
    document.removeEventListener('keydown', this.updateActivity)
  },
  methods: {
    formatGender(gender) {
      console.log('formatGender - gender:', gender, 'type:', typeof gender);
      if (gender === 1) {
        return '男';
      } else if (gender === 2) {
        return '女';
      } else {
        return '未知';
      }
    },
    calculateAge(birthDate) {
      if (!birthDate) return '';
      const birth = new Date(birthDate);
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const m = today.getMonth() - birth.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      return age + '岁';
    },
    async initializeApp() {
      try {
        this.showLoading('正在初始化应用...')
        
        // 加载配置
        this.config = await GetConfig()
        console.log('initializeApp - config after GetConfig():', JSON.stringify(this.config, null, 2)); // 调试日志
        
        // 处理device_no字段，从mac_address生成去除冒号的版本
        if (this.config.device_info && this.config.device_info.mac_address) {
          this.config.device_info.device_no = this.config.device_info.mac_address.replace(/:/g, '')
        }

        // 检查配置中是否已有站点信息，避免重复调用API
        if (!this.config.SiteInfo || !this.config.SiteInfo.SiteID) {
          try {
            console.log('配置中无站点信息，主动获取...');
            const siteInfo = await GetSiteInfo();
            console.log('成功获取站点信息:', siteInfo);
            
            // 更新配置中的站点信息 - 注意字段名大小写
            this.config.SiteInfo = {
              SiteName: siteInfo.site_name,
              SiteID: siteInfo.site_id,
              SiteType: siteInfo.site_type,
              ParentOrg: siteInfo.parent_org,
              Location: siteInfo.location,
              Contact: siteInfo.contact
            };
            this.showNotification(`站点信息已加载: ${siteInfo.site_name || '未知站点'}`, 'success');
          } catch (error) {
            console.error('获取站点信息失败:', error);
            this.showNotification('获取站点信息失败，请检查网络连接', 'error');
          }
        } else {
          console.log('使用已缓存的站点信息:', this.config.SiteInfo.SiteName);
          this.showNotification(`站点信息已就绪: ${this.config.SiteInfo.SiteName}`, 'info');
        }

        // 保留事件监听器作为备用机制
        window.runtime.EventsOn('siteInfoUpdated', (newSiteInfo) => {
          console.log('siteInfoUpdated - newSiteInfo:', JSON.stringify(newSiteInfo, null, 2)); // 调试日志
          if (this.config) {
            // 直接更新 SiteInfo 对象 (Vue 3) - 注意字段名大小写
            if (newSiteInfo && typeof newSiteInfo === 'object') {
              this.config.SiteInfo = {
                SiteName: newSiteInfo.site_name || newSiteInfo.SiteName,
                SiteID: newSiteInfo.site_id || newSiteInfo.SiteID,
                SiteType: newSiteInfo.site_type || newSiteInfo.SiteType,
                ParentOrg: newSiteInfo.parent_org || newSiteInfo.ParentOrg,
                Location: newSiteInfo.location || newSiteInfo.Location,
                Contact: newSiteInfo.contact || newSiteInfo.Contact
              };
              console.log('通过事件更新站点信息:', this.config.SiteInfo);
              this.showNotification(`站点信息已更新: ${newSiteInfo.site_name || newSiteInfo.SiteName || '未知站点'}`, 'success');
            } else {
              console.warn('siteInfoUpdated - newSiteInfo is not an object or is null:', newSiteInfo);
            }
          }
        });
        
        // 加载模式配置
        this.modeConfig = await GetModeConfig()
        
        // 加载患者列表
        this.patients = await GetPatientList()
        
        // 启动配置监听器，定期检查配置更新
        this.startConfigWatcher()
        
        this.hideLoading()
        this.showNotification('应用初始化成功', 'success')
      } catch (error) {
        this.hideLoading()
        this.showNotification(`初始化失败: ${error}`, 'error')
        console.error('初始化应用失败:', error)
      }
    },

    async generateRegistrationQRCode() {
      try {
        const result = await GenerateRegistrationQRCode(); // 调用后端方法
        if (result && result.qr_code_base64) {
          this.registrationQRCodeUrl = `data:image/png;base64,${result.qr_code_base64}`;
        } else if (result && result.file_path) {
          // Handle file_path if necessary, e.g., if Wails AssetServer serves it
          console.log('报到二维码文件路径:', result.file_path);
          // this.registrationQRCodeUrl = result.file_path; // This might require specific Wails setup
          this.showNotification('报到二维码已生成 (路径方式，可能无法直接显示)', 'info');
        } else {
          console.error('生成报到二维码失败或返回数据格式不正确', result);
          this.showNotification('生成报到二维码失败', 'error');
        }
      } catch (error) {
        console.error('调用生成报到二维码方法失败:', error);
        this.showNotification(`生成报到二维码失败: ${error.message || error}`, 'error');
      }
    },
    
    async updateCropSettings(cropSettings) {
      try {
        await UpdateCropSettings(cropSettings)
        this.config = await GetConfig()
        
        // 处理device_no字段，从mac_address生成去除冒号的版本
        if (this.config.device_info && this.config.device_info.mac_address) {
          this.config.device_info.device_no = this.config.device_info.mac_address.replace(/:/g, '')
        }
        
        this.showNotification('裁剪设置更新成功', 'success')
      } catch (error) {
        this.showNotification(`更新裁剪设置失败: ${error}`, 'error')
      }
    },
    
    async handleScreenshot(mode, userName) {
      try {
        this.showLoading(`正在处理和分析[${mode}]数据...`)
        
        const imageUrl = await ProcessScreenshotAndUpload(mode, userName)
        
        this.hideLoading()
        this.showNotification(`数据处理完成: ${imageUrl}`, 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '数据处理',
          user: userName,
          mode: mode,
          result: '成功',
          url: imageUrl
        })
      } catch (error) {
        this.hideLoading()
        this.showNotification(`数据处理失败: ${error}`, 'error')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '截图处理',
          user: userName,
          mode: mode,
          result: '失败',
          error: error.toString()
        })
      }
    },
    
    // async uploadLatestScreenshot() {
    //   // 已注释：功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
    //   try {
    //     this.showLoading('正在上传最新截图...')
        
    //     // 调用后端方法上传最新截图
    //     const result = await UploadLatestScreenshot()
        
    //     this.hideLoading()
    //     this.showNotification(`上传完成: ${result}`, 'success')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '成功',
    //       url: result
    //     })
    //   } catch (error) {
    //     this.hideLoading()
    //     this.showNotification(`上传失败: ${error}`, 'error')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '失败',
    //       error: error.toString()
    //     })
    //   }
    // },
    
    async generateQRCode() {
      try {
        this.showLoading('正在生成二维码...')
        
        const qrCodePath = await GenerateQRCode()
        this.latestQRCode = qrCodePath // 保存最新的二维码
        
        this.hideLoading()
        this.showNotification('二维码生成成功', 'success')
        
        return qrCodePath
      } catch (error) {
        this.hideLoading()
        this.showNotification(`生成二维码失败: ${error}`, 'error')
        throw error
      }
    },

    // 窗体控制方法
    async toggleWindowSize() {
      try {
        await ToggleWindowSize()
        const state = await GetWindowState()
        this.windowState = state
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体大小失败: ${error}`, 'error')
      }
    },

    async toggleWindowPosition() {
      try {
        const newPosition = this.windowState.position === 'left' ? 'right' : 'left'
        await SetWindowPosition(newPosition)
        this.windowState.position = newPosition
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体位置失败: ${error}`, 'error')
      }
    },

    async minimizeWindow() {
      try {
        await MinimizeWindow()
        this.updateActivity()
      } catch (error) {
        this.showNotification(`最小化窗体失败: ${error}`, 'error')
      }
    },

    async handleAddPatient() {
      if (!this.newPatient.name || !this.newPatient.registrationNumber) {
        this.showNotification('请填写完整的患者信息', 'warning')
        return
      }
      
      try {
        await this.addPatient(this.newPatient.name)
        this.newPatient = { name: '', registrationNumber: '' }
        this.showAddPatientDialog = false
        this.updateActivity()
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },

    // 活动跟踪
    updateActivity() {
      this.lastActivityTime = Date.now()
      this.resetInactivityTimer()
    },

    resetInactivityTimer() {
      if (this.inactivityTimer) {
        clearTimeout(this.inactivityTimer)
      }
      
      // 5分钟无操作自动收缩
      this.inactivityTimer = setTimeout(() => {
        if (this.windowState.isExpanded) {
          this.toggleWindowSize()
          this.showNotification('长时间无操作，窗体已自动收缩', 'info')
        }
      }, 5 * 60 * 1000)
    },

    // 初始化窗体状态
    async initializeWindowState() {
      try {
        const state = await GetWindowState()
        this.windowState = state
      } catch (error) {
        console.error('获取窗体状态失败:', error)
      }
    },
    
    async addPatient(name) {
      try {
        await AddPatient(name)
        this.patients = await GetPatientList()
        this.showNotification(`患者 ${name} 添加成功`, 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '添加患者',
          user: name,
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },
    
    async removePatient(index) {
      try {
        const patient = this.patients[index]
        await RemovePatient(index)
        this.patients = await GetPatientList()
        this.showNotification(`患者 ${patient.Name} 移除成功`, 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '移除患者',
          user: patient.Name,
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`移除患者失败: ${error}`, 'error')
      }
    },
    
    async clearPatients() {
      try {
        await ClearPatientList()
        this.patients = []
        this.showNotification('患者列表已清空', 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '清空患者列表',
          user: '系统',
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`清空患者列表失败: ${error}`, 'error')
      }
    },
    
    handleProcessComplete(result) {
      this.showNotification(`处理完成: ${result}`, 'success')
    },
    
    addLog(log) {
      this.logs.unshift(log)
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    
    showLoading(message) {
      this.loading = true
      this.loadingMessage = message
    },
    
    hideLoading() {
      this.loading = false
      this.loadingMessage = ''
    },
    
    showNotification(message, type = 'info') {
      this.notification = {
        show: true,
        type,
        message
      },
      
      setTimeout(() => {
        this.notification.show = false
      }, 3000)
    },
    
    startTimeUpdate() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    setupKeyboardShortcuts() {
      // 监听键盘事件
      document.addEventListener('keydown', (event) => {
        // F9-F12 快捷键处理（保留窗口控制功能）
        if (['F9', 'F10', 'F11', 'F12'].includes(event.key)) {
          event.preventDefault()
          this.handleFunctionKey(event.key)
        }
        
        // Ctrl+Shift 组合键处理
        if (event.ctrlKey && event.shiftKey) {
          event.preventDefault()
          this.handleHotkeyCombo(event.key.toUpperCase())
        }
      })
    },
    
    async handleFunctionKey(key) {
      const keyMap = {
        'F9': () => this.minimizeWindow(),
        'F10': () => this.toggleWindowPosition(),
        'F11': () => this.toggleWindowSize(),
        'F12': () => this.clearPatients()
      };
      
      if (keyMap[key]) {
        await keyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(key)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },
    
    async handleHotkeyCombo(key) {
      const hotkeyMap = {
        'A': () => this.handleScreenshot('器官问题来源分析', '医生或健康专家'),
        'B': () => this.handleScreenshot('生化平衡分析', '医生或健康专家'),
        'C': () => this.handleScreenshot('病理形态学分析', '医生或健康专家'),
        // 'U': () => this.uploadLatestScreenshot() // 已注释，功能由A、B、C快捷键替代
      };
      
      if (hotkeyMap[key]) {
        await hotkeyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(`Ctrl+Shift+${key}`)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },

    // 配置监听器方法
    startConfigWatcher() {
      // console.log('配置监听器已移除定时获取逻辑，仅在初始化时获取配置')
      // // 每30秒检查一次配置更新
      // this.configWatcherTimer = setInterval(async () => {
      //   try {
      //     const newConfig = await GetConfig()
      //     // 检查站点信息是否有变化
      //     if (this.hasConfigChanged(newConfig)) {
      //       const oldSiteInfo = this.config?.SiteInfo || {}
      //       this.config = newConfig
      //       this.showNotification(`站点信息已更新: ${newConfig.SiteInfo?.SiteName || '未知站点'}`, 'success')
      //       console.log('配置已更新:', {
      //         旧站点: oldSiteInfo.SiteName || '未设置',
      //         新站点: newConfig.SiteInfo?.SiteName || '未设置'
      //       })
      //     }
      //   } catch (error) {
      //     console.error('检查配置更新失败:', error)
      //   }
      // }, 30000) // 30秒检查一次
    },

    // 检查配置是否有变化
    hasConfigChanged(newConfig) {
      if (!this.config || !newConfig) return true
      
      const oldSiteInfo = this.config.SiteInfo || {}
      const newSiteInfo = newConfig.SiteInfo || {}
      
      return (
        oldSiteInfo.SiteID !== newSiteInfo.SiteID ||
        oldSiteInfo.SiteName !== newSiteInfo.SiteName ||
        oldSiteInfo.SiteType !== newSiteInfo.SiteType ||
        oldSiteInfo.ParentOrg !== newSiteInfo.ParentOrg
      )
    },

    // 候检者相关方法
    async loadRegistrations() {
      try {
        this.registrations = [] // 获取数据前先清空
        const newRegistrations = await GetRegistrations()
        this.registrations = newRegistrations || [] // 如果API返回null或undefined，则设置为空数组
        // 按报到时间倒序排列（最新的在前面）
        this.registrations.sort((a, b) => new Date(b.register_time) - new Date(a.register_time))
        // 重置显示限制
        this.displayLimit = 5
        // 默认选中第一个候检者
        if (this.registrations.length > 0) {
          this.selectedPatientIndex = 0
        } else {
          this.selectedPatientIndex = -1
        }
      } catch (error) {
        console.error('加载候检者列表失败:', error)
        this.showNotification('加载候检者列表失败: ' + error, 'error')
        this.registrations = []
        this.displayLimit = 5
        this.selectedPatientIndex = -1
      }
    },

    async refreshRegistrations() {
      if (this.isRefreshing) return
      
      this.isRefreshing = true
      try {
        await this.loadRegistrations()
        this.showNotification('候检者列表已刷新', 'success')
      } catch (error) {
        this.showNotification('刷新失败: ' + error, 'error')
      } finally {
        this.isRefreshing = false
      }
    },

    // 选择候检者
    selectPatient(index) {
      this.selectedPatientIndex = index
      this.showNotification(`已选择候检者: ${this.currentPatient.name}`, 'info')
    },

    // 条件性轮询 - 在特定条件下尝试获取新的候检者信息
    async startConditionalPolling() {
      // 清除之前的定时器
      if (this.pollTimer) {
        clearTimeout(this.pollTimer)
      }

      let attempts = 0
      const maxAttempts = 2
      const pollInterval = 2000 // 2秒

      const poll = async () => {
        attempts++
        const previousCount = this.registrations.length
        
        try {
          await this.loadRegistrations()
          
          // 如果有新的候检者，停止轮询
          if (this.registrations.length > previousCount) {
            this.showNotification(`发现 ${this.registrations.length - previousCount} 位新候检者`, 'success')
            return
          }
          
          // 如果还没达到最大尝试次数，继续轮询
          if (attempts < maxAttempts) {
            this.pollTimer = setTimeout(poll, pollInterval)
          }
        } catch (error) {
          console.error('轮询候检者列表失败:', error)
          // 即使失败也要继续尝试，直到达到最大次数
          if (attempts < maxAttempts) {
            this.pollTimer = setTimeout(poll, pollInterval)
          }
        }
      }

      // 延迟1秒后开始第一次轮询
      this.pollTimer = setTimeout(poll, 1000)
    },

    loadMoreRegistrations() {
      this.displayLimit = Math.min(this.displayLimit + 5, this.registrations.length)
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      // 后端已经格式化为 "2006-01-02 15:04:05" 格式，直接显示月日时分
      try {
        if (timeStr.includes('-') && timeStr.includes(':')) {
          // 解析 "2006-01-02 15:04:05" 格式
          const parts = timeStr.split(' ')
          if (parts.length === 2) {
            const datePart = parts[0].split('-')
            const timePart = parts[1].split(':')
            if (datePart.length === 3 && timePart.length >= 2) {
              return `${datePart[1]}/${datePart[2]} ${timePart[0]}:${timePart[1]}`
            }
          }
        }
        return timeStr
      } catch (error) {
        return timeStr
      }
    },

    async generateRegistrationQRCode() {
      try {
        const result = await GenerateRegistrationQRCode(); // 调用后端方法
        if (result && result.qr_code_base64) {
          this.registrationQRCodeUrl = `data:image/png;base64,${result.qr_code_base64}`;
        } else if (result && result.file_path) {
          // Handle file_path if necessary, e.g., if Wails AssetServer serves it
          console.log('报到二维码文件路径:', result.file_path);
          // this.registrationQRCodeUrl = result.file_path; // This might require specific Wails setup
          this.showNotification('报到二维码已生成 (路径方式，可能无法直接显示)', 'info');
        } else {
          console.error('生成报到二维码失败或返回数据格式不正确', result);
          this.showNotification('生成报到二维码失败', 'error');
        }
      } catch (error) {
        console.error('调用生成报到二维码方法失败:', error);
        this.showNotification(`生成报到二维码失败: ${error.message || error}`, 'error');
      }
    },
    
    async updateCropSettings(cropSettings) {
      try {
        await UpdateCropSettings(cropSettings)
        this.config = await GetConfig()
        
        // 处理device_no字段，从mac_address生成去除冒号的版本
        if (this.config.device_info && this.config.device_info.mac_address) {
          this.config.device_info.device_no = this.config.device_info.mac_address.replace(/:/g, '')
        }
        
        this.showNotification('裁剪设置更新成功', 'success')
      } catch (error) {
        this.showNotification(`更新裁剪设置失败: ${error}`, 'error')
      }
    },

    // async uploadLatestScreenshot() {
    //   // 已注释：功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
    //   try {
    //     this.showLoading('正在上传最新截图...')
        
    //     // 调用后端方法上传最新截图
    //     const result = await UploadLatestScreenshot()
        
    //     this.hideLoading()
    //     this.showNotification(`上传完成: ${result}`, 'success')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '成功',
    //       url: result
    //     })
    //   } catch (error) {
    //     this.hideLoading()
    //     this.showNotification(`上传失败: ${error}`, 'error')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '失败',
    //       error: error.toString()
    //     })
    //   }
    // },
    
    async generateQRCode() {
      try {
        this.showLoading('正在生成二维码...')
        
        const qrCodePath = await GenerateQRCode()
        this.latestQRCode = qrCodePath // 保存最新的二维码
        
        this.hideLoading()
        this.showNotification('二维码生成成功', 'success')
        
        return qrCodePath
      } catch (error) {
        this.hideLoading()
        this.showNotification(`生成二维码失败: ${error}`, 'error')
        throw error
      }
    },

    // 窗体控制方法
    async toggleWindowSize() {
      try {
        await ToggleWindowSize()
        const state = await GetWindowState()
        this.windowState = state
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体大小失败: ${error}`, 'error')
      }
    },

    async toggleWindowPosition() {
      try {
        const newPosition = this.windowState.position === 'left' ? 'right' : 'left'
        await SetWindowPosition(newPosition)
        this.windowState.position = newPosition
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体位置失败: ${error}`, 'error')
      }
    },

    async minimizeWindow() {
      try {
        await MinimizeWindow()
        this.updateActivity()
      } catch (error) {
        this.showNotification(`最小化窗体失败: ${error}`, 'error')
      }
    },

    async handleQuickScreenshot() {
      try {
        // 使用默认模式进行快速截图
        await this.handleScreenshot('default', '快速操作')
        this.updateActivity()
      } catch (error) {
        this.showNotification(`快速截图失败: ${error}`, 'error')
      }
    },

    async handleAddPatient() {
      if (!this.newPatient.name || !this.newPatient.registrationNumber) {
        this.showNotification('请填写完整的患者信息', 'warning')
        return
      }
      
      try {
        await this.addPatient(this.newPatient.name)
        this.newPatient = { name: '', registrationNumber: '' }
        this.showAddPatientDialog = false
        this.updateActivity()
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },

    // 活动跟踪
    updateActivity() {
      this.lastActivityTime = Date.now()
      this.resetInactivityTimer()
    },

    resetInactivityTimer() {
      if (this.inactivityTimer) {
        clearTimeout(this.inactivityTimer)
      }
      
      // 5分钟无操作自动收缩
      this.inactivityTimer = setTimeout(() => {
        if (this.windowState.isExpanded) {
          this.toggleWindowSize()
          this.showNotification('长时间无操作，窗体已自动收缩', 'info')
        }
      }, 5 * 60 * 1000)
    },

    // 初始化窗体状态
    async initializeWindowState() {
      try {
        const state = await GetWindowState()
        this.windowState = state
      } catch (error) {
        console.error('获取窗体状态失败:', error)
      }
    },
    
    async addPatient(name) {
      try {
        await AddPatient(name)
        this.patients = await GetPatientList()
        this.showNotification(`患者 ${name} 添加成功`, 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '添加患者',
          user: name,
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },
    
    async removePatient(index) {
      try {
        const patient = this.patients[index]
        await RemovePatient(index)
        this.patients = await GetPatientList()
        this.showNotification(`患者 ${patient.Name} 移除成功`, 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '移除患者',
          user: patient.Name,
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`移除患者失败: ${error}`, 'error')
      }
    },
    
    async clearPatients() {
      try {
        await ClearPatientList()
        this.patients = []
        this.showNotification('患者列表已清空', 'success')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '清空患者列表',
          user: '系统',
          result: '成功'
        })
      } catch (error) {
        this.showNotification(`清空患者列表失败: ${error}`, 'error')
      }
    },
    
    handleProcessComplete(result) {
      this.showNotification(`处理完成: ${result}`, 'success')
    },
    
    addLog(log) {
      this.logs.unshift(log)
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    
    showLoading(message) {
      this.loading = true
      this.loadingMessage = message
    },
    
    hideLoading() {
      this.loading = false
      this.loadingMessage = ''
    },
    
    showNotification(message, type = 'info') {
      this.notification = {
        show: true,
        type,
        message
      },
      
      setTimeout(() => {
        this.notification.show = false
      }, 3000)
    },
    
    startTimeUpdate() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    setupKeyboardShortcuts() {
      // 监听键盘事件
      document.addEventListener('keydown', (event) => {
        // F9-F12 快捷键处理（保留窗口控制功能）
        if (['F9', 'F10', 'F11', 'F12'].includes(event.key)) {
          event.preventDefault()
          this.handleFunctionKey(event.key)
        }
        
        // Ctrl+Shift 组合键处理
        if (event.ctrlKey && event.shiftKey) {
          event.preventDefault()
          this.handleHotkeyCombo(event.key.toUpperCase())
        }
      })
    },
    
    async handleFunctionKey(key) {
      const keyMap = {
        'F9': () => this.minimizeWindow(),
        'F10': () => this.toggleWindowPosition(),
        'F11': () => this.toggleWindowSize(),
        'F12': () => this.clearPatients()
      };
      
      if (keyMap[key]) {
        await keyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(key)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },
    
    async handleHotkeyCombo(key) {
      const hotkeyMap = {
        'A': () => this.handleScreenshot('器官问题来源分析', '医生或健康专家'),
        'B': () => this.handleScreenshot('生化平衡分析', '医生或健康专家'),
        'C': () => this.handleScreenshot('病理形态学分析', '医生或健康专家'),
        // 'U': () => this.uploadLatestScreenshot() // 已注释，功能由A、B、C快捷键替代
      };
      
      if (hotkeyMap[key]) {
        await hotkeyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(`Ctrl+Shift+${key}`)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },

    // 配置监听器方法
    startConfigWatcher() {
      // console.log('配置监听器已移除定时获取逻辑，仅在初始化时获取配置')
      // // 每30秒检查一次配置更新
      // this.configWatcherTimer = setInterval(async () => {
      //   try {
      //     const newConfig = await GetConfig()
      //     // 检查站点信息是否有变化
      //     if (this.hasConfigChanged(newConfig)) {
      //       const oldSiteInfo = this.config?.SiteInfo || {}
      //       this.config = newConfig
      //       this.showNotification(`站点信息已更新: ${newConfig.SiteInfo?.SiteName || '未知站点'}`, 'success')
      //       console.log('配置已更新:', {
      //         旧站点: oldSiteInfo.SiteName || '未设置',
      //         新站点: newConfig.SiteInfo?.SiteName || '未设置'
      //       })
      //     }
      //   } catch (error) {
      //     console.error('检查配置更新失败:', error)
      //   }
      // }, 30000) // 30秒检查一次
    },

    // 检查配置是否有变化
    hasConfigChanged(newConfig) {
      if (!this.config || !newConfig) return true
      
      const oldSiteInfo = this.config.SiteInfo || {}
      const newSiteInfo = newConfig.SiteInfo || {}
      
      return (
        oldSiteInfo.SiteID !== newSiteInfo.SiteID ||
        oldSiteInfo.SiteName !== newSiteInfo.SiteName ||
        oldSiteInfo.SiteType !== newSiteInfo.SiteType ||
        oldSiteInfo.ParentOrg !== newSiteInfo.ParentOrg
      )
    },

    // 候检者相关方法
    async loadRegistrations() {
      try {
        this.registrations = [] // 获取数据前先清空
        const newRegistrations = await GetRegistrations()
        this.registrations = newRegistrations || [] // 如果API返回null或undefined，则设置为空数组
        // 按报到时间倒序排列（最新的在前面）
        this.registrations.sort((a, b) => new Date(b.register_time) - new Date(a.register_time))
        // 重置显示限制
        this.displayLimit = 5
        // 默认选中第一个候检者
        if (this.registrations.length > 0) {
          this.selectedPatientIndex = 0
        } else {
          this.selectedPatientIndex = -1
        }
      } catch (error) {
        console.error('加载候检者列表失败:', error)
        this.showNotification('加载候检者列表失败: ' + error, 'error')
        this.registrations = []
        this.displayLimit = 5
        this.selectedPatientIndex = -1
      }
    },

    async refreshRegistrations() {
      if (this.isRefreshing) return
      
      this.isRefreshing = true
      try {
        await this.loadRegistrations()
        this.showNotification('候检者列表已刷新', 'success')
      } catch (error) {
        this.showNotification('刷新失败: ' + error, 'error')
      } finally {
        this.isRefreshing = false
      }
    },

    // 选择候检者
    selectPatient(index) {
      this.selectedPatientIndex = index
      this.showNotification(`已选择候检者: ${this.currentPatient.name}`, 'info')
    },

    // 条件性轮询 - 在特定条件下尝试获取新的候检者信息
    async startConditionalPolling() {
      // 清除之前的定时器
      if (this.pollTimer) {
        clearTimeout(this.pollTimer)
      }

      let attempts = 0
      const maxAttempts = 2
      const pollInterval = 2000 // 2秒

      const poll = async () => {
        attempts++
        const previousCount = this.registrations.length
        
        try {
          await this.loadRegistrations()
          
          // 如果有新的候检者，停止轮询
          if (this.registrations.length > previousCount) {
            this.showNotification(`发现 ${this.registrations.length - previousCount} 位新候检者`, 'success')
            return
          }
          
          // 如果还没达到最大尝试次数，继续轮询
          if (attempts < maxAttempts) {
            this.pollTimer = setTimeout(poll, pollInterval)
          }
        } catch (error) {
          console.error('轮询候检者列表失败:', error)
          // 即使失败也要继续尝试，直到达到最大次数
          if (attempts < maxAttempts) {
            this.pollTimer = setTimeout(poll, pollInterval)
          }
        }
      }

      // 延迟1秒后开始第一次轮询
      this.pollTimer = setTimeout(poll, 1000)
    },

    loadMoreRegistrations() {
      this.displayLimit = Math.min(this.displayLimit + 5, this.registrations.length)
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      // 后端已经格式化为 "2006-01-02 15:04:05" 格式，直接显示月日时分
      try {
        if (timeStr.includes('-') && timeStr.includes(':')) {
          // 解析 "2006-01-02 15:04:05" 格式
          const parts = timeStr.split(' ')
          if (parts.length === 2) {
            const datePart = parts[0].split('-')
            const timePart = parts[1].split(':')
            if (datePart.length === 3 && timePart.length >= 2) {
              return `${datePart[1]}/${datePart[2]} ${timePart[0]}:${timePart[1]}`
            }
          }
        }
        return timeStr
      } catch (error) {
        return timeStr
      }
    }
  }
}

</script>

<style>
/* 基础样式 */
#app {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #f5f7fa;
  color: #2c3e50;
  transition: all 0.3s ease;
}

/* 紧凑模式样式 */
#app.compact {
  width: 340px;
  background: linear-gradient(145deg, #ffffff, #f0f2f5);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 展开模式样式 */
#app.expanded {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0;
}

/* 紧凑模式头部 */
.compact-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.app-title {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-controls button {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.window-controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 展开模式下的窗体控制按钮样式 */
.expanded-content .window-controls button {
  background: rgba(52, 73, 94, 0.1);
  color: #2c3e50;
  border: 1px solid rgba(52, 73, 94, 0.2);
}

.expanded-content .window-controls button:hover {
  background: rgba(52, 73, 94, 0.2);
  color: #1a252f;
  border-color: rgba(52, 73, 94, 0.4);
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(52, 73, 94, 0.3);
}

/* 特殊按钮颜色 */
.expanded-content .collapse-btn {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  border-color: rgba(255, 152, 0, 0.3);
}

.expanded-content .collapse-btn:hover {
  background: rgba(255, 152, 0, 0.2);
  color: #f57c00;
  border-color: rgba(255, 152, 0, 0.5);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.4);
}

.expanded-content .minimize-btn {
  background: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
  border-color: rgba(156, 39, 176, 0.3);
}

.expanded-content .minimize-btn:hover {
  background: rgba(156, 39, 176, 0.2);
  color: #7b1fa2;
  border-color: rgba(156, 39, 176, 0.5);
  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.4);
}

.compact-status {
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  color: #2c3e50;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #2c3e50;
}

.status-item .label {
  opacity: 0.8;
  font-weight: 500;
  color: #34495e;
}

.status-item .value {
  font-weight: 600;
  background: rgba(52, 73, 94, 0.1);
  color: #2c3e50;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  border: 1px solid rgba(52, 73, 94, 0.2);
}

/* 紧凑模式内容 */
.compact-content {
  padding: 12px; /* 调整为统一的12px边距，确保左右边距一致 */
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

/* 快速操作按钮 */
.quick-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(145deg, #e3f2fd, #bbdefb);
  color: #1976d2;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-weight: 500;
}

.action-btn.primary {
  background: linear-gradient(145deg, #4caf50, #45a049);
  color: white;
}

.action-btn.expand-main {
  background: linear-gradient(145deg, #ff9800, #f57c00);
  color: white;
  border: 2px solid #ff6f00;
  font-weight: 600;
  position: relative;
}

.action-btn.expand-main::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff9800, #f57c00, #ff9800);
  border-radius: 10px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn.expand-main:hover::before {
  opacity: 0.3;
}

.action-btn.expand-main:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: translateY(0);
}

.quick-actions button.secondary {
  background-color: #e0e0e0;
  color: #333;
}

.registration-qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px; /* 与下方按钮的间距 */
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  min-height: 150px; /* 至少高度，避免加载时跳动 */
}

.registration-qrcode-image {
  max-width: 100%;
  max-height: 200px; /* 限制二维码图片的最大高度 */
  border-radius: 4px;
}

/* 当前候检者卡片 */
.current-patient-section {
  background: white;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-patient-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  padding: 12px;
  color: white;
  margin-top: 8px;
}

.current-patient-card.empty {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px dashed #dee2e6;
}

.current-patient-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-patient-name {
  font-weight: 600;
  font-size: 14px;
}

.current-patient-number {
  font-size: 12px;
  opacity: 0.9;
}

.empty-text {
  font-size: 12px;
  text-align: center;
  width: 100%;
}

/* 患者列表紧凑版 */
.compact-patients {
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 候检者列表容器 */
.patient-section {
  background: white;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 10px;
  margin-right: 0px; /* 移除额外的右边距，让容器内容区域的padding来控制边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #555;
}

.refresh-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #2196f3;
  color: white;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #1976d2;
  transform: scale(1.1) rotate(180deg);
}

.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.patient-list {
  max-height: 200px;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  margin-right: 4px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 11px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.patient-item:hover {
  background: #e9ecef;
  transform: translateX(2px);
}

.patient-item.selected {
  background: #e3f2fd;
  border: 2px solid #2196f3;
  transform: translateX(2px);
}

.patient-radio {
  margin-right: 8px;
  cursor: pointer;
}

.patient-name {
  font-weight: 600;
  color: #2c3e50;
}

.patient-number {
  color: #2c3e50;
  font-size: 12px;
  font-weight: 600;
}

.patient-time {
  color: #6c757d;
  font-size: 10px;
}

.remove-btn {
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background: #dc3545;
  color: white;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.more-patients {
  text-align: center;
  color: #2196f3;
  font-size: 10px;
  padding: 6px 8px;
  cursor: pointer;
  background: #f0f8ff;
  border-radius: 4px;
  margin-top: 4px;
  transition: all 0.2s ease;
}

.more-patients:hover {
  background: #e3f2fd;
  color: #1976d2;
  transform: translateY(-1px);
}

/* 二维码显示紧凑版 */
.compact-qrcode {
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.qrcode-display {
  margin-top: 8px;
}

.qrcode-display img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

/* 展开模式样式 */
.expanded-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.expanded-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.expanded-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #555;
}

.expanded-main {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.expanded-footer {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.stats {
  display: flex;
  gap: 30px;
  font-size: 14px;
  color: #555;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.dialog input {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
}

.dialog input:focus {
  outline: none;
  border-color: #4facfe;
  box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
}

.dialog-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
}

.dialog-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dialog-buttons button[type="button"] {
  background: #6c757d;
  color: white;
}

.dialog-buttons button[type="submit"] {
  background: #4caf50;
  color: white;
}

.dialog-buttons button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.status-bar {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

.left-panel,
.center-panel,
.right-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.left-panel {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-panel {
  flex: 0 0 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.footer {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.stats {
  display: flex;
  gap: 30px;
  font-size: 14px;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
  max-width: 400px;
  word-wrap: break-word;
}

.notification.success {
  background: #27ae60;
}

.notification.error {
  background: #e74c3c;
}

.notification.info {
  background: #3498db;
}

.notification.warning {
  background: #f39c12;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 基础样式 */
#app {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #f5f7fa;
  color: #2c3e50;
  transition: all 0.3s ease;
}

/* 紧凑模式样式 */
#app.compact {
  width: 320px;
  background: linear-gradient(145deg, #ffffff, #f0f2f5);
  border-radius: 12px;
}
</style>
