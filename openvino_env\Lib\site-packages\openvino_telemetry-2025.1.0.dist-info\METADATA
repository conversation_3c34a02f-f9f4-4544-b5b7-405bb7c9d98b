Metadata-Version: 2.1
Name: openvino-telemetry
Version: 2025.1.0
Summary: OpenVINO™ Telemetry package for sending statistics with user's consent, used in combination with other OpenVINO™ packages.
Home-page: https://github.com/openvinotoolkit/telemetry
Author: Intel® Corporation
Author-email: <EMAIL>
License: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Description-Content-Type: text/markdown
License-File: LICENSE

#  OpenVINO™ Telemetry

The implementation of the Python 3 library to send the telemetry data from the OpenVINO™ toolkit components.

To send the data to Google Analytics, use the following three variables: `category`, `action`, and `label`.

- In the `category`, use only the name of the tool. Place all Model Optimizer (MO) topics in the 'mo' category, all Post-Training Optimization Tool (POT) topics in the 'pot' category. 
- In the `action`, send a metric or a function, such as accuracy, session, conversion, and others. For example, for MO use: version, framework, conversion_results.
- In the `label`, send more detailed data for your action (function). For example, send a string with the version name for the version or a string with the framework name for the framework. You can send a string with a wrapped dictionary: "{param: value, version: value, error: value}".

**NOTE:** If you want to track the connection between data (for example, on which operating systems error '123' occurred), send the data in one event and add it to the label together. Example: telemetry.send_event("mo", "error_info", "{os:ubuntu18, error:123}"). If you send the data separately, you will not be able to identify the connection. Some data will be duplicated since the same metric/function will be sent in different events.

**NOTE:** Sending of telemetry data requires user's consent during installation of OpenVINO™ toolkit component. In case if control file does not exist on the system or it contains a "no" answer, no data will be transmitted. 

**TIP:**  To help automate the analytics, always send **all** the keys for a dictionary in the `label` variable. If a key is empty, send 'none' as its value. 
