package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// 更好的OCR解决方案
func demonstrateBetterOCR(imagePath string) {
	fmt.Println("=== 更好的OCR解决方案对比 ===")

	// 方案1: PaddleOCR (推荐)
	fmt.Println("\n1. PaddleOCR (百度开源，中文效果最佳)")
	tryPaddleOCR(imagePath)

	// 方案2: EasyOCR
	fmt.Println("\n2. EasyOCR (支持80+语言)")
	tryEasyOCR(imagePath)

	// 方案3: 云服务API
	fmt.Println("\n3. 云服务OCR API")
	demonstrateCloudOCR()

	// 方案4: TrOCR (Transformer OCR)
	fmt.Println("\n4. TrOCR (微软Transformer OCR)")
	tryTrOCR(imagePath)
}

// 尝试PaddleOCR
func tryPaddleOCR(imagePath string) {
	fmt.Println("PaddleOCR特点:")
	fmt.Println("✅ 中文识别准确率极高 (95%+)")
	fmt.Println("✅ 支持文本检测+识别")
	fmt.Println("✅ 模型小，速度快")
	fmt.Println("✅ 免费开源")

	// 检查是否安装了PaddleOCR
	if checkPaddleOCR() {
		fmt.Println("✅ 检测到PaddleOCR环境")
		result := runPaddleOCR(imagePath)
		fmt.Printf("识别结果: %s\n", result)
	} else {
		fmt.Println("❌ 未检测到PaddleOCR，请安装:")
		fmt.Println("   pip install paddlepaddle paddleocr")
	}
}

// 检查PaddleOCR是否可用
func checkPaddleOCR() bool {
	cmd := exec.Command("python", "-c", "import paddleocr; print('OK')")
	err := cmd.Run()
	return err == nil
}

// 运行PaddleOCR
func runPaddleOCR(imagePath string) string {
	// 使用Python脚本调用PaddleOCR
	pythonScript := `
import sys
from paddleocr import PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='ch')
result = ocr.ocr(sys.argv[1], cls=True)
for idx in range(len(result)):
    res = result[idx]
    if res:
        for line in res:
            print(line[1][0])
`

	// 创建临时Python文件
	tmpFile := "temp_paddle_ocr.py"
	err := os.WriteFile(tmpFile, []byte(pythonScript), 0644)
	if err != nil {
		return "创建临时文件失败"
	}
	defer os.Remove(tmpFile)

	// 执行Python脚本
	cmd := exec.Command("python", tmpFile, imagePath)
	output, err := cmd.Output()
	if err != nil {
		return fmt.Sprintf("PaddleOCR执行失败: %v", err)
	}

	return strings.TrimSpace(string(output))
}

// 尝试EasyOCR
func tryEasyOCR(imagePath string) {
	fmt.Println("EasyOCR特点:")
	fmt.Println("✅ 支持80+种语言")
	fmt.Println("✅ 安装简单")
	fmt.Println("✅ 中文支持良好")
	fmt.Println("❌ 模型较大")

	if checkEasyOCR() {
		fmt.Println("✅ 检测到EasyOCR环境")
		result := runEasyOCR(imagePath)
		fmt.Printf("识别结果: %s\n", result)
	} else {
		fmt.Println("❌ 未检测到EasyOCR，请安装:")
		fmt.Println("   pip install easyocr")
	}
}

// 检查EasyOCR是否可用
func checkEasyOCR() bool {
	cmd := exec.Command("python", "-c", "import easyocr; print('OK')")
	err := cmd.Run()
	return err == nil
}

// 运行EasyOCR
func runEasyOCR(imagePath string) string {
	pythonScript := `
import sys
import easyocr
reader = easyocr.Reader(['ch_sim', 'en'])
result = reader.readtext(sys.argv[1])
for (bbox, text, conf) in result:
    if conf > 0.5:  # 置信度阈值
        print(text)
`

	tmpFile := "temp_easy_ocr.py"
	err := os.WriteFile(tmpFile, []byte(pythonScript), 0644)
	if err != nil {
		return "创建临时文件失败"
	}
	defer os.Remove(tmpFile)

	cmd := exec.Command("python", tmpFile, imagePath)
	output, err := cmd.Output()
	if err != nil {
		return fmt.Sprintf("EasyOCR执行失败: %v", err)
	}

	return strings.TrimSpace(string(output))
}

// 演示云服务OCR
func demonstrateCloudOCR() {
	fmt.Println("云服务OCR选项:")
	fmt.Println("1. 百度OCR API")
	fmt.Println("   ✅ 中文识别效果极佳")
	fmt.Println("   ✅ 专业医疗OCR")
	fmt.Println("   ❌ 需要网络连接")
	fmt.Println("   ❌ 按次收费")

	fmt.Println("2. 腾讯云OCR")
	fmt.Println("   ✅ 识别准确率高")
	fmt.Println("   ✅ 支持表格识别")
	fmt.Println("   ❌ 需要网络连接")

	fmt.Println("3. 阿里云OCR")
	fmt.Println("   ✅ 企业级稳定性")
	fmt.Println("   ✅ 多种专业场景")
	fmt.Println("   ❌ 需要网络连接")

	// 示例：百度OCR API调用
	fmt.Println("\n百度OCR API示例:")
	showBaiduOCRExample()
}

// 百度OCR API示例
func showBaiduOCRExample() {
	fmt.Println(`
// 百度OCR API使用示例
func callBaiduOCR(imagePath string) (string, error) {
    // 1. 获取access_token
    token, err := getBaiduAccessToken()
    if err != nil {
        return "", err
    }
    
    // 2. 读取图片并编码
    imageData, err := os.ReadFile(imagePath)
    if err != nil {
        return "", err
    }
    imageBase64 := base64.StdEncoding.EncodeToString(imageData)
    
    // 3. 调用OCR API
    url := "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic"
    data := map[string]string{
        "image": imageBase64,
    }
    
    // 发送请求...
    return result, nil
}`)
}

// 尝试TrOCR
func tryTrOCR(imagePath string) {
	fmt.Println("TrOCR (Transformer OCR)特点:")
	fmt.Println("✅ 基于Transformer架构")
	fmt.Println("✅ 微软开源")
	fmt.Println("✅ 支持手写文字")
	fmt.Println("❌ 需要GPU加速")
	fmt.Println("❌ 模型较大")

	fmt.Println("安装方法:")
	fmt.Println("pip install transformers torch torchvision")
}

// 实际可用的OCR方案推荐
func recommendPracticalSolution() {
	fmt.Println("\n=== 实际推荐方案 ===")

	fmt.Println("🥇 最佳方案: PaddleOCR")
	fmt.Println("   - 中文识别准确率最高")
	fmt.Println("   - 免费开源")
	fmt.Println("   - 安装简单: pip install paddleocr")
	fmt.Println("   - 可以通过Python脚本调用")

	fmt.Println("🥈 备选方案: EasyOCR")
	fmt.Println("   - 多语言支持")
	fmt.Println("   - 安装简单")
	fmt.Println("   - 中文效果良好")

	fmt.Println("🥉 企业方案: 百度OCR API")
	fmt.Println("   - 识别准确率极高")
	fmt.Println("   - 专业医疗场景支持")
	fmt.Println("   - 需要网络和付费")

	fmt.Println("\n立即可行的实施步骤:")
	fmt.Println("1. 安装PaddleOCR: pip install paddleocr")
	fmt.Println("2. 创建Python OCR脚本")
	fmt.Println("3. Go程序调用Python脚本")
	fmt.Println("4. 集成OCR校正机制")
}

// 创建PaddleOCR Go调用示例
func createPaddleOCRGoExample() {
	fmt.Println("\n=== PaddleOCR Go调用示例 ===")

	goCode := `
// 使用Go调用PaddleOCR的完整示例
func callPaddleOCRFromGo(imagePath string) (string, error) {
    // 创建Python脚本
    script := ` + "`" + `
from paddleocr import PaddleOCR
import sys
import json

ocr = PaddleOCR(use_angle_cls=True, lang='ch')
result = ocr.ocr(sys.argv[1], cls=True)

texts = []
for idx in range(len(result)):
    res = result[idx]
    if res:
        for line in res:
            texts.append(line[1][0])

print(json.dumps(texts, ensure_ascii=False))
` + "`" + `
    
    // 写入临时文件
    tmpFile := "paddle_ocr_temp.py"
    err := os.WriteFile(tmpFile, []byte(script), 0644)
    if err != nil {
        return "", err
    }
    defer os.Remove(tmpFile)
    
    // 执行Python脚本
    cmd := exec.Command("python", tmpFile, imagePath)
    output, err := cmd.Output()
    if err != nil {
        return "", fmt.Errorf("PaddleOCR执行失败: %v", err)
    }
    
    // 解析JSON结果
    var texts []string
    err = json.Unmarshal(output, &texts)
    if err != nil {
        return string(output), nil // 如果JSON解析失败，返回原始输出
    }
    
    return strings.Join(texts, "\n"), nil
}`

	fmt.Println(goCode)
}

func main() {
	imagePath := "pic/医生或健康专家-B02-OCR-20250616_221124.png"

	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		fmt.Printf("图片文件不存在: %s\n", imagePath)
		fmt.Println("使用演示模式...")
	}

	demonstrateBetterOCR(imagePath)
	recommendPracticalSolution()
	createPaddleOCRGoExample()
}
