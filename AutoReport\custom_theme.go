package main

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
)

// 自定义主题结构体
type customTheme struct {
}

// 创建新的自定义主题
func newCustomTheme() fyne.Theme {
	return &customTheme{}
}

// 颜色 - 使用默认主题的颜色
func (t *customTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	return theme.DefaultTheme().Color(name, variant)
}

// 字体 - 使用默认主题的字体
func (t *customTheme) Font(style fyne.TextStyle) fyne.Resource {
	return theme.DefaultTheme().Font(style)
}

// 图标 - 使用默认主题的图标
func (t *customTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

// 字体大小 - 调整字体和图标大小
func (t *customTheme) <PERSON>ze(name fyne.ThemeSizeName) float32 {
	baseSize := theme.DefaultTheme().Size(name)

	// 文本相关大小调整为原始大小的84%（原来是70%，再放大20%）
	if name == theme.SizeNameText || name == theme.SizeNameHeadingText ||
		name == theme.SizeNameSubHeadingText || name == theme.SizeNameCaptionText {
		return baseSize * 0.84 // 0.7 * 1.2 = 0.84
	}

	// 内联图标大小放大一倍（原来是缩小60%，现在是原始大小的80%）
	if name == theme.SizeNameInlineIcon {
		return baseSize * 0.8
	}

	// 图标和按钮相关大小放大
	if name == theme.SizeNamePadding {
		return baseSize * 0.8
	}

	// 其他元素适当缩小
	return baseSize * 0.7
}
