#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import shutil
import cv2
import numpy as np

def test_with_english_filename():
    """使用英文文件名测试OCR"""
    
    # 原始中文文件名
    chinese_path = r"F:\myHbuilderAPP\MagneticOperator\pic\医生或健康专家-B02-OCR-20250616_221124.png"
    
    # 英文文件名
    english_path = r"F:\myHbuilderAPP\MagneticOperator\pic\medical_screenshot_test.png"
    
    print("=== 解决中文文件名问题的OCR测试 ===")
    
    # 检查原文件
    if not os.path.exists(chinese_path):
        print("ERROR: 原始图像文件不存在")
        return
    
    print("OK: 原始图像文件存在")
    
    # 复制到英文文件名
    try:
        shutil.copy2(chinese_path, english_path)
        print("OK: 复制到英文文件名成功")
    except Exception as e:
        print(f"ERROR: 复制文件失败: {e}")
        return
    
    # 验证文件可读性
    try:
        # 使用cv2.imdecode读取中文路径
        with open(chinese_path, 'rb') as f:
            image_data = f.read()
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        
        # 解码图像
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is not None:
            height, width = image.shape[:2]
            print(f"OK: 图像读取成功，尺寸: {width} x {height}")
            
            # 保存为英文文件名
            cv2.imwrite(english_path, image)
            print("OK: 图像保存为英文文件名成功")
        else:
            print("ERROR: 图像解码失败")
            return
            
    except Exception as e:
        print(f"ERROR: 图像处理失败: {e}")
        return
    
    # 测试EasyOCR
    try:
        import easyocr
        print("OK: EasyOCR可用")
        
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False, verbose=False)
        print("OK: EasyOCR初始化成功")
        
        # 使用英文文件名识别
        print("开始识别英文文件名图像...")
        start_time = time.time()
        results = reader.readtext(english_path)
        end_time = time.time()
        
        print(f"识别完成，耗时: {end_time - start_time:.2f}秒")
        print(f"识别结果数量: {len(results)}")
        
        if len(results) > 0:
            print("\n=== 识别结果 ===")
            for i, (bbox, text, confidence) in enumerate(results):
                print(f"结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
            # 查找目标模式
            print("\n=== 查找目标模式 ===")
            target_patterns = ["0.000", "0,000", "O.000", "0.00O", "o.000"]
            found_targets = []
            
            for bbox, text, confidence in results:
                for pattern in target_patterns:
                    if pattern in text:
                        found_targets.append((text, confidence, pattern))
                        print(f"找到目标: [{text}] 模式: {pattern} 置信度: {confidence:.3f}")
            
            if not found_targets:
                print("未找到目标数字模式")
                
                # 显示包含数字的结果
                print("\n=== 包含数字的结果 ===")
                for bbox, text, confidence in results:
                    if any(char.isdigit() for char in text):
                        print(f"数字文本: [{text}] 置信度: {confidence:.3f}")
        else:
            print("未识别到任何文本")
            
        # 尝试不同的预处理
        print("\n=== 尝试图像预处理 ===")
        
        # 读取图像
        image = cv2.imread(english_path)
        
        # 转换为灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 保存增强图像
        enhanced_path = english_path.replace('.png', '_enhanced.png')
        cv2.imwrite(enhanced_path, enhanced)
        
        # 识别增强图像
        print("识别增强图像...")
        start_time = time.time()
        results_enhanced = reader.readtext(enhanced_path)
        end_time = time.time()
        
        print(f"增强图像识别完成，耗时: {end_time - start_time:.2f}秒")
        print(f"增强图像识别结果数量: {len(results_enhanced)}")
        
        if len(results_enhanced) > 0:
            print("\n=== 增强图像识别结果 ===")
            for i, (bbox, text, confidence) in enumerate(results_enhanced):
                print(f"结果{i+1}: [{text}] 置信度: {confidence:.3f}")
            
            # 查找目标模式
            for bbox, text, confidence in results_enhanced:
                for pattern in target_patterns:
                    if pattern in text:
                        print(f"增强图像找到目标: [{text}] 模式: {pattern} 置信度: {confidence:.3f}")
        
        # 清理临时文件
        try:
            if os.path.exists(enhanced_path):
                os.remove(enhanced_path)
        except:
            pass
            
    except Exception as e:
        print(f"EasyOCR测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理临时文件
    try:
        if os.path.exists(english_path):
            os.remove(english_path)
    except:
        pass

if __name__ == "__main__":
    test_with_english_filename()
