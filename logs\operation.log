2025/05/31 13:39:56 [2025-05-31 13:39:56] [信息:应用启动成功]
2025/05/31 13:50:56 [2025-05-31 13:50:56] [操作:生成二维码] [用户:系统用户] [网点:]
2025/05/31 13:50:56 [2025-05-31 13:50:56] [操作:生成二维码] [用户:系统用户] [错误:配置未加载]
2025/05/31 15:31:38 [2025-05-31 15:31:38] [信息:应用启动成功]
2025/05/31 15:32:15 [2025-05-31 15:32:15] [操作:生成二维码] [用户:系统用户] [网点:]
2025/05/31 15:32:15 [2025-05-31 15:32:15] [操作:生成二维码] [用户:系统用户] [错误:配置未加载]
2025/05/31 15:32:20 [2025-05-31 15:32:20] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:50:43 [2025-06-01 00:50:43] [信息:应用启动成功]
2025/06/01 00:51:02 [2025-06-01 00:51:02] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:53:29 [2025-06-01 00:53:29] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/01 00:53:30 [2025-06-01 00:53:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:53:34 [2025-06-01 00:53:34] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:07 [2025-06-01 00:56:07] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:08 [2025-06-01 00:56:08] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:11 [2025-06-01 00:56:11] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/01 00:56:12 [2025-06-01 00:56:12] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/01 00:56:13 [2025-06-01 00:56:13] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:18 [2025-06-01 00:56:18] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:22 [2025-06-01 00:56:22] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/01 00:56:24 [2025-06-01 00:56:24] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:31 [2025-06-01 00:56:31] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:40 [2025-06-01 00:56:40] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:43 [2025-06-01 00:56:43] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:44 [2025-06-01 00:56:44] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:46 [2025-06-01 00:56:46] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:48 [2025-06-01 00:56:48] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:56:53 [2025-06-01 00:56:53] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:56:55 [2025-06-01 00:56:55] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:57:03 [2025-06-01 00:57:03] [操作:处理截图并上传] [用户:快速操作] [网点:]
2025/06/01 00:57:03 [2025-06-01 00:57:03] [操作:截取屏幕截图失败] [用户:快速操作] [错误:配置未加载]
2025/06/01 00:57:06 [2025-06-01 00:57:06] [操作:生成二维码] [用户:系统用户] [网点:]
2025/06/01 00:57:06 [2025-06-01 00:57:06] [操作:生成二维码] [用户:系统用户] [错误:配置未加载]
2025/06/01 00:57:22 [2025-06-01 00:57:22] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:57:24 [2025-06-01 00:57:24] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/01 00:57:25 [2025-06-01 00:57:25] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:57:30 [2025-06-01 00:57:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:57:32 [2025-06-01 00:57:32] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/01 00:57:33 [2025-06-01 00:57:33] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/01 00:57:40 [2025-06-01 00:57:40] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:57:42 [2025-06-01 00:57:42] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 00:57:43 [2025-06-01 00:57:43] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/01 00:57:45 [2025-06-01 00:57:45] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/01 00:59:15 [2025-06-01 00:59:15] [信息:应用启动成功]
2025/06/01 01:02:02 [2025-06-01 01:02:02] [信息:应用启动成功]
2025/06/01 01:13:19 [2025-06-01 01:13:19] [信息:应用启动成功]
2025/06/01 01:13:50 [2025-06-01 01:13:50] [信息:应用启动成功]
2025/06/01 01:15:49 [2025-06-01 01:15:49] [信息:应用启动成功]
2025/06/01 01:18:56 [2025-06-01 01:18:56] [信息:应用启动成功]
2025/06/01 01:19:36 [2025-06-01 01:19:36] [信息:应用启动成功]
2025/06/01 01:19:58 [2025-06-01 01:19:58] [信息:应用启动成功]
2025/06/01 01:20:10 [2025-06-01 01:20:10] [信息:应用启动成功]
2025/06/01 01:20:45 [2025-06-01 01:20:45] [信息:应用启动成功]
2025/06/01 01:20:59 [2025-06-01 01:20:59] [信息:应用启动成功]
2025/06/01 01:21:06 [2025-06-01 01:21:06] [信息:应用启动成功]
2025/06/01 01:24:22 [2025-06-01 01:24:22] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/01 01:25:51 [2025-06-01 01:25:51] [信息:应用启动成功]
2025/06/01 01:26:58 [2025-06-01 01:26:58] [信息:应用启动成功]
2025/06/01 01:27:54 [2025-06-01 01:27:54] [信息:应用启动成功]
2025/06/01 01:28:20 [2025-06-01 01:28:20] [信息:应用启动成功]
2025/06/01 01:29:14 [2025-06-01 01:29:14] [信息:应用启动成功]
2025/06/01 01:44:41 [2025-06-01 01:44:41] [信息:应用启动成功]
2025/06/01 01:45:58 [2025-06-01 01:45:58] [信息:应用启动成功]
2025/06/01 01:49:00 [2025-06-01 01:49:00] [信息:应用启动成功]
2025/06/01 02:03:09 [2025-06-01 02:03:09] [信息:应用启动成功]
2025/06/01 02:05:48 [2025-06-01 02:05:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/01 02:06:03 [2025-06-01 02:06:03] [信息:应用启动成功]
2025/06/01 02:06:04 [2025-06-01 02:06:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/01 02:21:33 [2025-06-01 02:21:33] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 09:57:00 [2025-06-02 09:57:00] [信息:应用启动成功]
2025/06/02 09:57:00 [2025-06-02 09:57:00] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:34:37 [2025-06-02 10:34:37] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:34:39 [2025-06-02 10:34:39] [信息:应用启动成功]
2025/06/02 10:34:39 [2025-06-02 10:34:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:34:40 [2025-06-02 10:34:40] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:34:41 [2025-06-02 10:34:41] [信息:应用启动成功]
2025/06/02 10:34:41 [2025-06-02 10:34:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:35:43 [2025-06-02 10:35:43] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:37:40 [2025-06-02 10:37:40] [信息:应用启动成功]
2025/06/02 10:37:40 [2025-06-02 10:37:40] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:37:40 [2025-06-02 10:37:40] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:37:43 [2025-06-02 10:37:43] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b07da17488318617683602234] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}"}]
2025/06/02 10:38:23 [2025-06-02 10:38:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:38:24 [2025-06-02 10:38:24] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b083f17488319030115118183] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}"}]
2025/06/02 10:39:14 [2025-06-02 10:39:14] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:39:14 [2025-06-02 10:39:14] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:39:14 [2025-06-02 10:39:14] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 10:39:15 [2025-06-02 10:39:15] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b07da17488319541166309234] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}"}]
2025/06/02 13:39:13 [2025-06-02 13:39:13] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 13:39:13 [2025-06-02 13:39:13] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b4c5717488427530784180220] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: \\\"/\\\" is not a valid cloudobject path. stack: Error: \\\"/\\\" is not a valid cloudobject path\\n    at a (/var/task/code/__index.js:1:1061)\\n    at new i (/var/task/code/__index.js:1:1590)\\n    at c (/var/task/code/__index.js:1:2401)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\"}"}]
2025/06/02 14:11:06 [2025-06-02 14:11:06] [信息:应用启动成功]
2025/06/02 14:11:06 [2025-06-02 14:11:06] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 14:11:06 [2025-06-02 14:11:06] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 14:11:07 [2025-06-02 14:11:07] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b07da17488446665757277234] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: -. stack: -\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: -. stack: -\"}"}]
2025/06/02 14:13:54 [2025-06-02 14:13:54] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 14:13:54 [2025-06-02 14:13:54] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 14:13:54 [2025-06-02 14:13:54] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b4d3117488448339345742220] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: -. stack: -\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: -. stack: -\"}"}]
2025/06/02 16:41:00 [2025-06-02 16:41:00] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 16:41:00 [2025-06-02 16:41:00] [操作:获取候检者列表] [用户:系统用户] [错误:API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-registration-CloudObj][0a4b4d3117488536598351128220] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: -. stack: -\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: -. stack: -\"}"}]
2025/06/02 17:05:04 [2025-06-02 17:05:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:05:04 [2025-06-02 17:05:04] [操作:获取候检者列表] [用户:系统用户] [错误:解析响应失败: json: cannot unmarshal string into Go struct field .errCode of type int]
2025/06/02 17:06:24 [2025-06-02 17:06:24] [信息:应用启动成功]
2025/06/02 17:06:24 [2025-06-02 17:06:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:06:24 [2025-06-02 17:06:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:06:25 [2025-06-02 17:06:25] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: db.collection(...).where(...).lookup is not a function]
2025/06/02 17:15:23 [2025-06-02 17:15:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:15:24 [2025-06-02 17:15:24] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: lookup方法内只允许使用子查询（let+pipeline）方式]
2025/06/02 17:16:12 [2025-06-02 17:16:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:16:13 [2025-06-02 17:16:13] [操作:获取候检者列表] [用户:系统用户] [错误:解析响应失败: json: cannot unmarshal number into Go struct field .errCode of type string]
2025/06/02 17:18:08 [2025-06-02 17:18:08] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:18:43 [2025-06-02 17:18:43] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 17:18:43 [2025-06-02 17:18:43] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:19:00 [2025-06-02 19:19:00] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:26:23 [2025-06-02 19:26:23] [信息:应用启动成功]
2025/06/02 19:26:24 [2025-06-02 19:26:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:26:24 [2025-06-02 19:26:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:11 [2025-06-02 19:28:11] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:11 [2025-06-02 19:28:11] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:16 [2025-06-02 19:28:16] [信息:应用启动成功]
2025/06/02 19:28:17 [2025-06-02 19:28:17] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:17 [2025-06-02 19:28:17] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:35 [2025-06-02 19:28:35] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:35 [2025-06-02 19:28:35] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:35 [2025-06-02 19:28:35] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:35 [2025-06-02 19:28:35] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:37 [2025-06-02 19:28:37] [信息:应用启动成功]
2025/06/02 19:28:39 [2025-06-02 19:28:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:28:39 [2025-06-02 19:28:39] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:32:50 [2025-06-02 19:32:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:33:40 [2025-06-02 19:33:40] [信息:应用启动成功]
2025/06/02 19:33:40 [2025-06-02 19:33:40] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:33:40 [2025-06-02 19:33:40] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:04 [2025-06-02 19:35:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:04 [2025-06-02 19:35:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:09 [2025-06-02 19:35:09] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:09 [2025-06-02 19:35:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:09 [2025-06-02 19:35:09] [信息:应用启动成功]
2025/06/02 19:35:10 [2025-06-02 19:35:10] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:10 [2025-06-02 19:35:10] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:20 [2025-06-02 19:35:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:20 [2025-06-02 19:35:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:20 [2025-06-02 19:35:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:35:20 [2025-06-02 19:35:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:37:17 [2025-06-02 19:37:17] [信息:应用启动成功]
2025/06/02 19:37:19 [2025-06-02 19:37:19] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:37:19 [2025-06-02 19:37:19] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:46:26 [2025-06-02 19:46:26] [信息:应用启动成功]
2025/06/02 19:46:27 [2025-06-02 19:46:27] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:46:27 [2025-06-02 19:46:27] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:50:37 [2025-06-02 19:50:37] [信息:应用启动成功]
2025/06/02 19:50:38 [2025-06-02 19:50:38] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:50:38 [2025-06-02 19:50:38] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:51:50 [2025-06-02 19:51:50] [信息:应用启动成功]
2025/06/02 19:51:50 [2025-06-02 19:51:50] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:51:50 [2025-06-02 19:51:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:54:20 [2025-06-02 19:54:20] [信息:应用启动成功]
2025/06/02 19:54:20 [2025-06-02 19:54:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:54:20 [2025-06-02 19:54:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:54:59 [2025-06-02 19:54:59] [信息:应用启动成功]
2025/06/02 19:54:59 [2025-06-02 19:54:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 19:54:59 [2025-06-02 19:54:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:06:24 [2025-06-02 20:06:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:06:24 [2025-06-02 20:06:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:06:25 [2025-06-02 20:06:25] [信息:应用启动成功]
2025/06/02 20:06:25 [2025-06-02 20:06:25] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:06:25 [2025-06-02 20:06:25] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:08:36 [2025-06-02 20:08:36] [信息:应用启动成功]
2025/06/02 20:08:37 [2025-06-02 20:08:37] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:08:37 [2025-06-02 20:08:37] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:14:55 [2025-06-02 20:14:55] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 20:14:55 [2025-06-02 20:14:55] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:17:27 [2025-06-02 21:17:27] [信息:应用启动成功]
2025/06/02 21:17:28 [2025-06-02 21:17:28] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:17:28 [2025-06-02 21:17:28] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:17:38 [2025-06-02 21:17:38] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/02 21:22:03 [2025-06-02 21:22:03] [信息:应用启动成功]
2025/06/02 21:22:04 [2025-06-02 21:22:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:22:04 [2025-06-02 21:22:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:41:09 [2025-06-02 21:41:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:42:10 [2025-06-02 21:42:10] [信息:应用启动成功]
2025/06/02 21:42:11 [2025-06-02 21:42:11] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:42:11 [2025-06-02 21:42:11] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:47:58 [2025-06-02 21:47:58] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:47:58 [2025-06-02 21:47:58] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:47:59 [2025-06-02 21:47:59] [信息:应用启动成功]
2025/06/02 21:47:59 [2025-06-02 21:47:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:47:59 [2025-06-02 21:47:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:48:15 [2025-06-02 21:48:15] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/02 21:48:15 [2025-06-02 21:48:15] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 00:02:04 [2025-06-03 00:02:04] [信息:应用启动成功]
2025/06/03 00:02:05 [2025-06-03 00:02:05] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 00:02:05 [2025-06-03 00:02:05] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 00:02:05 [2025-06-03 00:02:05] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: invalid $match]
2025/06/03 00:03:09 [2025-06-03 00:03:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 00:06:48 [2025-06-03 00:06:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 00:06:48 [2025-06-03 00:06:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:05:25 [2025-06-03 01:05:25] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:05:25 [2025-06-03 01:05:25] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: skip is not defined]
2025/06/03 01:05:33 [2025-06-03 01:05:33] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:05:33 [2025-06-03 01:05:33] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: skip is not defined]
2025/06/03 01:12:41 [2025-06-03 01:12:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:12:41 [2025-06-03 01:12:41] [操作:获取候检者列表] [用户:系统用户] [错误:云函数返回错误: allRegistrationRes is not defined]
2025/06/03 01:14:10 [2025-06-03 01:14:10] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:20:27 [2025-06-03 01:20:27] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:25:16 [2025-06-03 01:25:16] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:29:09 [2025-06-03 01:29:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:29:24 [2025-06-03 01:29:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 01:29:24 [2025-06-03 01:29:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:21:38 [2025-06-03 15:21:38] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:27:55 [2025-06-03 15:27:55] [信息:应用启动成功]
2025/06/03 15:27:55 [2025-06-03 15:27:55] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:27:55 [2025-06-03 15:27:55] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:30:24 [2025-06-03 15:30:24] [信息:应用启动成功]
2025/06/03 15:30:25 [2025-06-03 15:30:25] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:30:25 [2025-06-03 15:30:25] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:31:22 [2025-06-03 15:31:22] [信息:应用启动成功]
2025/06/03 15:31:23 [2025-06-03 15:31:23] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:31:23 [2025-06-03 15:31:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:47:04 [2025-06-03 15:47:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:47:04 [2025-06-03 15:47:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:47:06 [2025-06-03 15:47:06] [信息:应用启动成功]
2025/06/03 15:47:07 [2025-06-03 15:47:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:47:07 [2025-06-03 15:47:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:50:40 [2025-06-03 15:50:40] [信息:应用启动成功]
2025/06/03 15:50:41 [2025-06-03 15:50:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:50:41 [2025-06-03 15:50:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:51:24 [2025-06-03 15:51:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:51:24 [2025-06-03 15:51:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:51:26 [2025-06-03 15:51:26] [信息:应用启动成功]
2025/06/03 15:51:26 [2025-06-03 15:51:26] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 15:51:26 [2025-06-03 15:51:26] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 16:11:47 [2025-06-03 16:11:47] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 16:21:21 [2025-06-03 16:21:21] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 16:21:21 [2025-06-03 16:21:21] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 17:25:24 [2025-06-03 17:25:24] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:24 [2025-06-03 17:25:24] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:25 [2025-06-03 17:25:25] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:25 [2025-06-03 17:25:25] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:26 [2025-06-03 17:25:26] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:26 [2025-06-03 17:25:26] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:26 [2025-06-03 17:25:26] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:26 [2025-06-03 17:25:26] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:30 [2025-06-03 17:25:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:30 [2025-06-03 17:25:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:30 [2025-06-03 17:25:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:30 [2025-06-03 17:25:30] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:31 [2025-06-03 17:25:31] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:25:31 [2025-06-03 17:25:31] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:25:34 [2025-06-03 17:25:34] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/03 17:25:34 [2025-06-03 17:25:34] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/03 17:25:36 [2025-06-03 17:25:36] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/03 17:25:36 [2025-06-03 17:25:36] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/03 17:25:37 [2025-06-03 17:25:37] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/03 17:25:37 [2025-06-03 17:25:37] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/03 17:25:38 [2025-06-03 17:25:38] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/03 17:25:38 [2025-06-03 17:25:38] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/03 17:25:39 [2025-06-03 17:25:39] [操作:设置窗体位置] [用户:系统用户] [网点:right]
2025/06/03 17:25:39 [2025-06-03 17:25:39] [操作:设置窗体位置] [用户:系统用户] [网点:left]
2025/06/03 17:27:09 [2025-06-03 17:27:09] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/03 17:27:16 [2025-06-03 17:27:16] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL]
2025/06/03 17:27:38 [2025-06-03 17:27:38] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/03 17:27:41 [2025-06-03 17:27:41] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/03 17:41:49 [2025-06-03 17:41:49] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/03 17:41:56 [2025-06-03 17:41:56] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL]
2025/06/03 17:54:26 [2025-06-03 17:54:26] [信息:应用启动成功]
2025/06/03 17:54:44 [2025-06-03 17:54:44] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 17:54:44 [2025-06-03 17:54:44] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 18:01:40 [2025-06-03 18:01:40] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/03 18:01:47 [2025-06-03 18:01:47] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/03 18:01:47 [2025-06-03 18:01:47] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/03 18:01:54 [2025-06-03 18:01:54] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/03 18:01:54 [2025-06-03 18:01:54] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/03 18:01:59 [2025-06-03 18:01:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 18:01:59 [2025-06-03 18:01:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:10:57 [2025-06-03 22:10:57] [信息:应用启动成功]
2025/06/03 22:10:57 [2025-06-03 22:10:57] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:10:57 [2025-06-03 22:10:57] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:05 [2025-06-03 22:11:05] [信息:应用启动成功]
2025/06/03 22:11:08 [2025-06-03 22:11:08] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:08 [2025-06-03 22:11:08] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:39 [2025-06-03 22:11:39] [信息:应用启动成功]
2025/06/03 22:11:39 [2025-06-03 22:11:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:39 [2025-06-03 22:11:39] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:46 [2025-06-03 22:11:46] [信息:应用启动成功]
2025/06/03 22:11:48 [2025-06-03 22:11:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:11:48 [2025-06-03 22:11:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:14:01 [2025-06-03 22:14:01] [信息:应用启动成功]
2025/06/03 22:14:01 [2025-06-03 22:14:01] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:14:01 [2025-06-03 22:14:01] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:14:18 [2025-06-03 22:14:18] [信息:应用启动成功]
2025/06/03 22:14:18 [2025-06-03 22:14:18] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/03 22:14:18 [2025-06-03 22:14:18] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:18:48 [2025-06-04 00:18:48] [信息:应用启动成功]
2025/06/04 00:18:48 [2025-06-04 00:18:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:18:48 [2025-06-04 00:18:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:19:11 [2025-06-04 00:19:11] [信息:应用启动成功]
2025/06/04 00:19:12 [2025-06-04 00:19:12] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:19:12 [2025-06-04 00:19:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:19:35 [2025-06-04 00:19:35] [信息:应用启动成功]
2025/06/04 00:19:36 [2025-06-04 00:19:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:19:36 [2025-06-04 00:19:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:20:48 [2025-06-04 00:20:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:24:12 [2025-06-04 00:24:12] [信息:应用启动成功]
2025/06/04 00:24:12 [2025-06-04 00:24:12] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:24:12 [2025-06-04 00:24:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:25:05 [2025-06-04 00:25:05] [信息:应用启动成功]
2025/06/04 00:25:05 [2025-06-04 00:25:05] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:25:05 [2025-06-04 00:25:05] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:25:16 [2025-06-04 00:25:16] [信息:应用启动成功]
2025/06/04 00:25:16 [2025-06-04 00:25:16] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 00:25:16 [2025-06-04 00:25:16] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 01:02:54 [2025-06-04 01:02:54] [信息:应用启动成功]
2025/06/04 01:02:55 [2025-06-04 01:02:55] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 01:02:55 [2025-06-04 01:02:55] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 01:41:36 [2025-06-04 01:41:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 01:41:36 [2025-06-04 01:41:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 18:07:32 [2025-06-04 18:07:32] [信息:应用启动成功]
2025/06/04 18:07:33 [2025-06-04 18:07:33] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/04 18:07:33 [2025-06-04 18:07:33] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/05 11:44:10 [2025-06-05 11:44:10] [信息:开始从API加载站点信息...]
2025/06/05 11:44:11 [2025-06-05 11:44:11] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 11:44:11 [2025-06-05 11:44:11] [信息:应用启动成功]
2025/06/05 11:44:11 [2025-06-05 11:44:11] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 11:44:11 [2025-06-05 11:44:11] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 12:04:44 [2025-06-05 12:04:44] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 12:04:44 [2025-06-05 12:04:44] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 13:59:20 [2025-06-05 13:59:20] [信息:开始从API加载站点信息...]
2025/06/05 13:59:21 [2025-06-05 13:59:21] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 13:59:21 [2025-06-05 13:59:21] [信息:应用启动成功]
2025/06/05 13:59:21 [2025-06-05 13:59:21] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 13:59:21 [2025-06-05 13:59:21] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 14:01:29 [2025-06-05 14:01:29] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 14:01:29 [2025-06-05 14:01:29] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 16:16:22 [2025-06-05 16:16:22] [信息:开始从API加载站点信息...]
2025/06/05 16:16:22 [2025-06-05 16:16:22] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 16:16:22 [2025-06-05 16:16:22] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 16:16:22 [2025-06-05 16:16:22] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 16:16:22 [2025-06-05 16:16:22] [信息:应用启动成功]
2025/06/05 16:21:53 [2025-06-05 16:21:53] [信息:开始从API加载站点信息...]
2025/06/05 16:21:53 [2025-06-05 16:21:53] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 16:21:53 [2025-06-05 16:21:53] [信息:应用启动成功]
2025/06/05 16:21:53 [2025-06-05 16:21:53] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 16:21:53 [2025-06-05 16:21:53] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 16:37:57 [2025-06-05 16:37:57] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 16:37:57 [2025-06-05 16:37:57] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 16:38:00 [2025-06-05 16:38:00] [信息:开始从API加载站点信息...]
2025/06/05 16:38:00 [2025-06-05 16:38:00] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 16:38:00 [2025-06-05 16:38:00] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 16:38:00 [2025-06-05 16:38:00] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 16:38:00 [2025-06-05 16:38:00] [信息:应用启动成功]
2025/06/05 16:48:56 [2025-06-05 16:48:56] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 16:48:56 [2025-06-05 16:48:56] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 17:18:30 [2025-06-05 17:18:30] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/05 21:39:58 [2025-06-05 21:39:58] [信息:开始从API加载站点信息...]
2025/06/05 21:39:59 [2025-06-05 21:39:59] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 21:39:59 [2025-06-05 21:39:59] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 21:39:59 [2025-06-05 21:39:59] [信息:应用启动成功]
2025/06/05 21:39:59 [2025-06-05 21:39:59] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:09:42 [2025-06-05 22:09:42] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:09:52 [2025-06-05 22:09:52] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:10:52 [2025-06-05 22:10:52] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 22:10:52 [2025-06-05 22:10:52] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:10:55 [2025-06-05 22:10:55] [信息:开始从API加载站点信息...]
2025/06/05 22:10:55 [2025-06-05 22:10:55] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 22:10:55 [2025-06-05 22:10:55] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:10:55 [2025-06-05 22:10:55] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空]
2025/06/05 22:10:55 [2025-06-05 22:10:55] [信息:应用启动成功]
2025/06/05 22:14:16 [2025-06-05 22:14:16] [信息:开始从API加载站点信息...]
2025/06/05 22:14:17 [2025-06-05 22:14:17] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: ]
2025/06/05 22:14:17 [2025-06-05 22:14:17] [信息:应用启动成功]
2025/06/05 22:14:17 [2025-06-05 22:14:17] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 22:14:17 [2025-06-05 22:14:17] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 22:21:24 [2025-06-05 22:21:24] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 22:21:24 [2025-06-05 22:21:24] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 23:33:38 [2025-06-05 23:33:38] [信息:开始从API加载站点信息...]
2025/06/05 23:33:39 [2025-06-05 23:33:39] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 23:33:39 [2025-06-05 23:33:39] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/05 23:33:39 [2025-06-05 23:33:39] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数未返回有效的站点信息]
2025/06/05 23:33:39 [2025-06-05 23:33:39] [信息:应用启动成功]
2025/06/05 23:44:07 [2025-06-05 23:44:07] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/05 23:44:07 [2025-06-05 23:44:07] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 00:03:14 [2025-06-06 00:03:14] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 00:03:14 [2025-06-06 00:03:14] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:08:22 [2025-06-06 01:08:22] [信息:开始从API加载站点信息...]
2025/06/06 01:08:23 [2025-06-06 01:08:23] [信息:GetConfig called. Current a.config.SiteInfo: {SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}}]
2025/06/06 01:08:23 [2025-06-06 01:08:23] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数未返回有效的站点信息]
2025/06/06 01:08:23 [2025-06-06 01:08:23] [信息:应用启动成功]
2025/06/06 01:08:23 [2025-06-06 01:08:23] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:08:23 [2025-06-06 01:08:23] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:09:28 [2025-06-06 01:09:28] [信息:开始从API加载站点信息...]
2025/06/06 01:09:29 [2025-06-06 01:09:29] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数未返回有效的站点信息]
2025/06/06 01:09:29 [2025-06-06 01:09:29] [信息:应用启动成功]
2025/06/06 01:09:29 [2025-06-06 01:09:29] [信息:GetConfig called. Current a.config.SiteInfo: {SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}}]
2025/06/06 01:09:29 [2025-06-06 01:09:29] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:09:29 [2025-06-06 01:09:29] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:20:11 [2025-06-06 01:20:11] [信息:日志系统初始化成功]
2025/06/06 01:20:11 [2025-06-06 01:20:11] [信息:开始初始化服务...]
2025/06/06 01:20:11 [2025-06-06 01:20:11] [信息:成功加载配置文件]
2025/06/06 01:20:11 [2025-06-06 01:20:11] [信息:开始从API加载站点信息...]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数未返回有效的站点信息]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [信息:应用启动成功]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:20:12 [2025-06-06 01:20:12] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:47:24 [2025-06-06 01:47:24] [信息:日志系统初始化成功]
2025/06/06 01:47:24 [2025-06-06 01:47:24] [信息:开始初始化服务...]
2025/06/06 01:47:24 [2025-06-06 01:47:24] [信息:成功加载配置文件]
2025/06/06 01:47:24 [2025-06-06 01:47:24] [信息:开始从API加载站点信息...]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空 (errCode: PARAM_ERROR)]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [信息:应用启动成功]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:47:25 [2025-06-06 01:47:25] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:日志系统初始化成功]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:开始初始化服务...]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:成功加载配置文件]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:开始从API加载站点信息...]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空 (errCode: PARAM_ERROR)]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [信息:应用启动成功]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:51:06 [2025-06-06 01:51:06] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:日志系统初始化成功]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:开始初始化服务...]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:成功加载配置文件]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:开始从API加载站点信息...]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 01:57:43 [2025-06-06 01:57:43] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: [getSiteInfoByDeviceMAC] API调用失败，状态码: 500, 响应: {"errDetail":"[env-00jxtfqc9gq1][hc-actions-CloudObj][0a4b4c5717491462634226015220] User Function Code Logic Error, Please Check Function Code: {\"code\":\"400\",\"message\":\"error: Cannot destructure property 'mac_address' of 'actualParams.mac_address' as it is undefined.. stack: TypeError: Cannot destructure property 'mac_address' of 'actualParams.mac_address' as it is undefined.\\n    at i.getSiteInfoByDeviceMAC (/var/task/code/index.obj.js:204:11)\\n    at c (/var/task/code/__index.js:1:2749)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\\n    at async Container.dispatch (/var/task/index.js:68635:14)\"}","errCode":"40000","errMsg":"用户函数代码语法或逻辑异常","errOrigin":"{\"code\":\"400\",\"message\":\"error: Cannot destructure property 'mac_address' of 'actualParams.mac_address' as it is undefined.. stack: TypeError: Cannot destructure property 'mac_address' of 'actualParams.mac_address' as it is undefined.\\n    at i.getSiteInfoByDeviceMAC (/var/task/code/index.obj.js:204:11)\\n    at c (/var/task/code/__index.js:1:2749)\\n    at exports.main (/var/task/code/__index.js:1:2891)\\n    at He (/var/task/code/index.js:2:86358)\\n    at We (/var/task/code/index.js:2:86587)\\n    at ar (/var/task/code/index.js:2:88412)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async FetchEventPearl2.handleRequest (/var/task/index.js:62628:17)\\n    at async Container.dispatchHttpRequest (/var/task/index.js:68654:17)\\n    at async Container.dispatch (/var/task/index.js:68635:14)\"}"}]
2025/06/06 01:57:44 [2025-06-06 01:57:44] [信息:应用启动成功]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:日志系统初始化成功]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:开始初始化服务...]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:成功加载配置文件]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:开始从API加载站点信息...]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误: 设备MAC地址不能为空 (errCode: PARAM_ERROR)]
2025/06/06 02:03:48 [2025-06-06 02:03:48] [信息:应用启动成功]
2025/06/06 02:03:49 [2025-06-06 02:03:49] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:03:49 [2025-06-06 02:03:49] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:09:59 [2025-06-06 02:09:59] [信息:日志系统初始化成功]
2025/06/06 02:09:59 [2025-06-06 02:09:59] [信息:开始初始化服务...]
2025/06/06 02:09:59 [2025-06-06 02:09:59] [信息:成功加载配置文件]
2025/06/06 02:09:59 [2025-06-06 02:09:59] [信息:开始从API加载站点信息...]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [信息:从API加载并更新站点信息成功]
2025/06/06 02:10:00 [2025-06-06 02:10:00] [信息:应用启动成功]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:日志系统初始化成功]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:开始初始化服务...]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:成功加载配置文件]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:开始从API加载站点信息...]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:从API加载并更新站点信息成功]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:应用启动成功]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:23:06 [2025-06-06 02:23:06] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:23:07 [2025-06-06 02:23:07] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:23:07 [2025-06-06 02:23:07] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:25:21 [2025-06-06 02:25:21] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:25:21 [2025-06-06 02:25:21] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:25:21 [2025-06-06 02:25:21] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:25:21 [2025-06-06 02:25:21] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:25:28 [2025-06-06 02:25:28] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:25:28 [2025-06-06 02:25:28] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:25:28 [2025-06-06 02:25:28] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:25:29 [2025-06-06 02:25:29] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:29:28 [2025-06-06 02:29:28] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:29:28 [2025-06-06 02:29:28] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:29:28 [2025-06-06 02:29:28] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:29:28 [2025-06-06 02:29:28] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:日志系统初始化成功]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:开始初始化服务...]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:成功加载配置文件]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:开始从API加载站点信息...]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:29:33 [2025-06-06 02:29:33] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:29:34 [2025-06-06 02:29:34] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误:  (errCode: )]
2025/06/06 02:29:34 [2025-06-06 02:29:34] [信息:应用启动成功]
2025/06/06 02:30:04 [2025-06-06 02:30:04] [信息:日志系统初始化成功]
2025/06/06 02:30:04 [2025-06-06 02:30:04] [信息:开始初始化服务...]
2025/06/06 02:30:04 [2025-06-06 02:30:04] [信息:成功加载配置文件]
2025/06/06 02:30:04 [2025-06-06 02:30:04] [信息:开始从API加载站点信息...]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 云函数返回错误:  (errCode: )]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [信息:应用启动成功]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:30:05 [2025-06-06 02:30:05] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:32:53 [2025-06-06 02:32:53] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:32:53 [2025-06-06 02:32:53] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:32:53 [2025-06-06 02:32:53] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:32:53 [2025-06-06 02:32:53] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:日志系统初始化成功]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:开始初始化服务...]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:成功加载配置文件]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:开始从API加载站点信息...]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:从API加载并更新站点信息成功]
2025/06/06 02:32:56 [2025-06-06 02:32:56] [信息:应用启动成功]
2025/06/06 02:34:42 [2025-06-06 02:34:42] [信息:日志系统初始化成功]
2025/06/06 02:34:42 [2025-06-06 02:34:42] [信息:开始初始化服务...]
2025/06/06 02:34:42 [2025-06-06 02:34:42] [信息:成功加载配置文件]
2025/06/06 02:34:42 [2025-06-06 02:34:42] [信息:开始从API加载站点信息...]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [信息:从API加载并更新站点信息成功]
2025/06/06 02:34:43 [2025-06-06 02:34:43] [信息:应用启动成功]
2025/06/06 02:36:50 [2025-06-06 02:36:50] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:36:50 [2025-06-06 02:36:50] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:36:50 [2025-06-06 02:36:50] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:36:50 [2025-06-06 02:36:50] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:日志系统初始化成功]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:开始初始化服务...]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:成功加载配置文件]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:开始从API加载站点信息...]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 02:36:53 [2025-06-06 02:36:53] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 02:36:54 [2025-06-06 02:36:54] [信息:从API加载并更新站点信息成功]
2025/06/06 02:36:54 [2025-06-06 02:36:54] [信息:应用启动成功]
2025/06/06 02:38:17 [2025-06-06 02:38:17] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:38:17 [2025-06-06 02:38:17] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 02:38:17 [2025-06-06 02:38:17] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 02:38:17 [2025-06-06 02:38:17] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 02:50:13 [2025-06-06 02:50:13] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/06 02:50:27 [2025-06-06 02:50:27] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/06 02:51:23 [2025-06-06 02:51:23] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:51:23 [2025-06-06 02:51:23] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 02:51:23 [2025-06-06 02:51:23] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 02:51:23 [2025-06-06 02:51:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 02:57:21 [2025-06-06 02:57:21] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 02:57:21 [2025-06-06 02:57:21] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 02:57:21 [2025-06-06 02:57:21] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 02:57:21 [2025-06-06 02:57:21] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:日志系统初始化成功]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:开始初始化服务...]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:成功加载配置文件]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:开始从API加载站点信息...]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:从API加载并更新站点信息成功]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [信息:应用启动成功]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:01:19 [2025-06-06 03:01:19] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:01:46 [2025-06-06 03:01:46] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/06 03:01:50 [2025-06-06 03:01:50] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/06 03:01:52 [2025-06-06 03:01:52] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/06 03:02:02 [2025-06-06 03:02:02] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:日志系统初始化成功]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:开始初始化服务...]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:成功加载配置文件]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:开始从API加载站点信息...]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 03:03:51 [2025-06-06 03:03:51] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 03:03:52 [2025-06-06 03:03:52] [信息:从API加载并更新站点信息成功]
2025/06/06 03:03:52 [2025-06-06 03:03:52] [信息:应用启动成功]
2025/06/06 03:06:19 [2025-06-06 03:06:19] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:06:19 [2025-06-06 03:06:19] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:06:19 [2025-06-06 03:06:19] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 03:06:19 [2025-06-06 03:06:19] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 03:06:38 [2025-06-06 03:06:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:06:38 [2025-06-06 03:06:38] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:06:38 [2025-06-06 03:06:38] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 03:06:38 [2025-06-06 03:06:38] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 03:07:28 [2025-06-06 03:07:28] [信息:日志系统初始化成功]
2025/06/06 03:07:28 [2025-06-06 03:07:28] [信息:开始初始化服务...]
2025/06/06 03:07:28 [2025-06-06 03:07:28] [信息:成功加载配置文件]
2025/06/06 03:07:28 [2025-06-06 03:07:28] [信息:开始从API加载站点信息...]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [信息:从API加载并更新站点信息成功]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [信息:应用启动成功]
2025/06/06 03:07:29 [2025-06-06 03:07:29] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:10:17 [2025-06-06 03:10:17] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:10:17 [2025-06-06 03:10:17] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:10:17 [2025-06-06 03:10:17] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:10:17 [2025-06-06 03:10:17] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:11:06 [2025-06-06 03:11:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:11:06 [2025-06-06 03:11:06] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:11:06 [2025-06-06 03:11:06] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:11:06 [2025-06-06 03:11:06] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:11:30 [2025-06-06 03:11:30] [信息:日志系统初始化成功]
2025/06/06 03:11:30 [2025-06-06 03:11:30] [信息:开始初始化服务...]
2025/06/06 03:11:30 [2025-06-06 03:11:30] [信息:成功加载配置文件]
2025/06/06 03:11:30 [2025-06-06 03:11:30] [信息:开始从API加载站点信息...]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [信息:从API加载并更新站点信息成功]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [信息:应用启动成功]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:11:31 [2025-06-06 03:11:31] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:14:07 [2025-06-06 03:14:07] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:14:07 [2025-06-06 03:14:07] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:14:07 [2025-06-06 03:14:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:14:07 [2025-06-06 03:14:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:14:46 [2025-06-06 03:14:46] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:14:46 [2025-06-06 03:14:46] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:14:46 [2025-06-06 03:14:46] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:14:46 [2025-06-06 03:14:46] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:日志系统初始化成功]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:开始初始化服务...]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:成功加载配置文件]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:开始从API加载站点信息...]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 03:15:24 [2025-06-06 03:15:24] [信息:从API加载并更新站点信息成功]
2025/06/06 03:15:25 [2025-06-06 03:15:25] [信息:应用启动成功]
2025/06/06 03:20:22 [2025-06-06 03:20:22] [信息:日志系统初始化成功]
2025/06/06 03:20:22 [2025-06-06 03:20:22] [信息:开始初始化服务...]
2025/06/06 03:20:22 [2025-06-06 03:20:22] [信息:成功加载配置文件]
2025/06/06 03:20:22 [2025-06-06 03:20:22] [信息:开始从API加载站点信息...]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [信息:从API加载并更新站点信息成功]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [信息:应用启动成功]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:20:23 [2025-06-06 03:20:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:22:32 [2025-06-06 03:22:32] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:22:32 [2025-06-06 03:22:32] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:22:32 [2025-06-06 03:22:32] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:22:32 [2025-06-06 03:22:32] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:22:51 [2025-06-06 03:22:51] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:22:51 [2025-06-06 03:22:51] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:22:51 [2025-06-06 03:22:51] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:22:51 [2025-06-06 03:22:51] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:23:19 [2025-06-06 03:23:19] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/06 03:23:21 [2025-06-06 03:23:21] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/06 03:25:02 [2025-06-06 03:25:02] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:25:02 [2025-06-06 03:25:02] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:25:02 [2025-06-06 03:25:02] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:25:02 [2025-06-06 03:25:02] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:25:18 [2025-06-06 03:25:18] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:25:18 [2025-06-06 03:25:18] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:25:18 [2025-06-06 03:25:18] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:25:18 [2025-06-06 03:25:18] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:25:43 [2025-06-06 03:25:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:25:43 [2025-06-06 03:25:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:25:43 [2025-06-06 03:25:43] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:25:43 [2025-06-06 03:25:43] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:28:50 [2025-06-06 03:28:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:04 [2025-06-06 03:29:04] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:04 [2025-06-06 03:29:04] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:04 [2025-06-06 03:29:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:04 [2025-06-06 03:29:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:10 [2025-06-06 03:29:10] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:10 [2025-06-06 03:29:10] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:10 [2025-06-06 03:29:10] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:10 [2025-06-06 03:29:10] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:15 [2025-06-06 03:29:15] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:15 [2025-06-06 03:29:15] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:15 [2025-06-06 03:29:15] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:15 [2025-06-06 03:29:15] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:35 [2025-06-06 03:29:35] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:35 [2025-06-06 03:29:35] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:35 [2025-06-06 03:29:35] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:35 [2025-06-06 03:29:35] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:36 [2025-06-06 03:29:36] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:36 [2025-06-06 03:29:36] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:36 [2025-06-06 03:29:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:36 [2025-06-06 03:29:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:38 [2025-06-06 03:29:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:29:38 [2025-06-06 03:29:38] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:29:38 [2025-06-06 03:29:38] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:29:38 [2025-06-06 03:29:38] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:31:42 [2025-06-06 03:31:42] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:31:42 [2025-06-06 03:31:42] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:31:42 [2025-06-06 03:31:42] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:31:42 [2025-06-06 03:31:42] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:31:59 [2025-06-06 03:31:59] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 03:31:59 [2025-06-06 03:31:59] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 03:31:59 [2025-06-06 03:31:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 03:31:59 [2025-06-06 03:31:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 09:58:02 [2025-06-06 09:58:02] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 09:58:02 [2025-06-06 09:58:02] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 09:58:02 [2025-06-06 09:58:02] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 09:58:03 [2025-06-06 09:58:03] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 09:58:03 [2025-06-06 09:58:03] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 09:58:03 [2025-06-06 09:58:03] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 09:58:03 [2025-06-06 09:58:03] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 09:58:03 [2025-06-06 09:58:03] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:12:32 [2025-06-06 10:12:32] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:12:32 [2025-06-06 10:12:32] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:12:32 [2025-06-06 10:12:32] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:12:32 [2025-06-06 10:12:32] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:12:35 [2025-06-06 10:12:35] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:12:35 [2025-06-06 10:12:35] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:14:22 [2025-06-06 10:14:22] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:14:22 [2025-06-06 10:14:22] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:14:22 [2025-06-06 10:14:22] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:14:22 [2025-06-06 10:14:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:14:25 [2025-06-06 10:14:25] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:14:25 [2025-06-06 10:14:25] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:14:31 [2025-06-06 10:14:31] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:14:31 [2025-06-06 10:14:31] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:14:31 [2025-06-06 10:14:31] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:14:31 [2025-06-06 10:14:31] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:14:33 [2025-06-06 10:14:33] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:14:33 [2025-06-06 10:14:33] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:16:37 [2025-06-06 10:16:37] [信息:日志系统初始化成功]
2025/06/06 10:16:37 [2025-06-06 10:16:37] [信息:开始初始化服务...]
2025/06/06 10:16:37 [2025-06-06 10:16:37] [信息:成功加载配置文件]
2025/06/06 10:16:37 [2025-06-06 10:16:37] [信息:开始从API加载站点信息...]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:16:38 [2025-06-06 10:16:38] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:16:40 [2025-06-06 10:16:40] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:16:40 [2025-06-06 10:16:40] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:16:40 [2025-06-06 10:16:40] [信息:从API加载并更新站点信息成功]
2025/06/06 10:16:40 [2025-06-06 10:16:40] [信息:应用启动成功]
2025/06/06 10:16:41 [2025-06-06 10:16:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:16:41 [2025-06-06 10:16:41] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:30 [2025-06-06 10:20:30] [信息:GetConfig called. Current config: <nil>]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:日志系统初始化成功]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:开始初始化服务...]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:成功加载配置文件]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:开始从API加载站点信息...]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:20:31 [2025-06-06 10:20:31] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:20:33 [2025-06-06 10:20:33] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:33 [2025-06-06 10:20:33] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:20:35 [2025-06-06 10:20:35] [信息:从API加载并更新站点信息成功]
2025/06/06 10:20:35 [2025-06-06 10:20:35] [信息:应用启动成功]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:43 [2025-06-06 10:20:43] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:45 [2025-06-06 10:20:45] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:45 [2025-06-06 10:20:45] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:46 [2025-06-06 10:20:46] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:20:48 [2025-06-06 10:20:48] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:20:48 [2025-06-06 10:20:48] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:28:26 [2025-06-06 10:28:26] [信息:日志系统初始化成功]
2025/06/06 10:28:26 [2025-06-06 10:28:26] [信息:开始初始化服务...]
2025/06/06 10:28:26 [2025-06-06 10:28:26] [信息:成功加载配置文件]
2025/06/06 10:28:26 [2025-06-06 10:28:26] [信息:开始从API加载站点信息...]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [信息:从API加载并更新站点信息成功]
2025/06/06 10:28:27 [2025-06-06 10:28:27] [信息:应用启动成功]
2025/06/06 10:28:29 [2025-06-06 10:28:29] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:28:29 [2025-06-06 10:28:29] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:28:37 [2025-06-06 10:28:37] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:28:37 [2025-06-06 10:28:37] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:28:37 [2025-06-06 10:28:37] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:28:37 [2025-06-06 10:28:37] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:28:40 [2025-06-06 10:28:40] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:28:40 [2025-06-06 10:28:40] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:28:40 [2025-06-06 10:28:40] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:28:40 [2025-06-06 10:28:40] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:28:42 [2025-06-06 10:28:42] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:28:42 [2025-06-06 10:28:42] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:29:16 [2025-06-06 10:29:16] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:29:16 [2025-06-06 10:29:16] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:29:16 [2025-06-06 10:29:16] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:29:16 [2025-06-06 10:29:16] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:29:18 [2025-06-06 10:29:18] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:29:18 [2025-06-06 10:29:18] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:36 [2025-06-06 10:30:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:38 [2025-06-06 10:30:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:38 [2025-06-06 10:30:38] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:39 [2025-06-06 10:30:39] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:39 [2025-06-06 10:30:39] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:30:52 [2025-06-06 10:30:52] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:32:43 [2025-06-06 10:32:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:32:43 [2025-06-06 10:32:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:32:43 [2025-06-06 10:32:43] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:32:54 [2025-06-06 10:32:54] [操作:GetSiteInfo - 从API加载站点信息失败] [用户:系统用户] [错误:获取站点信息失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:32:54 [2025-06-06 10:32:54] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:32:54 [2025-06-06 10:32:54] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:33:05 [2025-06-06 10:33:05] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:34:46 [2025-06-06 10:34:46] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:34:46 [2025-06-06 10:34:46] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:34:46 [2025-06-06 10:34:46] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:34:47 [2025-06-06 10:34:47] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:34:47 [2025-06-06 10:34:47] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:34:47 [2025-06-06 10:34:47] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:35:01 [2025-06-06 10:35:01] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:35:59 [2025-06-06 10:35:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:42:58 [2025-06-06 10:42:58] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:42:58 [2025-06-06 10:42:58] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:42:58 [2025-06-06 10:42:58] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:43:00 [2025-06-06 10:43:00] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:43:00 [2025-06-06 10:43:00] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:43:00 [2025-06-06 10:43:00] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [信息:日志系统初始化成功]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [信息:开始初始化服务...]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [信息:成功加载配置文件]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [信息:开始从API加载站点信息...]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [操作:从API加载站点信息失败] [用户:系统] [错误:获取站点信息失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:44:58 [2025-06-06 10:44:58] [信息:应用启动成功]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:GetSiteInfo - 从API加载站点信息失败] [用户:系统用户] [错误:获取站点信息失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:GetSiteInfo - 从API加载站点信息失败] [用户:系统用户] [错误:获取站点信息失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:生成报到二维码] [用户:系统用户] [网点:]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:获取候检者列表] [用户:系统用户] [网点:]
2025/06/06 10:44:59 [2025-06-06 10:44:59] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:46:09 [2025-06-06 10:46:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:46:46 [2025-06-06 10:46:46] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:46:46 [2025-06-06 10:46:46] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:46:46 [2025-06-06 10:46:46] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:46:47 [2025-06-06 10:46:47] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:46:47 [2025-06-06 10:46:47] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:46:47 [2025-06-06 10:46:47] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:47:55 [2025-06-06 10:47:55] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:47:55 [2025-06-06 10:47:55] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:47:55 [2025-06-06 10:47:55] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:47:56 [2025-06-06 10:47:56] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:47:56 [2025-06-06 10:47:56] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:47:56 [2025-06-06 10:47:56] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:51:48 [2025-06-06 10:51:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:52:04 [2025-06-06 10:52:04] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:52:10 [2025-06-06 10:52:10] [操作:清空患者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:52:14 [2025-06-06 10:52:14] [操作:清空患者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:54:38 [2025-06-06 10:54:38] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:54:39 [2025-06-06 10:54:39] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 10:55:22 [2025-06-06 10:55:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 11:23:59 [2025-06-06 11:23:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 11:53:19 [2025-06-06 11:53:19] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 11:53:19 [2025-06-06 11:53:19] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 11:53:19 [2025-06-06 11:53:19] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 11:53:20 [2025-06-06 11:53:20] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 11:53:20 [2025-06-06 11:53:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 11:53:20 [2025-06-06 11:53:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [操作:GetSiteInfo - 从API加载站点信息失败] [用户:系统用户] [错误:获取站点信息失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:04:01 [2025-06-06 12:04:01] [操作:获取候检者列表] [用户:系统用户] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice": dial tcp: lookup env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn: no such host]
2025/06/06 12:04:29 [2025-06-06 12:04:29] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:04:29 [2025-06-06 12:04:29] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 12:04:29 [2025-06-06 12:04:29] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:04:30 [2025-06-06 12:04:30] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:04:30 [2025-06-06 12:04:30] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:04:30 [2025-06-06 12:04:30] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:06:11 [2025-06-06 12:06:11] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:06:24 [2025-06-06 12:06:24] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:06:24 [2025-06-06 12:06:24] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:06:36 [2025-06-06 12:06:36] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:06:36 [2025-06-06 12:06:36] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:06:51 [2025-06-06 12:06:51] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:07:02 [2025-06-06 12:07:02] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:07:02 [2025-06-06 12:07:02] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:07:10 [2025-06-06 12:07:10] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:07:13 [2025-06-06 12:07:13] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:07:13 [2025-06-06 12:07:13] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:07:22 [2025-06-06 12:07:22] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:07:22 [2025-06-06 12:07:22] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:07:35 [2025-06-06 12:07:35] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:07:35 [2025-06-06 12:07:35] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:28:04 [2025-06-06 12:28:04] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:28:04 [2025-06-06 12:28:04] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 12:28:04 [2025-06-06 12:28:04] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:28:05 [2025-06-06 12:28:05] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:28:05 [2025-06-06 12:28:05] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:28:05 [2025-06-06 12:28:05] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:28:14 [2025-06-06 12:28:14] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:28:28 [2025-06-06 12:28:28] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:28:28 [2025-06-06 12:28:28] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:28:40 [2025-06-06 12:28:40] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:28:40 [2025-06-06 12:28:40] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:30:55 [2025-06-06 12:30:55] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:31:09 [2025-06-06 12:31:09] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:31:09 [2025-06-06 12:31:09] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:31:21 [2025-06-06 12:31:21] [操作:上传图片失败] [用户:医生或健康专家] [错误:响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:31:21 [2025-06-06 12:31:21] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 响应中未找到图片URL，响应内容: {"code":-1,"message":"The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined","data":null}]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:日志系统初始化成功]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:开始初始化服务...]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:成功加载配置文件]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:开始从API加载站点信息...]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:从API加载并更新站点信息成功]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:快捷键服务启动成功]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:应用启动成功]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:00 [2025-06-06 12:36:00] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:33 [2025-06-06 12:36:33] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:33 [2025-06-06 12:36:33] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 12:36:33 [2025-06-06 12:36:33] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:45 [2025-06-06 12:36:45] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 12:36:45 [2025-06-06 12:36:45] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 12:36:45 [2025-06-06 12:36:45] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 12:36:45 [2025-06-06 12:36:45] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 12:36:56 [2025-06-06 12:36:56] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 12:36:56 [2025-06-06 12:36:56] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:38:43 [2025-06-06 12:38:43] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:38:44 [2025-06-06 12:38:44] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 12:39:35 [2025-06-06 12:39:35] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/06 15:40:40 [2025-06-06 15:40:40] [信息:日志系统初始化成功]
2025/06/06 15:40:40 [2025-06-06 15:40:40] [信息:开始初始化服务...]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:成功加载配置文件]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:开始从API加载站点信息...]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:从API加载并更新站点信息成功]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:快捷键服务启动成功]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:应用启动成功]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:40:41 [2025-06-06 15:40:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:40:48 [2025-06-06 15:40:48] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:40:48 [2025-06-06 15:40:48] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 15:40:48 [2025-06-06 15:40:48] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:41:01 [2025-06-06 15:41:01] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:41:01 [2025-06-06 15:41:01] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:41:01 [2025-06-06 15:41:01] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:41:01 [2025-06-06 15:41:01] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:41:11 [2025-06-06 15:41:11] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:41:11 [2025-06-06 15:41:11] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:45 [2025-06-06 15:44:45] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:49 [2025-06-06 15:44:49] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:49 [2025-06-06 15:44:49] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:44:49 [2025-06-06 15:44:49] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 15:44:49 [2025-06-06 15:44:49] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:02 [2025-06-06 15:45:02] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:45:14 [2025-06-06 15:45:14] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:14 [2025-06-06 15:45:14] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:14 [2025-06-06 15:45:14] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:45:14 [2025-06-06 15:45:14] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:44 [2025-06-06 15:46:44] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:GetConfig called. Current config: <nil>]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:日志系统初始化成功]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:开始初始化服务...]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:成功加载配置文件]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:开始从API加载站点信息...]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 15:46:48 [2025-06-06 15:46:48] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [信息:从API加载并更新站点信息成功]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [信息:快捷键服务启动成功]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [信息:应用启动成功]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:49 [2025-06-06 15:46:49] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:51 [2025-06-06 15:46:51] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 15:46:51 [2025-06-06 15:46:51] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:46:53 [2025-06-06 15:46:53] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:47:03 [2025-06-06 15:47:03] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:03 [2025-06-06 15:47:03] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:05 [2025-06-06 15:47:05] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:05 [2025-06-06 15:47:05] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:47:15 [2025-06-06 15:47:15] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:15 [2025-06-06 15:47:15] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:27 [2025-06-06 15:47:27] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:47:27 [2025-06-06 15:47:27] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 15:47:27 [2025-06-06 15:47:27] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:47:38 [2025-06-06 15:47:38] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:38 [2025-06-06 15:47:38] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 15:47:38 [2025-06-06 15:47:38] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:38 [2025-06-06 15:47:38] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:50 [2025-06-06 15:47:50] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:47:50 [2025-06-06 15:47:50] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:50:06 [2025-06-06 15:50:06] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 15:50:07 [2025-06-06 15:50:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:41 [2025-06-06 16:18:41] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:53 [2025-06-06 16:18:53] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:53 [2025-06-06 16:18:53] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:53 [2025-06-06 16:18:53] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:18:53 [2025-06-06 16:18:53] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 16:18:53 [2025-06-06 16:18:53] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:07 [2025-06-06 16:19:07] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 16:19:19 [2025-06-06 16:19:19] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:19 [2025-06-06 16:19:19] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:19 [2025-06-06 16:19:19] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:19 [2025-06-06 16:19:19] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:20 [2025-06-06 16:19:20] [操作:上传图片失败] [用户:医生或健康专家] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:19:20 [2025-06-06 16:19:20] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:21:32 [2025-06-06 16:21:32] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: true]
2025/06/06 16:21:51 [2025-06-06 16:21:51] [操作:处理截图并上传] [用户:大炜] [网点:YL-BJ-TZ-001]
2025/06/06 16:22:03 [2025-06-06 16:22:03] [操作:上传图片失败] [用户:大炜] [错误:上传失败: The first argument must be of type string or an instance of Buffer, ArrayBuffer, or Array or an Array-like Object. Received undefined]
2025/06/06 16:22:27 [2025-06-06 16:22:27] [操作:切换窗体大小] [用户:系统用户] [网点:扩展状态: false]
2025/06/06 16:43:20 [2025-06-06 16:43:20] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:日志系统初始化成功]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:开始初始化服务...]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:成功加载配置文件]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:开始从API加载站点信息...]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:从API加载并更新站点信息成功]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:快捷键服务启动成功]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:应用启动成功]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:18:12 [2025-06-06 18:18:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:21:31 [2025-06-06 18:21:31] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 18:21:31 [2025-06-06 18:21:31] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 18:21:31 [2025-06-06 18:21:31] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 18:21:46 [2025-06-06 18:21:46] [操作:调用扣子工作流失败] [用户:医生或健康专家] [错误:API调用失败，状态码: 401, 响应: {"code":700012006,"msg":"access token expired"}]
2025/06/06 18:21:46 [2025-06-06 18:21:46] [警告:扣子工作流调用失败，但图片已上传: https://env-00jxtfqc9gq1.normal.cloudstatic.cn/医生或健康专家-A01-20250606_182142.png?expire_at=1749205906&er_sign=f45635b5139b3023a4759080b8dae783]
2025/06/06 18:21:46 [2025-06-06 18:21:46] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 18:21:46 [2025-06-06 18:21:46] [操作:调用扣子工作流失败] [用户:医生或健康专家] [错误:API调用失败，状态码: 401, 响应: {"code":700012006,"msg":"access token expired"}]
2025/06/06 18:21:46 [2025-06-06 18:21:46] [警告:扣子工作流调用失败，但图片已上传: https://env-00jxtfqc9gq1.normal.cloudstatic.cn/医生或健康专家-A01-20250606_182142.png?expire_at=1749205906&er_sign=f45635b5139b3023a4759080b8dae783]
2025/06/06 18:22:00 [2025-06-06 18:22:00] [操作:调用扣子工作流失败] [用户:医生或健康专家] [错误:API调用失败，状态码: 401, 响应: {"code":700012006,"msg":"access token expired"}]
2025/06/06 18:22:00 [2025-06-06 18:22:00] [警告:扣子工作流调用失败，但图片已上传: https://env-00jxtfqc9gq1.normal.cloudstatic.cn/医生或健康专家-A01-20250606_182156.png?expire_at=1749205919&er_sign=d07ab3e36571ff942a292ce177448da4]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 18:26:02 [2025-06-06 18:26:02] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:03 [2025-06-06 18:26:03] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:GetConfig called. Current config: <nil>]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:日志系统初始化成功]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:开始初始化服务...]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:成功加载配置文件]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:开始从API加载站点信息...]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_vXb8GyQt50YCY0KnUQkoVAzcNUpTc7sQaDJSRnFdwOdmvegFUz49Ww1hg3HcgTFE WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 18:26:04 [2025-06-06 18:26:04] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [信息:从API加载并更新站点信息成功]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [信息:快捷键服务启动成功]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [信息:应用启动成功]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:05 [2025-06-06 18:26:05] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 18:26:31 [2025-06-06 18:26:31] [信息:应用开始关闭，执行清理工作...]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:日志系统初始化成功]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:开始初始化服务...]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:成功加载配置文件]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:开始从API加载站点信息...]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:从API加载并更新站点信息成功]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:快捷键服务启动成功]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:应用启动成功]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:34:47 [2025-06-06 21:34:47] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:59:11 [2025-06-06 21:59:11] [信息:日志系统初始化成功]
2025/06/06 21:59:11 [2025-06-06 21:59:11] [信息:开始初始化服务...]
2025/06/06 21:59:11 [2025-06-06 21:59:11] [信息:成功加载配置文件]
2025/06/06 21:59:11 [2025-06-06 21:59:11] [信息:开始从API加载站点信息...]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:从API加载并更新站点信息成功]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:快捷键服务启动成功]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:应用启动成功]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:59:12 [2025-06-06 21:59:12] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 21:59:34 [2025-06-06 21:59:34] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/06 21:59:34 [2025-06-06 21:59:34] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/06 22:08:41 [2025-06-06 22:08:41] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 22:08:41 [2025-06-06 22:08:41] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 22:08:41 [2025-06-06 22:08:41] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 22:08:42 [2025-06-06 22:08:42] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 22:08:42 [2025-06-06 22:08:42] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 22:08:42 [2025-06-06 22:08:42] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:日志系统初始化成功]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:开始初始化服务...]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:成功加载配置文件]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:开始从API加载站点信息...]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 23:04:26 [2025-06-06 23:04:26] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [信息:从API加载并更新站点信息成功]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [信息:快捷键服务启动成功]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [信息:应用启动成功]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:04:27 [2025-06-06 23:04:27] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:06 [2025-06-06 23:16:06] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:16:06 [2025-06-06 23:16:06] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 23:16:06 [2025-06-06 23:16:06] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:16:07 [2025-06-06 23:16:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:16:07 [2025-06-06 23:16:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:07 [2025-06-06 23:16:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:21 [2025-06-06 23:16:21] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:16:32 [2025-06-06 23:16:32] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:34:57 [2025-06-06 23:34:57] [信息:应用开始关闭，执行清理工作...]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:日志系统初始化成功]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:开始初始化服务...]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:成功加载配置文件]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:开始从API加载站点信息...]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:从API加载并更新站点信息成功]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:快捷键服务启动成功]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:应用启动成功]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:35:55 [2025-06-06 23:35:55] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:日志系统初始化成功]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:开始初始化服务...]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:成功加载配置文件]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:开始从API加载站点信息...]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID: SiteName: SiteType: ParentOrg: Location:{Province: City: District: Address:} Contact:{Manager: Phone:}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:Current SiteInfo details - Name: , ID: ]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:从API加载并更新站点信息成功]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:快捷键服务启动成功]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [信息:应用启动成功]
2025/06/06 23:44:24 [2025-06-06 23:44:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:44:25 [2025-06-06 23:44:25] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:51:32 [2025-06-06 23:51:32] [信息:应用开始关闭，执行清理工作...]
2025/06/06 23:51:32 [2025-06-06 23:51:32] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/06 23:51:32 [2025-06-06 23:51:32] [信息:快捷键服务已停止]
2025/06/06 23:51:32 [2025-06-06 23:51:32] [信息:应用清理工作完成]
2025/06/06 23:52:24 [2025-06-06 23:52:24] [信息:日志系统初始化成功]
2025/06/06 23:52:24 [2025-06-06 23:52:24] [信息:开始初始化服务...]
2025/06/06 23:52:24 [2025-06-06 23:52:24] [信息:成功加载配置文件]
2025/06/06 23:52:24 [2025-06-06 23:52:24] [信息:开始从API加载站点信息...]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:站点信息有变化，将重新生成二维码]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:快捷键服务启动成功]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:应用启动成功]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:GetConfig called. Current config: &{MpAppInfo:{AppID:wxd70aa4b433ef1843 TargetPage:pages/p_scan/p_scan} SiteInfo:{SiteID:YL-BJ-TZ-001 SiteName:北京市通州区潞城镇潞城社区卫生服务中心 SiteType:社区医院 ParentOrg: Location:{Province: City: District: Address:北京市通州区潞城镇潞城社区卫生服务中心} Contact:{Manager:李医生 Phone:13800138002}} CropSettings:{TopPercent:0 BottomPercent:0 LeftPercent:0 RightPercent:0} APIKeys:{Dcloud:{AccessKey:UROOC4IhfKDIRWGl SecretKey:TtLjMrk2pJdWG0i5 UploadURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage SpaceID:env-00jxtfqc9gq1} Coze:{Token:pat_kA4HH0FQ82yw3Losf9QJpUZFPcNpal2vI44ihBmk4yDW3BE4UIVG8Jbwlya4Vkg0 WorkflowIDPostPic:7496900622433812531 WorkflowIDPostRegistration:7501566019660939279 WorkflowIDCheckUserInfo:7501680491335614516 SpaceID:7331689003143544832 AppID:7496871719090077733} CloudFunction:{RegistrationsURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/registrations/getRegistrationsBySiteAndDevice SiteInfoByDeviceMACURL:https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/hc-actions/getSiteInfoByDeviceMAC}} DeviceInfo:{MACAddress:00:15:5d:ed:4a:58 DeviceName:} UserInfo:{Name: Gender: Birth: IDNumber:}}]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:Current SiteInfo details - Name: 北京市通州区潞城镇潞城社区卫生服务中心, ID: YL-BJ-TZ-001]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [信息:复用了缓存的报到二维码]
2025/06/06 23:52:25 [2025-06-06 23:52:25] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 00:05:38 [2025-06-07 00:05:38] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:日志系统初始化成功]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:开始初始化服务...]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:成功加载配置文件]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:开始从API加载站点信息...]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:站点信息缓存为空，从API加载]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:快捷键服务启动成功]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:应用启动成功]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [信息:复用了缓存的报到二维码]
2025/06/07 00:33:53 [2025-06-07 00:33:53] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 00:34:39 [2025-06-07 00:34:39] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 00:34:39 [2025-06-07 00:34:39] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:日志系统初始化成功]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:开始初始化服务...]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:成功加载配置文件]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:开始从API加载站点信息...]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:站点信息缓存为空，从API加载]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:快捷键服务启动成功]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:应用启动成功]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [信息:复用了缓存的报到二维码]
2025/06/07 01:06:28 [2025-06-07 01:06:28] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:08:52 [2025-06-07 01:08:52] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 01:08:52 [2025-06-07 01:08:52] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 01:10:21 [2025-06-07 01:10:21] [信息:应用开始关闭，执行清理工作...]
2025/06/07 01:10:24 [2025-06-07 01:10:24] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/07 01:10:24 [2025-06-07 01:10:24] [信息:快捷键服务已停止]
2025/06/07 01:10:24 [2025-06-07 01:10:24] [信息:应用清理工作完成]
2025/06/07 01:24:33 [2025-06-07 01:24:33] [信息:日志系统初始化成功]
2025/06/07 01:24:33 [2025-06-07 01:24:33] [信息:开始初始化服务...]
2025/06/07 01:24:33 [2025-06-07 01:24:33] [信息:成功加载配置文件]
2025/06/07 01:24:33 [2025-06-07 01:24:33] [信息:开始从API加载站点信息...]
2025/06/07 01:24:34 [2025-06-07 01:24:34] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 01:24:34 [2025-06-07 01:24:34] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 01:24:34 [2025-06-07 01:24:34] [信息:快捷键服务启动成功]
2025/06/07 01:24:34 [2025-06-07 01:24:34] [信息:应用启动成功]
2025/06/07 01:24:34 [2025-06-07 01:24:34] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [信息:使用缓存的站点信息]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [信息:重新生成了报到二维码]
2025/06/07 01:24:35 [2025-06-07 01:24:35] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:51:35 [2025-06-07 01:51:35] [信息:应用开始关闭，执行清理工作...]
2025/06/07 01:52:06 [2025-06-07 01:52:06] [信息:日志系统初始化成功]
2025/06/07 01:52:06 [2025-06-07 01:52:06] [信息:开始初始化服务...]
2025/06/07 01:52:06 [2025-06-07 01:52:06] [信息:成功加载配置文件]
2025/06/07 01:52:06 [2025-06-07 01:52:06] [信息:开始从API加载站点信息...]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:快捷键服务启动成功]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:应用启动成功]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:使用缓存的站点信息]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [信息:重新生成了报到二维码]
2025/06/07 01:52:07 [2025-06-07 01:52:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 01:52:41 [2025-06-07 01:52:41] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 01:52:41 [2025-06-07 01:52:41] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 01:53:24 [2025-06-07 01:53:24] [信息:应用开始关闭，执行清理工作...]
2025/06/07 01:54:02 [2025-06-07 01:54:02] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/07 01:54:02 [2025-06-07 01:54:02] [信息:快捷键服务已停止]
2025/06/07 01:54:02 [2025-06-07 01:54:02] [信息:应用清理工作完成]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:日志系统初始化成功]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:开始初始化服务...]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:成功加载配置文件]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:开始从API加载站点信息...]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:12:29 [2025-06-07 02:12:29] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [信息:快捷键服务启动成功]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [信息:应用启动成功]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [信息:复用了缓存的报到二维码]
2025/06/07 02:12:30 [2025-06-07 02:12:30] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:12:37 [2025-06-07 02:12:37] [信息:应用开始关闭，执行清理工作...]
2025/06/07 02:12:37 [2025-06-07 02:12:37] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/07 02:12:37 [2025-06-07 02:12:37] [信息:快捷键服务已停止]
2025/06/07 02:12:37 [2025-06-07 02:12:37] [信息:应用清理工作完成]
2025/06/07 02:12:50 [2025-06-07 02:12:50] [信息:日志系统初始化成功]
2025/06/07 02:12:50 [2025-06-07 02:12:50] [信息:开始初始化服务...]
2025/06/07 02:12:50 [2025-06-07 02:12:50] [信息:成功加载配置文件]
2025/06/07 02:12:50 [2025-06-07 02:12:50] [信息:开始从API加载站点信息...]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:站点信息无变化，复用现有二维码]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:快捷键服务启动成功]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:应用启动成功]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [信息:复用了缓存的报到二维码]
2025/06/07 02:12:51 [2025-06-07 02:12:51] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:23:21 [2025-06-07 02:23:21] [信息:应用开始关闭，执行清理工作...]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:日志系统初始化成功]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:开始初始化服务...]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:成功加载配置文件]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:开始从API加载站点信息...]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:站点信息无变化，复用现有二维码]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:快捷键服务启动成功]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:应用启动成功]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [信息:复用了缓存的报到二维码]
2025/06/07 02:23:29 [2025-06-07 02:23:29] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:27:52 [2025-06-07 02:27:52] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 02:27:52 [2025-06-07 02:27:52] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:日志系统初始化成功]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:开始初始化服务...]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:成功加载配置文件]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:开始从API加载站点信息...]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:站点信息无变化，复用现有二维码]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:快捷键服务启动成功]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:应用启动成功]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [信息:复用了缓存的报到二维码]
2025/06/07 02:36:18 [2025-06-07 02:36:18] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:37:50 [2025-06-07 02:37:50] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/07 02:42:20 [2025-06-07 02:42:20] [信息:日志系统初始化成功]
2025/06/07 02:42:20 [2025-06-07 02:42:20] [信息:开始初始化服务...]
2025/06/07 02:42:20 [2025-06-07 02:42:20] [信息:成功加载配置文件]
2025/06/07 02:42:20 [2025-06-07 02:42:20] [信息:开始从API加载站点信息...]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:快捷键服务启动成功]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:应用启动成功]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [信息:复用了缓存的报到二维码]
2025/06/07 02:42:21 [2025-06-07 02:42:21] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:42:46 [2025-06-07 02:42:46] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 02:42:46 [2025-06-07 02:42:46] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [信息:使用缓存的站点信息]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [信息:复用了缓存的报到二维码]
2025/06/07 02:49:54 [2025-06-07 02:49:54] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:日志系统初始化成功]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:开始初始化服务...]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:成功加载配置文件]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:开始从API加载站点信息...]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:49:56 [2025-06-07 02:49:56] [信息:站点信息缓存为空，从API加载]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [信息:站点信息有变化，将重新生成二维码]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [信息:快捷键服务启动成功]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [信息:应用启动成功]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [信息:复用了缓存的报到二维码]
2025/06/07 02:49:57 [2025-06-07 02:49:57] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:50:12 [2025-06-07 02:50:12] [操作:快捷键截图-模式A] [用户:医生或健康专家] [网点:]
2025/06/07 02:50:12 [2025-06-07 02:50:12] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [信息:使用缓存的站点信息]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [信息:复用了缓存的报到二维码]
2025/06/07 02:51:53 [2025-06-07 02:51:53] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/07 19:59:49 [2025-06-07 19:59:49] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/12 12:53:19 [2025-06-12 12:53:19] [信息:日志系统初始化成功]
2025/06/12 12:53:19 [2025-06-12 12:53:19] [信息:开始初始化服务...]
2025/06/12 12:53:19 [2025-06-12 12:53:19] [信息:成功加载配置文件]
2025/06/12 12:53:19 [2025-06-12 12:53:19] [信息:开始从API加载站点信息...]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:快捷键服务启动成功]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:应用启动成功]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:使用缓存的站点信息]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [信息:重新生成了报到二维码]
2025/06/12 12:53:20 [2025-06-12 12:53:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 12:53:58 [2025-06-12 12:53:58] [信息:应用开始关闭，执行清理工作...]
2025/06/12 13:40:31 [2025-06-12 13:40:31] [信息:日志系统初始化成功]
2025/06/12 13:40:31 [2025-06-12 13:40:31] [信息:开始初始化服务...]
2025/06/12 13:40:31 [2025-06-12 13:40:31] [信息:成功加载配置文件]
2025/06/12 13:40:31 [2025-06-12 13:40:31] [信息:开始从API加载站点信息...]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:快捷键服务启动成功]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:应用启动成功]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:使用缓存的站点信息]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [信息:重新生成了报到二维码]
2025/06/12 13:40:32 [2025-06-12 13:40:32] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [信息:使用缓存的站点信息]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [信息:重新生成了报到二维码]
2025/06/12 13:44:08 [2025-06-12 13:44:08] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:44:09 [2025-06-12 13:44:09] [信息:日志系统初始化成功]
2025/06/12 13:44:09 [2025-06-12 13:44:09] [信息:开始初始化服务...]
2025/06/12 13:44:09 [2025-06-12 13:44:09] [信息:成功加载配置文件]
2025/06/12 13:44:09 [2025-06-12 13:44:09] [信息:开始从API加载站点信息...]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:站点信息缓存为空，从API加载]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:站点信息无变化，复用现有二维码]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:快捷键服务启动成功]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:应用启动成功]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [信息:复用了缓存的报到二维码]
2025/06/12 13:44:10 [2025-06-12 13:44:10] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 13:58:13 [2025-06-12 13:58:13] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:01:57 [2025-06-12 14:01:57] [信息:日志系统初始化成功]
2025/06/12 14:01:57 [2025-06-12 14:01:57] [信息:开始初始化服务...]
2025/06/12 14:01:57 [2025-06-12 14:01:57] [信息:成功加载配置文件]
2025/06/12 14:01:57 [2025-06-12 14:01:57] [信息:开始从API加载站点信息...]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:快捷键服务启动成功]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:应用启动成功]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [信息:复用了缓存的报到二维码]
2025/06/12 14:01:58 [2025-06-12 14:01:58] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:06:15 [2025-06-12 14:06:15] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:06:15 [2025-06-12 14:06:15] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:06:15 [2025-06-12 14:06:15] [信息:快捷键服务已停止]
2025/06/12 14:06:15 [2025-06-12 14:06:15] [信息:应用清理工作完成]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:日志系统初始化成功]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:开始初始化服务...]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:成功加载配置文件]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:开始从API加载站点信息...]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:06:54 [2025-06-12 14:06:54] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [信息:快捷键服务启动成功]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [信息:应用启动成功]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [信息:复用了缓存的报到二维码]
2025/06/12 14:06:55 [2025-06-12 14:06:55] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:日志系统初始化成功]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:开始初始化服务...]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:成功加载配置文件]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:开始从API加载站点信息...]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:快捷键服务启动成功]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:应用启动成功]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [信息:复用了缓存的报到二维码]
2025/06/12 14:07:29 [2025-06-12 14:07:29] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:12:10 [2025-06-12 14:12:10] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:12:10 [2025-06-12 14:12:10] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:12:10 [2025-06-12 14:12:10] [信息:快捷键服务已停止]
2025/06/12 14:12:10 [2025-06-12 14:12:10] [信息:应用清理工作完成]
2025/06/12 14:12:47 [2025-06-12 14:12:47] [信息:日志系统初始化成功]
2025/06/12 14:12:47 [2025-06-12 14:12:47] [信息:开始初始化服务...]
2025/06/12 14:12:47 [2025-06-12 14:12:47] [信息:成功加载配置文件]
2025/06/12 14:12:47 [2025-06-12 14:12:47] [信息:开始从API加载站点信息...]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:快捷键服务启动成功]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:应用启动成功]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [信息:复用了缓存的报到二维码]
2025/06/12 14:12:48 [2025-06-12 14:12:48] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:14:57 [2025-06-12 14:14:57] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:14:57 [2025-06-12 14:14:57] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:14:57 [2025-06-12 14:14:57] [信息:快捷键服务已停止]
2025/06/12 14:14:57 [2025-06-12 14:14:57] [信息:应用清理工作完成]
2025/06/12 14:15:06 [2025-06-12 14:15:06] [信息:日志系统初始化成功]
2025/06/12 14:15:06 [2025-06-12 14:15:06] [信息:开始初始化服务...]
2025/06/12 14:15:06 [2025-06-12 14:15:06] [信息:成功加载配置文件]
2025/06/12 14:15:06 [2025-06-12 14:15:06] [信息:开始从API加载站点信息...]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:站点信息无变化，复用现有二维码]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:快捷键服务启动成功]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:应用启动成功]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [信息:复用了缓存的报到二维码]
2025/06/12 14:15:07 [2025-06-12 14:15:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:19:51 [2025-06-12 14:19:51] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:19:51 [2025-06-12 14:19:51] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:19:51 [2025-06-12 14:19:51] [信息:快捷键服务已停止]
2025/06/12 14:19:51 [2025-06-12 14:19:51] [信息:应用清理工作完成]
2025/06/12 14:20:01 [2025-06-12 14:20:01] [信息:日志系统初始化成功]
2025/06/12 14:20:01 [2025-06-12 14:20:01] [信息:开始初始化服务...]
2025/06/12 14:20:01 [2025-06-12 14:20:01] [信息:成功加载配置文件]
2025/06/12 14:20:01 [2025-06-12 14:20:01] [信息:开始从API加载站点信息...]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:快捷键服务启动成功]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:应用启动成功]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [信息:复用了缓存的报到二维码]
2025/06/12 14:20:02 [2025-06-12 14:20:02] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [信息:使用缓存的站点信息]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [信息:复用了缓存的报到二维码]
2025/06/12 14:28:36 [2025-06-12 14:28:36] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [信息:使用缓存的站点信息]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [信息:复用了缓存的报到二维码]
2025/06/12 14:30:06 [2025-06-12 14:30:06] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:30:23 [2025-06-12 14:30:23] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:30:23 [2025-06-12 14:30:23] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:30:23 [2025-06-12 14:30:23] [信息:快捷键服务已停止]
2025/06/12 14:30:23 [2025-06-12 14:30:23] [信息:应用清理工作完成]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:日志系统初始化成功]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:开始初始化服务...]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:成功加载配置文件]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:开始从API加载站点信息...]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:38:32 [2025-06-12 14:38:32] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [信息:快捷键服务启动成功]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [信息:应用启动成功]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [信息:复用了缓存的报到二维码]
2025/06/12 14:38:33 [2025-06-12 14:38:33] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:日志系统初始化成功]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:开始初始化服务...]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:成功加载配置文件]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:开始从API加载站点信息...]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 14:41:13 [2025-06-12 14:41:13] [信息:站点信息缓存为空，从API加载]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [信息:快捷键服务启动成功]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [信息:应用启动成功]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [信息:复用了缓存的报到二维码]
2025/06/12 14:41:14 [2025-06-12 14:41:14] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 14:41:54 [2025-06-12 14:41:54] [信息:应用开始关闭，执行清理工作...]
2025/06/12 14:41:54 [2025-06-12 14:41:54] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 14:41:54 [2025-06-12 14:41:54] [信息:快捷键服务已停止]
2025/06/12 14:41:54 [2025-06-12 14:41:54] [信息:应用清理工作完成]
2025/06/12 15:02:58 [2025-06-12 15:02:58] [信息:日志系统初始化成功]
2025/06/12 15:02:58 [2025-06-12 15:02:58] [信息:开始初始化服务...]
2025/06/12 15:02:58 [2025-06-12 15:02:58] [信息:成功加载配置文件]
2025/06/12 15:02:58 [2025-06-12 15:02:58] [信息:开始从API加载站点信息...]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:站点信息缓存为空，从API加载]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:快捷键服务启动成功]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:应用启动成功]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [信息:复用了缓存的报到二维码]
2025/06/12 15:02:59 [2025-06-12 15:02:59] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:09:18 [2025-06-12 15:09:18] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:日志系统初始化成功]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:开始初始化服务...]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:成功加载配置文件]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:开始从API加载站点信息...]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:站点信息缓存为空，从API加载]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:快捷键服务启动成功]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:应用启动成功]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [信息:复用了缓存的报到二维码]
2025/06/12 15:41:24 [2025-06-12 15:41:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:50:22 [2025-06-12 15:50:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:50:23 [2025-06-12 15:50:23] [操作:获取候检者列表] [用户:系统用户] [错误:解析响应失败: json: cannot unmarshal number into Go struct field UserInfo.data.userInfo.gender of type string]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:日志系统初始化成功]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:开始初始化服务...]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:成功加载配置文件]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:开始从API加载站点信息...]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 15:54:10 [2025-06-12 15:54:10] [信息:站点信息缓存为空，从API加载]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [信息:快捷键服务启动成功]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [信息:应用启动成功]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [信息:复用了缓存的报到二维码]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 15:54:11 [2025-06-12 15:54:11] [操作:获取候检者列表] [用户:系统用户] [错误:解析响应失败: json: cannot unmarshal number into Go struct field UserInfo.data.userInfo.gender of type string]
2025/06/12 16:05:37 [2025-06-12 16:05:37] [信息:应用开始关闭，执行清理工作...]
2025/06/12 16:06:30 [2025-06-12 16:06:30] [信息:日志系统初始化成功]
2025/06/12 16:06:30 [2025-06-12 16:06:30] [信息:开始初始化服务...]
2025/06/12 16:06:30 [2025-06-12 16:06:30] [信息:成功加载配置文件]
2025/06/12 16:06:30 [2025-06-12 16:06:30] [信息:开始从API加载站点信息...]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:站点信息缓存为空，从API加载]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:站点信息无变化，复用现有二维码]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:快捷键服务启动成功]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:应用启动成功]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [信息:复用了缓存的报到二维码]
2025/06/12 16:06:31 [2025-06-12 16:06:31] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [信息:使用缓存的站点信息]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [信息:复用了缓存的报到二维码]
2025/06/12 16:08:46 [2025-06-12 16:08:46] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [信息:使用缓存的站点信息]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [信息:复用了缓存的报到二维码]
2025/06/12 16:23:28 [2025-06-12 16:23:28] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:23:47 [2025-06-12 16:23:47] [信息:应用开始关闭，执行清理工作...]
2025/06/12 16:23:47 [2025-06-12 16:23:47] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 16:23:47 [2025-06-12 16:23:47] [信息:快捷键服务已停止]
2025/06/12 16:23:47 [2025-06-12 16:23:47] [信息:应用清理工作完成]
2025/06/12 16:23:56 [2025-06-12 16:23:56] [信息:日志系统初始化成功]
2025/06/12 16:23:56 [2025-06-12 16:23:56] [信息:开始初始化服务...]
2025/06/12 16:23:56 [2025-06-12 16:23:56] [信息:成功加载配置文件]
2025/06/12 16:23:56 [2025-06-12 16:23:56] [信息:开始从API加载站点信息...]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:站点信息缓存为空，从API加载]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:快捷键服务启动成功]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:应用启动成功]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [信息:复用了缓存的报到二维码]
2025/06/12 16:23:57 [2025-06-12 16:23:57] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [信息:使用缓存的站点信息]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [信息:复用了缓存的报到二维码]
2025/06/12 16:28:32 [2025-06-12 16:28:32] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:28:50 [2025-06-12 16:28:50] [信息:应用开始关闭，执行清理工作...]
2025/06/12 16:28:50 [2025-06-12 16:28:50] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 16:28:50 [2025-06-12 16:28:50] [信息:快捷键服务已停止]
2025/06/12 16:28:50 [2025-06-12 16:28:50] [信息:应用清理工作完成]
2025/06/12 16:29:06 [2025-06-12 16:29:06] [信息:日志系统初始化成功]
2025/06/12 16:29:06 [2025-06-12 16:29:06] [信息:开始初始化服务...]
2025/06/12 16:29:06 [2025-06-12 16:29:06] [信息:成功加载配置文件]
2025/06/12 16:29:06 [2025-06-12 16:29:06] [信息:开始从API加载站点信息...]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:站点信息缓存为空，从API加载]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:快捷键服务启动成功]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:应用启动成功]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [信息:复用了缓存的报到二维码]
2025/06/12 16:29:07 [2025-06-12 16:29:07] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:32:58 [2025-06-12 16:32:58] [信息:应用开始关闭，执行清理工作...]
2025/06/12 16:32:58 [2025-06-12 16:32:58] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/12 16:32:58 [2025-06-12 16:32:58] [信息:快捷键服务已停止]
2025/06/12 16:32:58 [2025-06-12 16:32:58] [信息:应用清理工作完成]
2025/06/12 16:33:08 [2025-06-12 16:33:08] [信息:日志系统初始化成功]
2025/06/12 16:33:08 [2025-06-12 16:33:08] [信息:开始初始化服务...]
2025/06/12 16:33:08 [2025-06-12 16:33:08] [信息:成功加载配置文件]
2025/06/12 16:33:08 [2025-06-12 16:33:08] [信息:开始从API加载站点信息...]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:站点信息缓存为空，从API加载]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:快捷键服务启动成功]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:应用启动成功]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [信息:复用了缓存的报到二维码]
2025/06/12 16:33:09 [2025-06-12 16:33:09] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [信息:使用缓存的站点信息]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [信息:复用了缓存的报到二维码]
2025/06/12 16:34:20 [2025-06-12 16:34:20] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:日志系统初始化成功]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:开始初始化服务...]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:成功加载配置文件]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:开始从API加载站点信息...]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:快捷键服务启动成功]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:应用启动成功]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:使用缓存的站点信息]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [信息:重新生成了报到二维码]
2025/06/12 16:34:22 [2025-06-12 16:34:22] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [信息:使用缓存的站点信息]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [信息:重新生成了报到二维码]
2025/06/12 16:34:23 [2025-06-12 16:34:23] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:日志系统初始化成功]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:开始初始化服务...]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:成功加载配置文件]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:开始从API加载站点信息...]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:站点信息缓存为空，从API加载]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:站点信息无变化，复用现有二维码]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:快捷键服务启动成功]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:应用启动成功]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [信息:复用了缓存的报到二维码]
2025/06/12 16:34:24 [2025-06-12 16:34:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [信息:使用缓存的站点信息]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [信息:复用了缓存的报到二维码]
2025/06/12 17:49:11 [2025-06-12 17:49:11] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 22:05:57 [2025-06-12 22:05:57] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/12 22:15:17 [2025-06-12 22:15:17] [信息:日志系统初始化成功]
2025/06/12 22:15:17 [2025-06-12 22:15:17] [信息:开始初始化服务...]
2025/06/12 22:15:17 [2025-06-12 22:15:17] [信息:成功加载配置文件]
2025/06/12 22:15:17 [2025-06-12 22:15:17] [信息:开始从API加载站点信息...]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:快捷键服务启动成功]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:应用启动成功]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:使用缓存的站点信息]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [信息:重新生成了报到二维码]
2025/06/12 22:15:18 [2025-06-12 22:15:18] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 22:15:45 [2025-06-12 22:15:45] [操作:快捷键截图-模式B] [用户:医生或健康专家] [网点:]
2025/06/12 22:15:45 [2025-06-12 22:15:45] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [信息:使用缓存的站点信息]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [信息:重新生成了报到二维码]
2025/06/12 22:23:18 [2025-06-12 22:23:18] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:日志系统初始化成功]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:开始初始化服务...]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:成功加载配置文件]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:开始从API加载站点信息...]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 23:08:33 [2025-06-12 23:08:33] [信息:站点信息缓存为空，从API加载]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [信息:站点信息有变化，将重新生成二维码]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [信息:快捷键服务启动成功]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [信息:应用启动成功]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [信息:复用了缓存的报到二维码]
2025/06/12 23:08:34 [2025-06-12 23:08:34] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [信息:GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [信息:使用缓存的站点信息]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [信息:复用了缓存的报到二维码]
2025/06/12 23:08:50 [2025-06-12 23:08:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/13 10:08:45 [2025-06-13 10:08:45] [操作:检测到退出快捷键] [用户:医生或健康专家] [网点:]
2025/06/16 17:56:42 [2025-06-16 17:56:42] [信息:日志系统初始化成功]
2025/06/16 17:56:42 [2025-06-16 17:56:42] [信息:开始初始化服务...]
2025/06/16 17:56:42 [2025-06-16 17:56:42] [信息:成功加载配置文件]
2025/06/16 17:56:42 [2025-06-16 17:56:42] [信息:开始从API加载站点信息...]
2025/06/16 17:56:43 [2025-06-16 17:56:43] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/16 17:56:43 [2025-06-16 17:56:43] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/16 17:56:43 [2025-06-16 17:56:43] [信息:站点信息缓存为空，从API加载]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [信息:重新生成了报到二维码]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [信息:站点信息无变化，复用现有二维码]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [信息:快捷键服务启动成功]
2025/06/16 17:56:46 [2025-06-16 17:56:46] [信息:应用启动成功]
2025/06/16 17:57:24 [2025-06-16 17:57:24] [操作:快捷键截图-模式B] [用户:医生或健康专家] [网点:]
2025/06/16 17:57:26 [2025-06-16 17:57:26] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/16 17:57:53 [2025-06-16 17:57:53] [操作:更新截图记录失败] [用户:医生或健康专家] [错误:云函数返回错误: this.createScreenshotEntry is not a function]
2025/06/16 17:58:35 [2025-06-16 17:58:35] [操作:快捷键截图-模式B] [用户:医生或健康专家] [网点:]
2025/06/16 17:58:36 [2025-06-16 17:58:36] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/16 17:59:14 [2025-06-16 17:59:14] [操作:更新截图记录失败] [用户:医生或健康专家] [错误:云函数返回错误: this.createScreenshotEntry is not a function]
2025/06/16 18:00:26 [2025-06-16 18:00:26] [信息:应用开始关闭，执行清理工作...]
2025/06/16 18:00:31 [2025-06-16 18:00:31] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/16 18:00:31 [2025-06-16 18:00:31] [信息:快捷键服务已停止]
2025/06/16 18:00:31 [2025-06-16 18:00:31] [信息:OCR服务已关闭]
2025/06/16 18:00:31 [2025-06-16 18:00:31] [信息:应用清理工作完成]
2025/06/16 18:00:46 [2025-06-16 18:00:46] [信息:日志系统初始化成功]
2025/06/16 18:00:46 [2025-06-16 18:00:46] [信息:开始初始化服务...]
2025/06/16 18:00:46 [2025-06-16 18:00:46] [信息:成功加载配置文件]
2025/06/16 18:00:46 [2025-06-16 18:00:46] [信息:开始从API加载站点信息...]
2025/06/16 18:00:47 [2025-06-16 18:00:47] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/16 18:00:47 [2025-06-16 18:00:47] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/16 18:00:47 [2025-06-16 18:00:47] [信息:站点信息缓存为空，从API加载]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [信息:站点信息有变化，将重新生成二维码]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [信息:快捷键服务启动成功]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [信息:应用启动成功]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [信息:复用了缓存的报到二维码]
2025/06/16 18:00:50 [2025-06-16 18:00:50] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 18:01:00 [2025-06-16 18:01:00] [操作:快捷键截图-模式B] [用户:医生或健康专家] [网点:]
2025/06/16 18:01:01 [2025-06-16 18:01:01] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/16 18:01:38 [2025-06-16 18:01:38] [操作:更新截图记录失败] [用户:医生或健康专家] [错误:云函数返回错误: this.createScreenshotEntry is not a function]
2025/06/16 18:03:02 [2025-06-16 18:03:02] [操作:快捷键截图-模式C] [用户:医生或健康专家] [网点:]
2025/06/16 18:03:03 [2025-06-16 18:03:03] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/16 18:03:43 [2025-06-16 18:03:43] [操作:上传图片失败] [用户:医生或健康专家] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage?fileName=%E5%8C%BB%E7%94%9F%E6%88%96%E5%81%A5%E5%BA%B7%E4%B8%93%E5%AE%B6_%E6%9C%AA%E7%9F%A5%E5%99%A8%E5%AE%98_RPT20250616001_R01_C03.png&siteId=YL-BJ-TZ-001": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/16 18:03:43 [2025-06-16 18:03:43] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage?fileName=%E5%8C%BB%E7%94%9F%E6%88%96%E5%81%A5%E5%BA%B7%E4%B8%93%E5%AE%B6_%E6%9C%AA%E7%9F%A5%E5%99%A8%E5%AE%98_RPT20250616001_R01_C03.png&siteId=YL-BJ-TZ-001": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/16 18:04:34 [2025-06-16 18:04:34] [操作:快捷键截图-模式B] [用户:医生或健康专家] [网点:]
2025/06/16 18:04:35 [2025-06-16 18:04:35] [操作:处理截图并上传] [用户:医生或健康专家] [网点:YL-BJ-TZ-001]
2025/06/16 18:05:15 [2025-06-16 18:05:15] [操作:上传图片失败] [用户:医生或健康专家] [错误:发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage?fileName=%E5%8C%BB%E7%94%9F%E6%88%96%E5%81%A5%E5%BA%B7%E4%B8%93%E5%AE%B6_%E6%9C%AA%E7%9F%A5%E5%99%A8%E5%AE%98_RPT20250616001_R01_B02.png&siteId=YL-BJ-TZ-001": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/16 18:05:15 [2025-06-16 18:05:15] [操作:快捷键截图失败] [用户:医生或健康专家] [错误:上传图片失败: 发送请求失败: Post "https://env-00jxtfqc9gq1.dev-hz.cloudbasefunction.cn/uploadImage?fileName=%E5%8C%BB%E7%94%9F%E6%88%96%E5%81%A5%E5%BA%B7%E4%B8%93%E5%AE%B6_%E6%9C%AA%E7%9F%A5%E5%99%A8%E5%AE%98_RPT20250616001_R01_B02.png&siteId=YL-BJ-TZ-001": context deadline exceeded (Client.Timeout exceeded while awaiting headers)]
2025/06/16 18:10:19 [2025-06-16 18:10:19] [信息:日志系统初始化成功]
2025/06/16 18:10:19 [2025-06-16 18:10:19] [信息:开始初始化服务...]
2025/06/16 18:10:19 [2025-06-16 18:10:19] [信息:成功加载配置文件]
2025/06/16 18:10:19 [2025-06-16 18:10:19] [信息:开始从API加载站点信息...]
2025/06/16 18:10:20 [2025-06-16 18:10:20] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/16 18:10:20 [2025-06-16 18:10:20] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/16 18:10:20 [2025-06-16 18:10:20] [信息:站点信息缓存为空，从API加载]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [信息:重新生成了报到二维码]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [信息:站点信息无变化，复用现有二维码]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [信息:快捷键服务启动成功]
2025/06/16 18:10:24 [2025-06-16 18:10:24] [信息:应用启动成功]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:日志系统初始化成功]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:开始初始化服务...]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:成功加载配置文件]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:开始从API加载站点信息...]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:GetConfig called - SiteInfo not loaded yet]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:GetSiteInfo called - 开始获取站点信息]
2025/06/16 21:26:11 [2025-06-16 21:26:11] [信息:站点信息缓存为空，从API加载]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [信息:站点信息有变化，将重新生成二维码]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [信息:GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [操作:启动快捷键监听] [用户:系统] [网点:]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [信息:快捷键服务启动成功]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [信息:应用启动成功]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [操作:生成报到二维码] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [信息:重新生成了报到二维码]
2025/06/16 21:26:15 [2025-06-16 21:26:15] [操作:获取候检者列表] [用户:系统用户] [网点:YL-BJ-TZ-001]
2025/06/16 21:27:13 [2025-06-16 21:27:13] [信息:应用开始关闭，执行清理工作...]
2025/06/16 21:27:13 [2025-06-16 21:27:13] [操作:停止快捷键监听] [用户:系统] [网点:]
2025/06/16 21:27:13 [2025-06-16 21:27:13] [信息:快捷键服务已停止]
2025/06/16 21:27:13 [2025-06-16 21:27:13] [信息:OCR服务已关闭]
2025/06/16 21:27:13 [2025-06-16 21:27:13] [信息:应用清理工作完成]
