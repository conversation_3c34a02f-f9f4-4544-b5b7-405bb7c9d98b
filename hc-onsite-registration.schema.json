// 患者现场登记(注册)和挂号表
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": true,
		"update": true,
		"delete": true
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"user_id": {
			"bsonType": "string",
			"description": "受检人userID，参考`uni-id-users`",
			"foreignKey": "uni-id-users._id",
			"defaultValue": {
				"$env": "uid"
			}
		},
		"device_id": {
			"bsonType": "string",
			"description": "设备，磁感检测设备表doc id，参考hc-device-info表",
			"foreignKey": "hc-device-info._id"
		},
		"check_institution_id": {
			"bsonType": "string",
			"description": "网点，doc id，参考hc-check-organization表",
			"foreignKey": "hc-check-organization._id"
		},
		"site_id": {
			"bsonType": "string",
			"description": "网点ID"
		},
		"device_no": {
			"bsonType": "string",
			"description": "网点磁感设备编号（MAC地址）"
		},
		"registration_number": {
			"bsonType": "string",
			"description": "完整的长规则挂号号码，由桌面截图程序生成"
		},
		"short_number": {
			"bsonType": "string",
			"description": "短数字挂号号码，由小程序端输入"
		},
		"user_confirmed": {
			"bsonType": "bool",
			"description": "受检人已经完成手机端挂号码输入"
		},
		"registration_time": {
			"bsonType": "timestamp",
			"description": "挂号时间"
		},
		"registration_date": {
			"bsonType": "string",
			"description": "挂号日期"
		}
	},
	"version": "0.0.1"
}