@echo off
echo ===== Python环境修复指南 =====
echo.

echo 检测当前Python版本...
python --version
python -c "import platform; print('架构:', platform.architecture()[0])"

echo.
echo 问题分析:
echo 您当前使用的是32位Python，但PaddleOCR需要64位Python
echo.

echo 解决方案:
echo 1. 下载64位Python 3.9-3.11 (推荐3.10)
echo    下载地址: https://www.python.org/downloads/windows/
echo    选择: Windows installer (64-bit)
echo.
echo 2. 安装时选择:
echo    - Add Python to PATH
echo    - Install for all users
echo.
echo 3. 安装完成后重新运行此脚本验证

echo.
echo 或者使用备选方案...
echo.
pause
