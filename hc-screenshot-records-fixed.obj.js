// 云对象：hc-screenshot-records (修复版本)
// 用于管理检测截图记录
// 修复了 createScreenshotEntry 方法调用问题

const db = uniCloud.database();
const uniID = require('uni-id-common');

module.exports = {
    _before: async function() {
        const clientInfo = this.getClientInfo();
        this.uniID = uniID.createInstance({
            clientInfo
        });

        // 对于截图记录相关方法，跳过token验证
        const skipTokenMethods = [
            'createOrUpdateScreenshotRecord',
            'getSessionsByUser'
        ];

        if (skipTokenMethods.includes(this.getMethodName())) {
            return;
        }

        // 用户信息验证
        this.userInfo = await this.uniID.checkToken(clientInfo.uniIdToken);
        if (this.userInfo.code && this.userInfo.code > 0) {
            throw this.userInfo
        }
    },

    /**
     * 创建或更新截图记录
     */
    async createOrUpdateScreenshotRecord(params = {}) {
        console.log('[hc-screenshot-records][createOrUpdateScreenshotRecord] 收到参数:', JSON.stringify(params));

        try {
            // 处理HTTP请求参数
            let actualParams = params;
            try {
                const httpInfo = this.getHttpInfo();
                if (httpInfo && httpInfo.body) {
                    const bodyData = JSON.parse(httpInfo.body);
                    actualParams = { ...params, ...bodyData };
                    console.log('[createOrUpdateScreenshotRecord] HTTP请求参数:', actualParams);
                }
            } catch (httpError) {
                console.log('[createOrUpdateScreenshotRecord] 使用原始参数:', params);
            }

            const {
                user_name,
                user_id,
                site_id,
                device_no,
                registration_id,
                round,
                analysis_type,
                detected_organ,
                filename,
                cloud_url,
                operator_name,
                ocr_success
            } = actualParams;

            // 参数验证
            if (!user_name || !analysis_type || !filename) {
                return {
                    errCode: "PARAM_ERROR",
                    errMsg: "缺少必要参数: user_name, analysis_type, filename"
                };
            }

            // 生成session_id和report_number
            const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            const session_id = `SES_${user_name}_${today}`;
            const report_number = `RPT${today}001`;

            // 查询现有记录
            const existingRecord = await db.collection('hc-detection-screenshot-records')
                .where({ session_id: session_id })
                .get();

            const currentTime = new Date();
            
            if (existingRecord.data.length === 0) {
                // 创建新记录
                console.log('[createOrUpdateScreenshotRecord] 创建新的截图记录');
                
                // 直接创建截图条目，避免this调用问题
                const screenshotEntry = this._createScreenshotEntry(
                    round || 1, analysis_type, detected_organ, filename, 
                    cloud_url, currentTime, operator_name, ocr_success
                );
                
                const newRecord = {
                    session_id: session_id,
                    user_id: user_id || "unknown_user",
                    user_name: user_name,
                    site_id: site_id || "",
                    device_no: device_no || "",
                    registration_id: registration_id || "",
                    report_number: report_number,
                    current_round: round || 1,
                    total_rounds: 10,
                    screenshots: [screenshotEntry],
                    session_status: "CREATED",
                    operator_name: operator_name || "系统操作员",
                    session_start_time: currentTime,
                    created_time: currentTime,
                    updated_time: currentTime
                };

                const result = await db.collection('hc-detection-screenshot-records').add(newRecord);

                if (result.id) {
                    return {
                        errCode: "0",
                        errMsg: "截图记录创建成功",
                        data: {
                            action: "created",
                            session_id: session_id,
                            record_id: result.id,
                            current_round: round || 1
                        }
                    };
                } else {
                    return {
                        errCode: 'ADD_FAILED',
                        errMsg: '截图记录创建失败，未创建任何记录',
                        result: result
                    };
                }
                
            } else {
                // 更新现有记录
                console.log('[createOrUpdateScreenshotRecord] 更新现有截图记录');
                
                const record = existingRecord.data[0];
                const screenshots = record.screenshots || [];
                
                // 查找当前轮次的截图记录
                let currentRoundScreenshot = screenshots.find(s => s.round === (round || 1));
                
                if (!currentRoundScreenshot) {
                    // 添加新轮次的截图记录
                    currentRoundScreenshot = this._createScreenshotEntry(
                        round || 1, analysis_type, detected_organ, filename, 
                        cloud_url, currentTime, operator_name, ocr_success
                    );
                    screenshots.push(currentRoundScreenshot);
                } else {
                    // 更新现有轮次的截图记录
                    this._updateScreenshotEntry(
                        currentRoundScreenshot, analysis_type, detected_organ, 
                        filename, cloud_url, currentTime, operator_name, ocr_success
                    );
                }

                // 计算会话状态
                const newStatus = this._calculateSessionStatus(round || 1, analysis_type);
                const newCurrentRound = Math.max(record.current_round || 1, round || 1);

                // 更新记录
                const updateData = {
                    screenshots: screenshots,
                    current_round: newCurrentRound,
                    session_status: newStatus,
                    updated_time: currentTime
                };

                // 如果是最后一轮，设置结束时间
                if (newStatus === "COMPLETED") {
                    updateData.session_end_time = currentTime;
                }

                const updateResult = await db.collection('hc-detection-screenshot-records')
                    .doc(record._id).update(updateData);

                return {
                    errCode: "0",
                    errMsg: "截图记录更新成功",
                    data: {
                        action: "updated",
                        session_id: session_id,
                        record_id: record._id,
                        current_round: newCurrentRound,
                        session_status: newStatus,
                        affectedDocs: updateResult.affectedDocs
                    }
                };
            }
            
        } catch (e) {
            console.error('[hc-screenshot-records][createOrUpdateScreenshotRecord] 处理截图记录时出错:', e);

            let errMsg = '数据库操作失败。';
            if (e.message) {
                errMsg = e.message;
            }
            if (e.code) {
                errMsg = `数据库错误: ${e.code} - ${e.message}`;
            }

            return {
                errCode: String(e.code) || 'DATABASE_ERROR',
                errMsg: errMsg
            };
        }
    },

    /**
     * 查询用户检测记录
     */
    async getSessionsByUser(params = {}) {
        console.log('[hc-screenshot-records][getSessionsByUser] 收到参数:', JSON.stringify(params));

        // 处理HTTP请求参数
        let actualParams = params;
        try {
            const httpInfo = this.getHttpInfo();
            if (httpInfo && httpInfo.body) {
                const bodyData = JSON.parse(httpInfo.body);
                actualParams = { ...params, ...bodyData };
                console.log('[getSessionsByUser] HTTP请求参数:', actualParams);
            }
        } catch (httpError) {
            console.log('[getSessionsByUser] 使用原始参数:', params);
        }

        const { user_id, user_name, site_id, device_no, date, page = 1, pageSize = 20 } = actualParams;
        const offset = (page - 1) * pageSize;

        try {
            // 构建查询条件
            let queryCondition = {};

            if (user_id) queryCondition.user_id = user_id;
            if (user_name) queryCondition.user_name = user_name;
            if (site_id) queryCondition.site_id = site_id;
            if (device_no) queryCondition.device_no = device_no;

            // 如果指定了日期，添加日期过滤
            if (date) {
                const sessionIdPattern = `SES_.*_${date.replace(/-/g, '')}`;
                queryCondition.session_id = new RegExp(sessionIdPattern);
            }

            // 执行查询
            const result = await db.collection('hc-detection-screenshot-records')
                .where(queryCondition)
                .orderBy('created_time', 'desc')
                .skip(offset)
                .limit(pageSize)
                .get();

            // 统计总数
            const countResult = await db.collection('hc-detection-screenshot-records')
                .where(queryCondition)
                .count();

            return {
                errCode: "0",
                errMsg: "查询成功",
                data: result.data,
                total: countResult.total,
                queryCondition: queryCondition
            };
            
        } catch (e) {
            console.error('[hc-screenshot-records][getSessionsByUser] 查询检测记录时出错:', e);

            let errMsg = '数据库操作失败。';
            if (e.message) {
                errMsg = e.message;
            }
            if (e.code) {
                errMsg = `数据库错误: ${e.code} - ${e.message}`;
            }

            return {
                errCode: String(e.code) || 'DATABASE_ERROR',
                errMsg: errMsg
            };
        }
    },
