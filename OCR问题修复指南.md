# OCR问题修复指南

## 🚨 问题描述
当前应用显示以下错误：
```
OCR识别失败: OCR文字提取失败: OCR功能未启用：请安装Tesseract-OCR
```

## 🔍 问题原因
应用当前使用的是**模拟OCR服务**，而不是真实的OCR服务。这是因为：

1. **构建时未使用OCR标签**：应用需要使用 `-tags ocr` 构建标签才能启用真实的OCR功能
2. **Tesseract-OCR可能未安装**：即使使用了正确的构建标签，如果系统未安装Tesseract-OCR，也会回退到模拟服务

## ✅ 解决方案

### 步骤1: 安装Tesseract-OCR
1. 访问 https://github.com/UB-Mannheim/tesseract/wiki
2. 下载Windows版本安装包
3. **重要**：安装时选择中文语言包 (chi_sim)
4. 将安装目录添加到系统PATH环境变量
5. 重启命令提示符测试：`tesseract --version`

### 步骤2: 重新构建应用（关键步骤）

#### 方法1: 使用自动化脚本（推荐）
```bash
# 运行OCR设置脚本，会自动检查环境并构建
setup_ocr.bat
```

#### 方法2: 手动构建
```bash
# 使用包含OCR功能的构建脚本
build.bat

# 或手动使用wails命令
wails build -tags ocr
```

### 步骤3: 验证修复
1. 启动新构建的应用
2. 尝试截图功能
3. 检查日志，应该看到 "Tesseract版本: ..." 而不是 "OCR功能未启用"

## 📋 快速检查清单

- [ ] Tesseract-OCR已安装
- [ ] 中文语言包已安装
- [ ] Tesseract已添加到PATH环境变量
- [ ] 使用 `-tags ocr` 重新构建了应用
- [ ] 测试OCR功能正常工作

## 🔧 提供的工具

### 新增脚本文件
1. **setup_ocr.bat** - 自动检查OCR环境并构建应用
2. **build.bat** - 更新为包含OCR功能的构建脚本
3. **build_no_ocr.bat** - 备用的无OCR构建脚本

### 测试方法
```bash
# 进入测试目录
cd ocr_tests

# 运行OCR测试（需要使用OCR标签）
go run -tags ocr test_ocr.go
```

## 🚀 预期结果
修复后，应用日志应显示：
```
[DEBUG] 步骤1.1成功: 识别到器官名称 - [实际器官名称]
```
而不是：
```
OCR识别失败: OCR文字提取失败: OCR功能未启用：请安装Tesseract-OCR
```

## 📞 如果仍有问题
1. 检查 `tesseract --version` 命令是否正常工作
2. 确认使用了正确的构建命令
3. 查看应用启动时的控制台输出
4. 运行 `setup_ocr.bat` 进行完整的环境检查
