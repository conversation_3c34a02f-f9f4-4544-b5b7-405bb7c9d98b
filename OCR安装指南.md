# OCR功能安装指南

## 概述
本项目已集成gosseract OCR库，用于从截图中识别文字和提取器官部位信息。

## 安装要求

### Windows系统安装Tesseract-OCR

#### 1. 下载Tesseract-OCR
- 访问官方下载页面：https://github.com/UB-Mannheim/tesseract/wiki
- 下载最新版本的Windows安装包（推荐64位版本）

#### 2. 安装步骤
1. 运行下载的安装程序
2. **重要**：在安装过程中，确保选择以下语言包：
   - English (默认已选)
   - Chinese - Simplified (chi_sim) - **必须选择**
   - Chinese - Traditional (chi_tra) - 可选
3. 记住安装路径（通常是 `C:\Program Files\Tesseract-OCR`）

#### 3. 配置环境变量
1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"系统变量"中找到"Path"，点击"编辑"
3. 点击"新建"，添加Tesseract安装路径：
   ```
   C:\Program Files\Tesseract-OCR
   ```
4. 点击"确定"保存所有设置
5. **重启命令提示符或重启电脑**使环境变量生效

#### 4. 验证安装
打开命令提示符，输入：
```cmd
tesseract --version
```
如果显示版本信息，说明安装成功。

### macOS系统安装

```bash
# 使用Homebrew安装
brew install tesseract

# 安装中文语言包
brew install tesseract-lang
```

### Linux系统安装

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra

# CentOS/RHEL
sudo yum install tesseract tesseract-langpack-chi_sim tesseract-langpack-chi_tra
```

## 🚀 构建和启用OCR功能

### 重要说明
**OCR功能需要使用特定的构建标签才能启用**。如果不使用正确的构建方式，应用将使用模拟OCR服务。

### 构建方法

#### 方法1: 使用提供的脚本（推荐）
```bash
# 运行OCR设置和测试脚本
setup_ocr.bat

# 或直接构建包含OCR功能的版本
build.bat
```

#### 方法2: 手动构建
```bash
# 使用Wails构建（推荐）
wails build -tags ocr

# 或使用Go构建（开发测试）
go build -tags ocr -o MagneticOperator.exe
```

#### 方法3: 无OCR版本（备用）
```bash
# 如果不需要OCR功能或Tesseract未安装
build_no_ocr.bat
```

## 功能测试

### 1. 运行OCR测试程序
```bash
cd ocr_tests
go run -tags ocr test_ocr.go
```

### 2. 在应用中测试OCR
1. 启动应用程序（确保使用包含OCR的构建版本）
2. 在前端调用OCR相关API：
   - `TestOCR(imagePath)` - 测试OCR功能
   - `ExtractOrganFromScreenshot(imagePath)` - 提取器官名称
   - `ValidateOCRSetup()` - 验证OCR环境

## OCR功能特性

### 支持的功能
- ✅ 中英文混合识别
- ✅ 器官部位名称提取
- ✅ 置信度评估
- ✅ 处理时间统计
- ✅ 环境验证

### 支持的器官关键词
- 内脏器官：心脏、肝脏、肺、肾脏、脾脏、胃、肠、胆囊、胰腺
- 内分泌：甲状腺、前列腺
- 生殖系统：子宫、卵巢、乳腺
- 其他：膀胱、食管、十二指肠等
- 部位：头部、颈部、胸部、腹部、盆腔等

## 故障排除

### 常见问题

#### 0. "OCR功能未启用：请安装Tesseract-OCR"（最常见）
**原因**：应用使用了模拟OCR服务，而不是真实的OCR服务
**解决方案**：
- **检查构建方式**：确保使用了 `-tags ocr` 构建标签
- 使用 `build.bat` 而不是 `build_no_ocr.bat`
- 或运行 `setup_ocr.bat` 进行完整设置
- 重新构建应用：`wails build -tags ocr`

#### 1. "OCR环境验证失败"
**原因**：Tesseract未正确安装或环境变量未配置
**解决方案**：
- 重新安装Tesseract-OCR
- 检查环境变量PATH设置
- 重启命令提示符或电脑

#### 2. "无法获取Tesseract版本信息"
**原因**：Tesseract可执行文件不在PATH中
**解决方案**：
- 确认Tesseract安装路径
- 手动添加到系统PATH环境变量
- 在命令行测试 `tesseract --version`

#### 3. "识别效果不佳"
**原因**：图片质量或语言包问题
**解决方案**：
- 确保安装了中文语言包（chi_sim）
- 检查图片清晰度和对比度
- 尝试图像预处理（已在代码中实现）

#### 4. "处理速度慢"
**原因**：图片过大或OCR配置不当
**解决方案**：
- 图片会自动裁剪和预处理
- OCR已配置为LSTM模式以提高速度
- 考虑升级硬件配置

### 调试信息
启用详细日志查看OCR处理过程：
```go
// 在OCR服务中已包含详细的错误信息和处理时间统计
```

## 下一步计划
1. ✅ 集成OCR库（已完成）
2. 🔄 创建数据库表结构（下一步）
3. 🔄 实现检测会话管理
4. 🔄 优化文件命名逻辑
5. 🔄 实现10轮自动化截图流程

## 技术支持
如遇到问题，请检查：
1. Tesseract-OCR是否正确安装
2. 中文语言包是否已安装
3. 环境变量PATH是否正确配置
4. 测试图片是否存在且格式正确
