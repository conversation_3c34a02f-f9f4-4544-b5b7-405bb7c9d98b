package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"MagneticOperator/app/services"
)

func main() {
	fmt.Println("=== OCR功能测试 ===")

	// 创建OCR服务
	ocrService := services.NewOCRService()
	defer ocrService.Close()

	// 验证OCR环境
	fmt.Println("1. 验证OCR环境...")
	if err := ocrService.ValidateOCREnvironment(); err != nil {
		log.Printf("OCR环境验证失败: %v", err)
		fmt.Println("请确保已正确安装Tesseract-OCR")
		fmt.Println("Windows安装指南:")
		fmt.Println("1. 下载Tesseract-OCR: https://github.com/UB-Mannheim/tesseract/wiki")
		fmt.Println("2. 安装时选择中文语言包")
		fmt.Println("3. 将安装目录添加到系统PATH环境变量")
		return
	}
	fmt.Println("✓ OCR环境验证成功")

	// 查找测试图片
	fmt.Println("\n2. 查找测试图片...")
	testImages := []string{}
	
	// 在pic目录中查找PNG文件
	picDir := "../pic"
	if _, err := os.Stat(picDir); err == nil {
		files, err := filepath.Glob(filepath.Join(picDir, "*.png"))
		if err == nil {
			testImages = append(testImages, files...)
		}
	}

	if len(testImages) == 0 {
		fmt.Println("未找到测试图片，请在pic目录中放置一些PNG格式的截图文件")
		return
	}

	fmt.Printf("找到 %d 个测试图片\n", len(testImages))

	// 测试前3个图片
	maxTests := 3
	if len(testImages) < maxTests {
		maxTests = len(testImages)
	}

	for i := 0; i < maxTests; i++ {
		imagePath := testImages[i]
		fmt.Printf("\n3.%d 测试图片: %s\n", i+1, filepath.Base(imagePath))
		
		// 处理图片
		result, err := ocrService.ProcessImageWithDetails(imagePath)
		if err != nil {
			log.Printf("处理图片失败: %v", err)
			continue
		}

		// 显示结果
		fmt.Printf("   识别文字长度: %d 字符\n", len(result.FullText))
		fmt.Printf("   器官名称: %s\n", result.OrganName)
		fmt.Printf("   置信度: %.2f\n", result.Confidence)
		fmt.Printf("   处理时间: %v\n", result.ProcessTime)
		
		// 显示部分识别文字（前200字符）
		if len(result.FullText) > 0 {
			displayText := result.FullText
			if len(displayText) > 200 {
				displayText = displayText[:200] + "..."
			}
			fmt.Printf("   识别文字预览: %s\n", displayText)
		}
	}

	fmt.Println("\n=== OCR测试完成 ===")
}
