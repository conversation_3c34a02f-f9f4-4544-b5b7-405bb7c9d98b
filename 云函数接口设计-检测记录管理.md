# 云函数接口设计 - 检测记录管理（MVP版本）

## 📋 基于现有云函数模式的接口设计

参考现有的 `getRegistrationsBySiteAndDevice` 云函数模式，设计以下接口：

## 1. 创建或获取检测会话

### 云函数：`hc-detection-records/createOrGetSession`

**请求参数：**
```javascript
{
  "user_id": "用户ID",
  "user_name": "张三",
  "site_id": "网点ID", 
  "device_no": "设备MAC地址（去除冒号）",
  "registration_id": "挂号记录ID",
  "operator_name": "操作员姓名"
}
```

**响应格式：**
```javascript
{
  "errCode": "0",
  "errMsg": "success",
  "data": {
    "session_id": "SES_张三_20240616",
    "current_round": 1,
    "total_rounds": 10,
    "session_status": "CREATED",
    "report_number": "RPT20240616001"
  }
}
```

## 2. 添加截图记录

### 云函数：`hc-detection-records/addScreenshot`

**请求参数：**
```javascript
{
  "session_id": "SES_张三_20240616",
  "round": 1,
  "analysis_type": "B02", // A01/B02/C03
  "detected_organ": "心脏",
  "filename": "张三_心脏_RPT20240616001_R01_B02.png",
  "cloud_url": "https://...",
  "operator_name": "操作员姓名",
  "ocr_success": true,
  "coze_workflow_called": false
}
```

**响应格式：**
```javascript
{
  "errCode": "0",
  "errMsg": "success", 
  "data": {
    "record_updated": true,
    "current_round": 1,
    "screenshots_count": 1
  }
}
```

## 3. 更新会话状态

### 云函数：`hc-detection-records/updateSession`

**请求参数：**
```javascript
{
  "session_id": "SES_张三_20240616",
  "current_round": 2,
  "session_status": "IN_PROGRESS", // CREATED/IN_PROGRESS/COMPLETED/CANCELLED
  "session_end_time": "2024-06-16 11:30:00" // 可选，完成时填写
}
```

## 4. 获取用户检测记录

### 云函数：`hc-detection-records/getSessionsByUser`

**请求参数：**
```javascript
{
  "user_id": "用户ID", // 可选
  "user_name": "张三", // 可选
  "site_id": "网点ID",
  "device_no": "设备MAC地址",
  "date": "2024-06-16", // 可选，默认今天
  "page": 1,
  "pageSize": 20
}
```

**响应格式：**
```javascript
{
  "errCode": "0",
  "errMsg": "success",
  "data": [
    {
      "session_id": "SES_张三_20240616",
      "user_name": "张三",
      "current_round": 3,
      "total_rounds": 10,
      "session_status": "IN_PROGRESS",
      "screenshots_count": 5,
      "created_time": "2024-06-16 10:00:00",
      "latest_screenshot_time": "2024-06-16 10:45:00"
    }
  ],
  "total": 1
}
```

## 5. 获取详细检测记录

### 云函数：`hc-detection-records/getSessionDetail`

**请求参数：**
```javascript
{
  "session_id": "SES_张三_20240616"
}
```

**响应格式：**
```javascript
{
  "errCode": "0",
  "errMsg": "success",
  "data": {
    "session_id": "SES_张三_20240616",
    "user_name": "张三",
    "current_round": 3,
    "total_rounds": 10,
    "session_status": "IN_PROGRESS",
    "screenshots": [
      {
        "round": 1,
        "analysis_type1": "B02",
        "analysis_type2": "C03", 
        "detected_organ": "心脏",
        "B02_filename": "张三_心脏_RPT20240616001_R01_B02.png",
        "C03_filename": "张三_心脏_RPT20240616001_R01_C03.png",
        "B02_screenshot_cloud_url": "https://...",
        "C03_screenshot_cloud_url": "https://...",
        "screenshot_time": "2024-06-16 10:30:00",
        "ocr_success": true,
        "coze_workflow_called": true
      }
    ]
  }
}
```

## 📝 本地代码集成示例

### 在 `app/services/api_service.go` 中添加：

```go
// CreateOrGetDetectionSession 创建或获取检测会话
func (as *APIService) CreateOrGetDetectionSession(userID, userName, siteID, deviceNo, registrationID, operatorName string) (string, error) {
    config := as.configService.GetConfig()
    if config == nil {
        return "", fmt.Errorf("配置未加载")
    }

    requestBody := map[string]interface{}{
        "user_id":         userID,
        "user_name":       userName,
        "site_id":         siteID,
        "device_no":       strings.ReplaceAll(deviceNo, ":", ""),
        "registration_id": registrationID,
        "operator_name":   operatorName,
    }

    // 调用云函数（参考现有的GetRegistrations方法）
    // 返回session_id
}

// AddScreenshotRecord 添加截图记录
func (as *APIService) AddScreenshotRecord(sessionID string, round int, analysisType, detectedOrgan, filename, cloudURL, operatorName string, ocrSuccess, cozeWorkflowCalled bool) error {
    // 实现逻辑...
}
```

### 在 `app.go` 中的 `ProcessScreenshotAndUpload` 中集成：

```go
func (a *App) ProcessScreenshotAndUpload(mode string, userName string) (string, error) {
    // 1. 创建或获取检测会话
    sessionID, err := a.apiService.CreateOrGetDetectionSession(
        currentUser.UserID, userName, a.config.SiteInfo.SiteID, 
        a.config.DeviceInfo.MACAddress, registrationID, "操作员")
    
    // 2. OCR识别 + 优化命名截图（已有逻辑）
    _, organName, _ := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
    filePath, err := a.screenshotService.TakeScreenshotWithOptimizedNaming(mode, userName, organName, currentUser)
    
    // 3. 上传到DCloud（已有逻辑）
    picURL, err := a.apiService.UploadImageToDCloud(filePath)
    
    // 4. 添加截图记录到数据库
    err = a.apiService.AddScreenshotRecord(sessionID, getCurrentRound(), mode, organName, 
        filepath.Base(filePath), picURL, "操作员", organName != "未知器官", false)
    
    // 5. 调用扣子工作流（已有逻辑）
    err = a.apiService.CallCozeWorkflow(picURL, filePath, reportID, userInput)
    
    // 6. 更新扣子工作流调用状态
    // 可以添加一个更新记录的调用
    
    return picURL, nil
}
```

## 🎯 MVP实施优势

1. **简单实用** - 一个表解决核心需求
2. **易于扩展** - 后续可以拆分为多个表
3. **兼容现有** - 不影响现有截图流程
4. **数据完整** - 包含了追溯所需的关键信息
5. **性能良好** - 避免了复杂的多表关联查询

这个MVP方案既满足了数据管理需求，又保持了系统的简洁性！
