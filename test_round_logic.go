package main

import (
	"fmt"
	"time"
)

// 简化的轮次管理器测试
type RoundManager struct {
	roundManager map[string]int
}

func NewRoundManager() *RoundManager {
	return &RoundManager{
		roundManager: make(map[string]int),
	}
}

// getCurrentRound 获取当前轮次
func (rm *RoundManager) getCurrentRound(userName string) int {
	// 生成用户的唯一标识（用户名+日期）
	today := time.Now().Format("20060102")
	userKey := fmt.Sprintf("%s_%s", userName, today)
	
	// 如果是新用户或新的一天，从第1轮开始
	if currentRound, exists := rm.roundManager[userKey]; exists {
		return currentRound
	} else {
		// 新用户，从第1轮开始
		rm.roundManager[userKey] = 1
		return 1
	}
}

// incrementRound 增加用户的轮次
func (rm *RoundManager) incrementRound(userName string) int {
	// 生成用户的唯一标识（用户名+日期）
	today := time.Now().Format("20060102")
	userKey := fmt.Sprintf("%s_%s", userName, today)
	
	// 获取当前轮次并增加1
	currentRound := rm.getCurrentRound(userName)
	newRound := currentRound + 1
	
	// 确保不超过最大轮次（10轮）
	if newRound > 10 {
		newRound = 10
	}
	
	rm.roundManager[userKey] = newRound
	return newRound
}

// generateFilename 生成文件名
func (rm *RoundManager) generateFilename(userName, organName, mode string, currentRound int) string {
	// 生成检测报告号：RPT + 日期 + 序号
	timestamp := time.Now()
	reportNumber := fmt.Sprintf("RPT%s001", timestamp.Format("20060102"))
	
	// 轮次
	roundNumber := fmt.Sprintf("R%02d", currentRound)
	
	// 列表类型
	var listType string
	switch mode {
	case "器官问题来源分析":
		listType = "A01"
	case "生化平衡分析":
		listType = "B02"
	case "病理形态学分析":
		listType = "C03"
	default:
		listType = "B02"
	}
	
	// 生成最终文件名
	filename := fmt.Sprintf("%s_%s_%s_%s_%s.png",
		userName, organName, reportNumber, roundNumber, listType)
	
	return filename
}

func main() {
	fmt.Println("=== 轮次管理逻辑测试 ===")
	
	rm := NewRoundManager()
	userName := "医生或健康专家"
	organName := "心脏"
	mode := "生化平衡分析"
	
	fmt.Printf("用户: %s\n", userName)
	fmt.Printf("器官: %s\n", organName)
	fmt.Printf("模式: %s\n", mode)
	fmt.Println()
	
	// 模拟10轮截图
	for i := 1; i <= 12; i++ {
		// 获取当前轮次
		currentRound := rm.getCurrentRound(userName)
		
		// 生成文件名
		filename := rm.generateFilename(userName, organName, mode, currentRound)
		
		fmt.Printf("第%d次截图:\n", i)
		fmt.Printf("  当前轮次: R%02d\n", currentRound)
		fmt.Printf("  文件名: %s\n", filename)
		
		// 截图完成后增加轮次
		nextRound := rm.incrementRound(userName)
		fmt.Printf("  下次轮次: R%02d\n", nextRound)
		fmt.Println()
	}
	
	fmt.Println("=== 测试不同用户 ===")
	
	userName2 := "张三"
	currentRound2 := rm.getCurrentRound(userName2)
	filename2 := rm.generateFilename(userName2, "肝脏", mode, currentRound2)
	
	fmt.Printf("新用户 %s 的第一次截图:\n", userName2)
	fmt.Printf("  当前轮次: R%02d\n", currentRound2)
	fmt.Printf("  文件名: %s\n", filename2)
}
