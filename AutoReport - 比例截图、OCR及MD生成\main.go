package main

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"syscall"
	"time"

	"github.com/kbinani/screenshot"
)

var (
	user32               = syscall.NewLazyDLL("user32.dll")
	procGetAsyncKeyState = user32.NewProc("GetAsyncKeyState")
	VK_CONTROL           = 0x11
	VK_SHIFT             = 0x10
	VK_X                 = 0x58
	tesseractPath        = `D:\Program Files\Tesseract-OCR\tesseract.exe`
)

// 用户信息结构
type UserInfo struct {
	Name string
}

// 当前用户信息（临时使用）
var currentUser = UserInfo{
	Name: "某用户",
}

// 模式信息结构
type ModeInfo struct {
	Code string
	Name string
}

// 模式配置
var modeConfig = map[string]ModeInfo{
	"A": {"A01", "器官问题来源分析"},
	"B": {"B02", "生化平衡分析"},
	"C": {"C03", "病理形态学分析"},
}

func getAsyncKeyState(key int) bool {
	ret, _, _ := procGetAsyncKeyState.Call(uintptr(key))
	return ret&0x8000 != 0
}

// 图像预处理函数
func preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()
	enhancedImg := image.NewRGBA(bounds)

	// 1. 提高对比度和亮度
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 将颜色值从uint32转换为float64进行处理
			rf := float64(r >> 8)
			gf := float64(g >> 8)
			bf := float64(b >> 8)

			// 提高对比度（增加20%）
			contrast := 1.2
			rf = ((rf - 128.0) * contrast) + 128.0
			gf = ((gf - 128.0) * contrast) + 128.0
			bf = ((bf - 128.0) * contrast) + 128.0

			// 确保值在0-255范围内
			rf = maxf(0.0, minf(255.0, rf))
			gf = maxf(0.0, minf(255.0, gf))
			bf = maxf(0.0, minf(255.0, bf))

			enhancedImg.Set(x, y, color.RGBA{
				R: uint8(rf),
				G: uint8(gf),
				B: uint8(bf),
				A: uint8(a >> 8),
			})
		}
	}

	// 2. 轻微锐化处理
	sharpenKernel := []float64{
		0, -0.5, 0,
		-0.5, 3, -0.5,
		0, -0.5, 0,
	}

	sharpenedImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64

			// 应用锐化核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := enhancedImg.At(x+kx, y+ky).RGBA()
					weight := sharpenKernel[(ky+1)*3+(kx+1)]
					sumR += float64(r>>8) * weight
					sumG += float64(g>>8) * weight
					sumB += float64(b>>8) * weight
				}
			}

			// 确保值在0-255范围内
			r := uint8(maxf(0.0, minf(255.0, sumR)))
			g := uint8(maxf(0.0, minf(255.0, sumG)))
			b := uint8(maxf(0.0, minf(255.0, sumB)))

			sharpenedImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 3. 轻微平滑处理（使用简单的3x3均值滤波）
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64
			count := 0.0

			// 3x3邻域平均
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := sharpenedImg.At(x+kx, y+ky).RGBA()
					sumR += float64(r >> 8)
					sumG += float64(g >> 8)
					sumB += float64(b >> 8)
					count++
				}
			}

			// 计算平均值
			r := uint8(maxf(0.0, minf(255.0, sumR/count)))
			g := uint8(maxf(0.0, minf(255.0, sumG/count)))
			b := uint8(maxf(0.0, minf(255.0, sumB/count)))

			finalImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return finalImg
}

// 辅助函数 - 处理float64
func minf(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

func maxf(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// 修改performOCR函数，在OCR之前进行预处理
func performOCR(imagePath string) (string, error) {
	// 检查 Tesseract 是否可用
	if _, err := os.Stat(tesseractPath); os.IsNotExist(err) {
		return "", fmt.Errorf("Tesseract OCR 未安装，请先安装 Tesseract OCR")
	}

	// 读取原始图像
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("读取图像失败: %v", err)
	}
	defer file.Close()

	// 解码PNG图像
	originalImg, err := png.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图像失败: %v", err)
	}

	// 预处理图像
	processedImg := preprocessImage(originalImg)

	// 保存预处理后的图像到临时文件
	tempImagePath := imagePath + "_processed.png"
	tempFile, err := os.Create(tempImagePath)
	if err != nil {
		return "", fmt.Errorf("创建临时文件失败: %v", err)
	}
	defer os.Remove(tempImagePath) // 清理临时文件
	defer tempFile.Close()

	// 保存处理后的图像
	if err := png.Encode(tempFile, processedImg); err != nil {
		return "", fmt.Errorf("保存预处理图像失败: %v", err)
	}

	// 生成输出文件路径（与图片同名，但扩展名为 .txt）
	outputPath := strings.TrimSuffix(imagePath, filepath.Ext(imagePath))

	// 执行 Tesseract 命令，使用预处理后的图像
	cmd := exec.Command(tesseractPath, tempImagePath, outputPath, "-l", "chi_sim+eng")
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("OCR识别失败: %v", err)
	}

	// 读取识别结果
	content, err := os.ReadFile(outputPath + ".txt")
	if err != nil {
		return "", fmt.Errorf("读取识别结果失败: %v", err)
	}

	// 删除临时文件
	os.Remove(outputPath + ".txt")

	return string(content), nil
}

func saveAsMarkdown(text, imagePath string) error {
	// 获取图片文件名（不含扩展名）
	baseName := strings.TrimSuffix(imagePath, filepath.Ext(imagePath))
	markdownPath := baseName + ".md"

	// 从文件名中提取模式信息
	fileName := filepath.Base(imagePath)
	parts := strings.Split(fileName, "-")
	if len(parts) >= 2 {
		modeCode := parts[1]
		modeInfo := modeConfig[modeCode[:2]] // 获取 A01, B02, C03 对应的模式信息

		// 创建markdown内容
		content := fmt.Sprintf(`# %s - %s

## 报告信息
- 生成时间：%s
- 用户：%s
- 分析类型：%s
- 原始图片：![截图](%s)

## 内容分析
%s

## 原始识别文本
%s
`,
			modeInfo.Code,
			modeInfo.Name,
			time.Now().Format("2006-01-02 15:04:05"),
			currentUser.Name,
			modeInfo.Name,
			fileName,
			text,
			"```\n"+text+"\n```")

		// 保存markdown文件
		return os.WriteFile(markdownPath, []byte(content), 0644)
	}

	// 如果文件名格式不正确，使用默认格式
	content := fmt.Sprintf(`# 截图内容识别

## 报告信息
- 生成时间：%s
- 原始图片：![截图](%s)

## 内容分析
%s

## 原始识别文本
%s
`,
		time.Now().Format("2006-01-02 15:04:05"),
		fileName,
		text,
		"```\n"+text+"\n```")

	return os.WriteFile(markdownPath, []byte(content), 0644)
}

func filterTextByMode(text string, mode string) string {
	if mode != "C" {
		return text
	}

	// 模式C：只保留包含数字的行
	lines := strings.Split(text, "\n")
	var filteredLines []string

	for _, line := range lines {
		// 检查行是否包含数字
		if matched, _ := regexp.MatchString(`\d`, line); matched {
			filteredLines = append(filteredLines, line)
		}
	}

	return strings.Join(filteredLines, "\n")
}

func generateFilename(mode string) string {
	modeInfo := modeConfig[mode]
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s-%s-%s", currentUser.Name, modeInfo.Code, timestamp)
}

// Calculates cropping bounds based on percentages
func calculateCropBounds(imgBounds image.Rectangle, topPercent, bottomPercent, leftPercent, rightPercent float64) image.Rectangle {
	width := float64(imgBounds.Dx())
	height := float64(imgBounds.Dy())

	minX := int(width * leftPercent)
	maxX := int(width * (1.0 - rightPercent))
	minY := int(height * topPercent)
	maxY := int(height * (1.0 - bottomPercent))

	// Ensure bounds are within the original image
	minX = max(minX, imgBounds.Min.X)
	minY = max(minY, imgBounds.Min.Y)
	maxX = min(maxX, imgBounds.Max.X)
	maxY = min(maxY, imgBounds.Max.Y)

	return image.Rect(minX, minY, maxX, maxY)
}

// Performs the actual cropping based on calculated bounds
func performCrop(img image.Image, cropBounds image.Rectangle) image.Image {
	// Check if cropBounds is valid
	if cropBounds.Dx() <= 0 || cropBounds.Dy() <= 0 {
		fmt.Println("警告: 计算出的裁剪边界无效，返回原始图片副本。")
		bounds := img.Bounds()
		newImg := image.NewRGBA(bounds)
		for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
			for x := bounds.Min.X; x < bounds.Max.X; x++ {
				newImg.Set(x, y, img.At(x, y))
			}
		}
		return newImg
	}

	// Create a new RGBA image for the cropped area
	croppedImg := image.NewRGBA(image.Rect(0, 0, cropBounds.Dx(), cropBounds.Dy()))

	// Manual copy to be sure
	for y := 0; y < cropBounds.Dy(); y++ {
		for x := 0; x < cropBounds.Dx(); x++ {
			// Calculate corresponding pixel in the original image
			originalX := cropBounds.Min.X + x
			originalY := cropBounds.Min.Y + y
			// Get the color from the original image
			c := img.At(originalX, originalY)
			// Set the color in the new cropped image
			croppedImg.Set(x, y, c)
		}
	}

	return croppedImg
}

func saveImage(img image.Image, filepath string) error {
	file, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	encoder := png.Encoder{
		CompressionLevel: png.BestCompression,
	}

	if err := encoder.Encode(file, img); err != nil {
		return fmt.Errorf("保存图片失败: %v", err)
	}
	return nil
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	fmt.Println("截图程序已启动")
	fmt.Println("当前用户:", currentUser.Name)
	fmt.Println("可用模式：")
	// 模式A: A01 # 器官问题来源分析
	fmt.Println("Ctrl + Shift + A: 器官问题来源分析")

	// 模式B: B02 # 生化平衡分析
	fmt.Println("Ctrl + Shift + B: 生化平衡分析")

	// 模式C: C03 # 病理形态学分析
	fmt.Println("Ctrl + Shift + C: 病理形态学分析（仅保留数字行）")

	fmt.Println("\n按 Ctrl + X 终止程序")

	// 创建pic目录（如果不存在）
	if err := os.MkdirAll("pic", 0755); err != nil {
		fmt.Printf("创建目录失败: %v\n", err)
		return
	}

	// 添加冷却时间，防止重复触发
	lastScreenshot := time.Now().Add(-1 * time.Second)
	cooldown := 500 * time.Millisecond

	for {
		// 检查终止程序的快捷键
		if getAsyncKeyState(VK_CONTROL) && getAsyncKeyState(VK_X) {
			fmt.Println("\n检测到 Ctrl + X，程序即将退出...")
			time.Sleep(500 * time.Millisecond) // 给一点时间显示退出消息
			return
		}

		// 检查组合键是否按下
		ctrlPressed := getAsyncKeyState(VK_CONTROL)
		shiftPressed := getAsyncKeyState(VK_SHIFT)
		aPressed := getAsyncKeyState(0x41) // A键
		bPressed := getAsyncKeyState(0x42) // B键
		cPressed := getAsyncKeyState(0x43) // C键

		// 检查截图快捷键和模式选择
		if ctrlPressed && shiftPressed {
			var mode string
			if aPressed {
				mode = "A"
			} else if bPressed {
				mode = "B"
			} else if cPressed {
				mode = "C"
			}

			if mode != "" {
				// 检查是否在冷却时间内
				if time.Since(lastScreenshot) < cooldown {
					time.Sleep(50 * time.Millisecond)
					continue
				}

				fmt.Printf("正在使用模式%s进行截图...\n", mode)

				// 获取屏幕数量
				n := screenshot.NumActiveDisplays()
				if n <= 0 {
					fmt.Println("未检测到显示器")
					continue
				}

				// 捕获主屏幕
				bounds := screenshot.GetDisplayBounds(0)
				fmt.Printf("正在捕获屏幕区域: %v\n", bounds)

				img, err := screenshot.CaptureRect(bounds)
				if err != nil {
					fmt.Printf("截图失败: %v\n", err)
					continue
				}

				// --- 二次裁剪：基于百分比 ---
				topPercent := 0.153    // 裁剪顶部 15.3%
				bottomPercent := 0.051 // 裁剪底部 5.1%
				leftPercent := 0.0482  // 裁剪左侧 4.82%
				rightPercent := 0.30   // 裁剪右侧 30%

				cropBounds := calculateCropBounds(img.Bounds(), topPercent, bottomPercent, leftPercent, rightPercent)
				fmt.Printf("应用百分比裁剪，新区域: %v\n", cropBounds)
				croppedImg := performCrop(img, cropBounds)

				// 生成文件名并保存图片
				filename := generateFilename(mode)
				filepath := filepath.Join("pic", filename+".png")

				// 保存裁剪后的图片
				if err := saveImage(croppedImg, filepath); err != nil {
					fmt.Printf("保存图片失败: %v\n", err)
					continue
				}

				// 获取文件大小
				fileInfo, err := os.Stat(filepath)
				if err != nil {
					fmt.Printf("获取文件信息失败: %v\n", err)
				} else {
					fmt.Printf("截图已保存: %s (大小: %d 字节)\n", filepath, fileInfo.Size())
				}

				// 进行OCR识别
				text, err := performOCR(filepath)
				if err != nil {
					fmt.Printf("OCR识别失败: %v\n", err)
					continue
				}

				// 根据当前模式过滤文本（仅保留模式C的数字过滤功能）
				filteredText := filterTextByMode(text, mode)

				// 保存为markdown文件
				if err := saveAsMarkdown(filteredText, filepath); err != nil {
					fmt.Printf("保存markdown文件失败: %v\n", err)
					continue
				}

				fmt.Printf("已生成markdown文件: %s.md\n", strings.TrimSuffix(filepath, ".png"))

				lastScreenshot = time.Now()
			}
		}

		// 短暂延迟，避免CPU占用过高
		time.Sleep(50 * time.Millisecond)
	}
}
