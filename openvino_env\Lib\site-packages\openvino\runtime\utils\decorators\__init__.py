# -*- coding: utf-8 -*-
# Copyright (C) 2018-2025 Intel Corporation
# SPDX-License-Identifier: Apache-2.0

from openvino.utils.decorators import _get_name
from openvino.utils.decorators import _set_node_friendly_name
from openvino.utils.decorators import nameable_op
from openvino.utils.decorators import unary_op
from openvino.utils.decorators import binary_op
from openvino.utils.decorators import custom_preprocess_function
from openvino.utils.decorators import MultiMethod
from openvino.utils.decorators import registry
from openvino.utils.decorators import overloading
