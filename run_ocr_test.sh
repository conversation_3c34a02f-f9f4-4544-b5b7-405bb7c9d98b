#!/bin/bash

echo "=== OCR器官名称提取测试脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 检查是否有测试图片
if [ ! -f "test_image.png" ]; then
    echo "警告: 未找到 test_image.png"
    echo "请将测试图片保存为 test_image.png 并放在当前目录"
    echo "程序将使用屏幕截图进行测试"
fi

# 创建必要的目录
mkdir -p pic

# 安装依赖（如果需要）
echo "检查并安装依赖..."
go mod tidy

# 运行测试
echo "开始运行OCR测试..."
go run test_ocr_organ_extraction.go

echo "测试完成！"
echo "请查看 pic/ 目录下的裁剪图片和识别结果"
